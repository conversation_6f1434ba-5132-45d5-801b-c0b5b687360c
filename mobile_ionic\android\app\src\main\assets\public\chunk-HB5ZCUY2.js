import{b as W}from"./chunk-KAKQC7EG.js";import"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{$a as R,B as x,C as m,Cb as V,E as e,F as t,G as c,H as E,I as y,J as h,L as n,La as I,M as f,Ma as N,N as b,Na as O,Oa as F,Pa as M,Qa as A,Ra as P,Sa as D,U as T,Va as L,W as S,_a as B,bb as K,da as k,eb as $,fb as j,na as _,p as v,q as C,tb as q,ub as H,x as s,y as p,yb as G,z as w,zb as U}from"./chunk-6UWMO7JM.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as u}from"./chunk-LNJ3S2LQ.js";function Y(g,d){if(g&1){let l=E();e(0,"ion-item",14),c(1,"ion-icon",15),e(2,"ion-label")(3,"h2"),n(4),t(),e(5,"p"),n(6),t(),e(7,"p",16)(8,"ion-badge",17),n(9),t(),e(10,"ion-badge",17),n(11),t()()(),e(12,"ion-buttons",18)(13,"ion-button",19),y("click",function(){let a=v(l).$implicit,o=h();return C(o.testForegroundNotification(a))}),c(14,"ion-icon",20),t()()()}if(g&2){let l=d.$implicit,i=h();s(),m("name",i.getDisasterIcon(l.category))("color",i.getDisasterColor(l.category)),s(3),f(l.title),s(2),f(l.body),s(2),m("color",i.getDisasterColor(l.category)),s(),b(" ",l.category.toUpperCase()," "),s(),m("color",l.severity==="high"?"danger":"warning"),s(),b(" ",l.severity.toUpperCase()," "),s(2),m("color",i.getDisasterColor(l.category))}}var ae=(()=>{let d=class d{constructor(i,a,o,r){this.fcmService=i,this.alertController=a,this.loadingController=o,this.router=r,this.testNotifications=[{title:"EARTHQUAKE ALERT",body:"Magnitude 7.2 earthquake detected. Evacuate to nearest safe area immediately.",category:"earthquake",severity:"high"},{title:"FLOOD WARNING",body:"Flash flood warning in your area. Move to higher ground immediately.",category:"flood",severity:"high"},{title:"TYPHOON ALERT",body:"Typhoon approaching. Seek shelter in a sturdy building.",category:"typhoon",severity:"medium"},{title:"FIRE EMERGENCY",body:"Fire reported in your vicinity. Evacuate the area immediately.",category:"fire",severity:"high"},{title:"GENERAL ALERT",body:"Emergency situation detected. Follow local authorities instructions.",category:"general",severity:"medium"}]}testForegroundNotification(i){return u(this,null,function*(){let a=yield this.loadingController.create({message:"Testing foreground notification...",duration:3e3});yield a.present();try{let o={title:i.title,body:i.body,category:i.category,severity:i.severity,wasTapped:!1,data:{category:i.category,severity:i.severity}};yield this.fcmService.simulateForegroundNotification(o),yield a.dismiss(),yield(yield this.alertController.create({header:"Test Complete",message:"Foreground notification test completed. Check if the emergency modal appeared.",buttons:["OK"]})).present()}catch(o){yield a.dismiss(),yield(yield this.alertController.create({header:"Test Failed",message:`Error testing notification: ${o}`,buttons:["OK"]})).present()}})}testBackgroundNotification(i){return u(this,null,function*(){let a=yield this.loadingController.create({message:"Testing background notification...",duration:3e3});yield a.present();try{let o={title:i.title,body:i.body,category:i.category,severity:i.severity,wasTapped:!0,data:{category:i.category,severity:i.severity}};yield this.fcmService.simulateBackgroundNotification(o),yield a.dismiss(),yield(yield this.alertController.create({header:"Test Complete",message:"Background notification test completed. Check if the emergency modal appeared.",buttons:["OK"]})).present()}catch(o){yield a.dismiss(),yield(yield this.alertController.create({header:"Test Failed",message:`Error testing notification: ${o}`,buttons:["OK"]})).present()}})}testAllNotifications(){return u(this,null,function*(){let i=yield this.loadingController.create({message:"Testing all notification types...",duration:15e3});yield i.present();try{for(let o=0;o<this.testNotifications.length;o++){let r=this.testNotifications[o];o>0&&(yield new Promise(Q=>setTimeout(Q,3e3)));let z={title:r.title,body:r.body,category:r.category,severity:r.severity,wasTapped:!1,data:{category:r.category,severity:r.severity}};yield this.fcmService.simulateForegroundNotification(z)}yield i.dismiss(),yield(yield this.alertController.create({header:"All Tests Complete",message:"All notification types have been tested. Check if emergency modals appeared for each.",buttons:["OK"]})).present()}catch(a){yield i.dismiss(),yield(yield this.alertController.create({header:"Test Failed",message:`Error during batch testing: ${a}`,buttons:["OK"]})).present()}})}getDisasterIcon(i){switch(i.toLowerCase()){case"earthquake":return"warning-outline";case"flood":return"water-outline";case"typhoon":return"cloudy-outline";case"fire":return"flame-outline";default:return"notifications-outline"}}getDisasterColor(i){switch(i.toLowerCase()){case"earthquake":return"warning";case"flood":return"primary";case"typhoon":return"success";case"fire":return"danger";default:return"medium"}}goBack(){this.router.navigate(["/tabs/profile"])}};d.\u0275fac=function(a){return new(a||d)(p(W),p(G),p(U),p(k))},d.\u0275cmp=w({type:d,selectors:[["app-notification-test"]],decls:94,vars:3,consts:[[3,"translucent"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[1,"ion-padding",3,"fullscreen"],[1,"test-container"],["expand","block","color","danger",3,"click"],["name","flash-outline","slot","start"],["class","notification-item",4,"ngFor","ngForOf"],["name","phone-portrait-outline","slot","start","color","primary"],["name","moon-outline","slot","start","color","secondary"],["name","checkmark-circle-outline","slot","start","color","success"],["name","close-circle-outline","slot","start","color","danger"],["name","warning-outline","slot","start","color","warning"],[1,"notification-item"],["slot","start",3,"name","color"],[1,"category-badge"],[3,"color"],["slot","end"],["fill","clear",3,"click","color"],["name","play-outline"]],template:function(a,o){a&1&&(e(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),y("click",function(){return o.goBack()}),c(4,"ion-icon",3),t()(),e(5,"ion-title"),n(6,"Emergency Notification Test"),t()()(),e(7,"ion-content",4)(8,"div",5)(9,"ion-card")(10,"ion-card-header")(11,"ion-card-title"),n(12,"\u{1F6A8} Emergency Notification Testing"),t(),e(13,"ion-card-subtitle"),n(14,"Test emergency modals and notifications"),t()(),e(15,"ion-card-content")(16,"p"),n(17,"This page allows you to test emergency notifications to ensure they work properly when the app is active."),t(),e(18,"p")(19,"strong"),n(20,"What to expect:"),t()(),e(21,"ul")(22,"li"),n(23,"Emergency modal should appear immediately"),t(),e(24,"li"),n(25,"Device should vibrate"),t(),e(26,"li"),n(27,"Modal should have disaster-specific colors"),t(),e(28,"li"),n(29,"App should NOT crash or close"),t()()()(),e(30,"ion-card")(31,"ion-card-header")(32,"ion-card-title"),n(33,"Quick Test"),t()(),e(34,"ion-card-content")(35,"ion-button",6),y("click",function(){return o.testAllNotifications()}),c(36,"ion-icon",7),n(37," Test All Notification Types "),t()()(),e(38,"ion-card")(39,"ion-card-header")(40,"ion-card-title"),n(41,"Individual Tests"),t(),e(42,"ion-card-subtitle"),n(43,"Test specific disaster types"),t()(),e(44,"ion-card-content")(45,"ion-list"),x(46,Y,15,9,"ion-item",8),t()()(),e(47,"ion-card")(48,"ion-card-header")(49,"ion-card-title"),n(50,"Test Types"),t()(),e(51,"ion-card-content")(52,"ion-list")(53,"ion-item"),c(54,"ion-icon",9),e(55,"ion-label")(56,"h3"),n(57,"Foreground Test"),t(),e(58,"p"),n(59,"Tests notifications when app is active and visible"),t()()(),e(60,"ion-item"),c(61,"ion-icon",10),e(62,"ion-label")(63,"h3"),n(64,"Background Test"),t(),e(65,"p"),n(66,"Simulates notifications when app was in background"),t()()()()()(),e(67,"ion-card")(68,"ion-card-header")(69,"ion-card-title"),n(70,"Troubleshooting"),t()(),e(71,"ion-card-content")(72,"ion-list")(73,"ion-item"),c(74,"ion-icon",11),e(75,"ion-label")(76,"h3"),n(77,"\u2705 Working Correctly"),t(),e(78,"p"),n(79,"Modal appears, device vibrates, app stays open"),t()()(),e(80,"ion-item"),c(81,"ion-icon",12),e(82,"ion-label")(83,"h3"),n(84,"\u274C App Crashes"),t(),e(85,"p"),n(86,"Check console logs, modal creation might be failing"),t()()(),e(87,"ion-item"),c(88,"ion-icon",13),e(89,"ion-label")(90,"h3"),n(91,"\u26A0\uFE0F No Modal"),t(),e(92,"p"),n(93,"Check if fallback toast appears instead"),t()()()()()()()()),a&2&&(m("translucent",!0),s(7),m("fullscreen",!0),s(39),m("ngForOf",o.testNotifications))},dependencies:[V,I,N,O,F,M,A,P,D,L,B,R,K,$,j,q,H,S,T,_],styles:[".test-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}.notification-item[_ngcontent-%COMP%]{margin-bottom:8px;border-radius:8px;--background: var(--ion-color-light)}.category-badge[_ngcontent-%COMP%]{margin-top:8px;display:flex;gap:8px}ion-badge[_ngcontent-%COMP%]{font-size:.7rem;padding:4px 8px}ion-card[_ngcontent-%COMP%]{margin-bottom:16px}ion-card-title[_ngcontent-%COMP%]{color:var(--ion-color-primary)}.test-button[_ngcontent-%COMP%]{margin:8px 0}ion-list[_ngcontent-%COMP%]{background:transparent}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--padding-end: 16px;margin-bottom:4px}h3[_ngcontent-%COMP%]{margin:0 0 4px;font-weight:600}p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:.9rem}"]});let g=d;return g})();export{ae as NotificationTestPage};
