@echo off
echo ========================================
echo    ALERTO - Deploy to Mobile Device
echo ========================================
echo.

echo Step 1: Building the app...
call npm run build
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Copying files to Android...
call npx cap copy android
if %errorlevel% neq 0 (
    echo ERROR: Copy failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Syncing Capacitor...
call npx cap sync android
if %errorlevel% neq 0 (
    echo ERROR: Sync failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Choose deployment method:
echo 1. Open Android Studio (manual build)
echo 2. Direct USB deployment (phone connected)
echo 3. Live reload development server
echo ========================================
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo Opening Android Studio...
    call npx cap open android
) else if "%choice%"=="2" (
    echo Deploying to connected device...
    call npx cap run android
) else if "%choice%"=="3" (
    echo Starting development server...
    echo Make sure your phone and computer are on the same network
    echo Your computer IP should be accessible from your phone
    call ionic serve --external
) else (
    echo Invalid choice!
    pause
    exit /b 1
)

echo.
echo Deployment process completed!
pause
