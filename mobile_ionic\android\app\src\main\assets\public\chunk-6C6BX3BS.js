import{a as q}from"./chunk-6Z4VSBPG.js";import"./chunk-L5U4Z35Y.js";import{a as U,b as B}from"./chunk-KAKQC7EG.js";import{a as g}from"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{$a as z,Ca as x,Cb as j,E as o,F as r,G as d,I as p,Ia as F,L as l,Ma as L,P as k,Pa as E,Q as _,R as C,Va as A,_ as w,ab as R,bb as N,da as b,eb as D,ga as P,ha as T,ia as v,ja as y,ka as S,la as O,na as I,x as u,y as c,yb as W,z as M}from"./chunk-6UWMO7JM.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as a}from"./chunk-LNJ3S2LQ.js";var ne=(()=>{let m=class m{goToRegister(){this.router.navigate(["/register"])}openNetworkDiagnostics(){this.router.navigate(["/network-diagnostics"])}openEnvironmentSwitcher(){this.router.navigate(["/environment-switcher"])}constructor(e,n,t,i,s,f,V){this.router=e,this.authService=n,this.fcm=t,this.http=i,this.alertController=s,this.platform=f,this.fcmService=V,this.credentials={email:"",password:""},this.errorMessage="",this.fcmToken="",this.fcmTokenReady=!1}ngOnInit(){return a(this,null,function*(){console.log("\u{1F525} Login page initializing..."),yield this.initializeFCM()})}initializeFCM(){return a(this,null,function*(){try{console.log("\u{1F525} Initializing FCM for login..."),yield this.fcmService.initPush(),yield this.getFCMToken(),console.log("\u2705 FCM initialization complete, token ready:",!!this.fcmToken),this.fcmTokenReady=!0}catch(e){console.error("\u274C FCM initialization failed:",e),this.fcmTokenReady=!1}})}getFCMToken(){return a(this,null,function*(){try{if(!this.platform.is("cordova")&&!this.platform.is("capacitor")){console.log("Running in browser, using mock FCM token"),this.fcmToken="browser-mock-token-"+Math.random().toString(36).substring(2,15),console.log("Mock FCM Token:",this.fcmToken);return}console.log("Getting FCM token from service..."),this.fcmToken=yield this.fcmService.getToken(),console.log("\u2705 FCM Token obtained:",this.fcmToken.substring(0,20)+"...")}catch(e){console.error("\u274C Error getting FCM token from service:",e);try{console.log("Trying direct FCM plugin as fallback..."),this.fcmToken=yield this.fcm.getToken(),console.log("\u2705 FCM Token from direct plugin:",this.fcmToken.substring(0,20)+"...")}catch(n){console.error("\u274C All FCM token methods failed:",n),this.fcmToken=""}}})}registerTokenWithEndpoint(e,n,t,i){if(localStorage.getItem("fcm_token")===this.fcmToken){console.log("Token already registered, skipping registration"),t&&t();return}if(localStorage.getItem("fcm_token_registering")==="true"){console.log("Token registration already in progress, skipping"),t&&t();return}localStorage.setItem("fcm_token_registering","true"),this.http.post(e,n).subscribe({next:f=>{console.log(`FCM token registered with ${e}:`,f),localStorage.setItem("fcm_token",this.fcmToken),localStorage.removeItem("fcm_token_registering"),t&&t()},error:f=>{console.error(`Error registering token with ${e}:`,f),localStorage.removeItem("fcm_token_registering"),i&&i()}})}onLogin(){return a(this,null,function*(){if(!this.credentials.email||!this.credentials.password){yield this.presentAlert("Login Failed","Please enter both email and password.");return}if(console.log("\u{1F510} Login attempt started"),console.log("\u{1F4E7} Email:",this.credentials.email),console.log("\u{1F310} API URL:",g.apiUrl),console.log("\u{1F4F1} Platform:",this.platform.is("android")?"Android":this.platform.is("ios")?"iOS":"Browser"),console.log("\u{1F30D} Network status:",navigator.onLine?"Online":"Offline"),console.log("\u{1F525} FCM Token ready:",this.fcmTokenReady,"Token:",this.fcmToken?this.fcmToken.substring(0,20)+"...":"None"),!this.fcmTokenReady&&!this.fcmToken){console.log("\u{1F525} FCM token not ready, attempting to get it now...");try{yield this.getFCMToken()}catch(e){console.warn("\u26A0\uFE0F Could not get FCM token, continuing without it:",e)}}console.log("\u{1F9EA} Testing API connectivity...");try{let e=yield this.http.get(`${g.apiUrl.replace("/api","")}/api/test`).toPromise();console.log("\u2705 API test successful:",e)}catch(e){console.error("\u274C API test failed:",e),yield this.presentAlert("Connection Error",`Cannot connect to the server. Please check:
\u2022 Your internet connection
\u2022 If you're on the same WiFi network
\u2022 If the backend server is running

Server: ${g.apiUrl}`);return}this.authService.login(this.credentials).subscribe({next:e=>a(this,null,function*(){if(console.log("\u2705 Login successful:",e),yield this.presentSuccessAlert("Login Successful","Welcome, "+e.user.full_name),this.authService.setToken(e.token),this.fcmToken){console.log("Registering FCM token with backend:",this.fcmToken);let n={token:this.fcmToken,device_type:this.platform.is("ios")?"ios":"android",project_id:g.firebase.projectId};e.user&&e.user.id&&(n.user_id=e.user.id),console.log("Token registration payload:",n),console.log("API URL:",`${g.apiUrl}/device-token`),this.fcmService.registerTokenWithBackend(this.fcmToken,e.user.id),this.router.navigate(["/welcome"])}else console.warn("No FCM token available to register"),this.router.navigate(["/welcome"])}),error:e=>{var n;console.error("\u274C Login error:",e),console.error("\u{1F4CA} Error details:",{status:e.status,statusText:e.statusText,message:e.message,url:e.url,error:e.error}),this.errorMessage=((n=e.error)==null?void 0:n.message)||"Login failed",e.status===0?this.presentAlert("Network Error",`Cannot connect to the server. Please check:
\u2022 Your internet connection
\u2022 If you're on the same WiFi network as the server
\u2022 If the backend server is running

Server URL: ${g.apiUrl}`):e.status===401?this.presentAlert("Login Failed","Invalid email or password. Please try again."):e.status===404?this.presentAlert("Server Error","Login endpoint not found. Please check server configuration."):e.status>=500?this.presentAlert("Server Error","The server encountered an error. Please try again later."):this.presentAlert("Login Error",`An error occurred during login (${e.status}).

Please check the console for more details or try again later.`)}})})}presentAlert(e,n){return a(this,null,function*(){yield(yield this.alertController.create({header:e,message:n,buttons:["OK"],cssClass:"login-alert"})).present()})}presentSuccessAlert(e,n){return a(this,null,function*(){yield(yield this.alertController.create({header:e,message:n,buttons:["OK"],cssClass:"login-success-alert"})).present()})}};m.\u0275fac=function(n){return new(n||m)(c(b),c(q),c(U),c(w),c(W),c(x),c(B))},m.\u0275cmp=M({type:m,selectors:[["app-login"]],decls:36,vars:2,consts:[[1,"ion-padding"],[1,"login-container"],[1,"login-wrapper"],["src","assets/ALERTO.png","alt","App Logo",1,"login-logo"],[1,"login-title"],[1,"login-form",3,"ngSubmit"],["position","floating"],["type","email","name","email","required","",3,"ngModelChange","ngModel"],["type","password","name","password","required","",3,"ngModelChange","ngModel"],["expand","block","type","submit",1,"login-btn"],[1,"troubleshooting-section"],["expand","block","fill","outline","color","warning",3,"click"],["name","settings-outline","slot","start"],["expand","block","fill","clear","size","small",3,"click"],["name","bug-outline","slot","start"],["expand","block","fill","outline","color","secondary",3,"click"],[1,"login-link"],[3,"click"]],template:function(n,t){n&1&&(o(0,"ion-content",0)(1,"div",1)(2,"ion-card-content")(3,"div",2),d(4,"img",3),o(5,"h1",4),l(6,"Log In Here!"),r(),d(7,"p"),o(8,"form",5),p("ngSubmit",function(){return t.onLogin()}),o(9,"ion-item")(10,"ion-label",6),l(11,"Email:"),r(),o(12,"ion-input",7),C("ngModelChange",function(s){return _(t.credentials.email,s)||(t.credentials.email=s),s}),r()(),o(13,"ion-item")(14,"ion-label",6),l(15,"Password:"),r(),o(16,"ion-input",8),C("ngModelChange",function(s){return _(t.credentials.password,s)||(t.credentials.password=s),s}),r()(),d(17,"br")(18,"br"),o(19,"ion-button",9),l(20,"Log In"),r()(),o(21,"div",10)(22,"ion-button",11),p("click",function(){return t.openEnvironmentSwitcher()}),d(23,"ion-icon",12),l(24," Switch API Endpoint "),r(),o(25,"ion-button",13),p("click",function(){return t.openNetworkDiagnostics()}),d(26,"ion-icon",14),l(27," Network Diagnostics "),r()(),o(28,"ion-button",15),p("click",function(){return t.openNetworkDiagnostics()}),l(29," \u{1F527} Network Diagnostics "),r(),o(30,"div",16),l(31," Don't have an account? "),o(32,"a",17),p("click",function(){return t.goToRegister()}),o(33,"strong")(34,"u"),l(35,"Sign Up"),r()()()()()()()()),n&2&&(u(12),k("ngModel",t.credentials.email),u(4),k("ngModel",t.credentials.password))},dependencies:[j,L,E,A,z,R,N,D,F,I,S,P,T,O,y,v],styles:[".login-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:80vh}.login-wrapper[_ngcontent-%COMP%]{width:100%;max-width:420px;padding:32px 28px;margin:0 auto;display:flex;flex-direction:column;align-items:center}.login-logo[_ngcontent-%COMP%]{width:300px;height:300px}.login-title[_ngcontent-%COMP%]{font-size:2.2rem;font-weight:700}.login-desc[_ngcontent-%COMP%]{font-size:1.1rem;color:#888}.login-form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{font-size:1.1rem;border-radius:16px}.login-btn[_ngcontent-%COMP%]{--border-radius: 25px;font-size:1.2rem;height:48px}.login-link[_ngcontent-%COMP%]{margin-top:20px;font-size:1.1rem}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:700}.troubleshooting-section[_ngcontent-%COMP%]{margin-top:24px;padding-top:16px;border-top:1px solid var(--ion-color-light)}.troubleshooting-section[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-bottom:8px}.login-alert[_ngcontent-%COMP%]{--backdrop-opacity: .8}.login-alert[_ngcontent-%COMP%]   .alert-wrapper[_ngcontent-%COMP%]{border-radius:15px;box-shadow:0 4px 16px #0003}.login-alert[_ngcontent-%COMP%]   .alert-head[_ngcontent-%COMP%]{padding-bottom:10px}.login-alert[_ngcontent-%COMP%]   .alert-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;color:#d9534f}.login-alert[_ngcontent-%COMP%]   .alert-message[_ngcontent-%COMP%]{font-size:1rem;color:#333}.login-alert[_ngcontent-%COMP%]   .alert-button[_ngcontent-%COMP%]{color:#3880ff;font-weight:500}.login-success-alert[_ngcontent-%COMP%]{--backdrop-opacity: .8}.login-success-alert[_ngcontent-%COMP%]   .alert-wrapper[_ngcontent-%COMP%]{border-radius:15px;box-shadow:0 4px 16px #0003}.login-success-alert[_ngcontent-%COMP%]   .alert-head[_ngcontent-%COMP%]{padding-bottom:10px}.login-success-alert[_ngcontent-%COMP%]   .alert-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;color:#5cb85c}.login-success-alert[_ngcontent-%COMP%]   .alert-message[_ngcontent-%COMP%]{font-size:1rem;color:#333}.login-success-alert[_ngcontent-%COMP%]   .alert-button[_ngcontent-%COMP%]{color:#3880ff;font-weight:500}"]});let h=m;return h})();export{ne as LoginPage};
