import{a as g}from"./chunk-FULEFYAM.js";import{$ as f,g as u,m as p,o as w}from"./chunk-YFIZFQXH.js";import{h as l}from"./chunk-LNJ3S2LQ.js";var M=(()=>{let n=class n{constructor(e){this.http=e,this.baseUrl="https://api.mapbox.com/directions/v5/mapbox",this.accessToken=g.mapboxAccessToken}getDirections(I,P,N,j){return l(this,arguments,function*(e,r,i,a,d="walking",o={}){if([e,r,i,a].some(t=>typeof t!="number"||isNaN(t)))throw new Error("Invalid coordinates provided");if(Math.abs(r)>90||Math.abs(a)>90)throw new Error("Latitude values must be between -90 and 90");if(Math.abs(e)>180||Math.abs(i)>180)throw new Error("Longitude values must be between -180 and 180");let m=`${e},${r};${i},${a}`,v=`${this.baseUrl}/${d}/${m}`,c=new URLSearchParams({access_token:this.accessToken,geometries:o.geometries||"geojson",overview:o.overview||"full",steps:(o.steps!==!1).toString(),alternatives:(o.alternatives||!1).toString()});o.continue_straight!==void 0&&c.append("continue_straight",o.continue_straight.toString()),o.waypoint_snapping&&o.waypoint_snapping.length>0&&c.append("waypoint_snapping",o.waypoint_snapping.join(";"));let h=`${v}?${c.toString()}`;console.log("Mapbox Directions API request:",{url:h.replace(this.accessToken,"TOKEN_HIDDEN"),profile:d,coordinates:{startLng:e,startLat:r,endLng:i,endLat:a}});try{let t=yield u(this.http.get(h,{headers:{"Content-Type":"application/json"}}));if(t.code!=="Ok")throw new Error(`Mapbox API error: ${t.code}`);if(!t.routes||t.routes.length===0)throw new Error("No routes found");return console.log("Mapbox Directions API response:",{routeCount:t.routes.length,distance:t.routes[0].distance,duration:t.routes[0].duration}),t}catch(t){throw console.error("Mapbox Directions API error:",t),t.status===401?new Error("Invalid Mapbox access token. Please check your token."):t.status===422?new Error("Invalid request parameters. Please check coordinates."):t.status===429?new Error("Rate limit exceeded. Please try again later."):t.status===0?new Error("Network error. Please check your internet connection."):t}})}convertTravelModeToProfile(e){switch(e){case"foot-walking":case"walking":return"walking";case"cycling-regular":case"cycling":return"cycling";case"driving-car":case"driving":return"driving";case"driving-traffic":return"driving-traffic";default:return"walking"}}convertToGeoJSON(e){return{type:"Feature",properties:{distance:e.distance,duration:e.duration,weight:e.weight},geometry:e.geometry}}getRouteSummary(e){let r=e.distance/1e3,i=e.duration/60;return{distance:e.distance,duration:e.duration,distanceText:r<1?`${Math.round(e.distance)} m`:`${r.toFixed(2)} km`,durationText:i<60?`${Math.round(i)} min`:`${Math.floor(i/60)}h ${Math.round(i%60)}min`}}checkAvailability(){return l(this,null,function*(){try{let e=`${this.baseUrl}/walking/0,0;0.001,0.001?access_token=${this.accessToken}&overview=simplified`,r=yield u(this.http.get(e));return!0}catch(e){return console.error("Mapbox availability check failed:",e),!1}})}};n.\u0275fac=function(r){return new(r||n)(w(f))},n.\u0275prov=p({token:n,factory:n.\u0275fac,providedIn:"root"});let s=n;return s})();export{M as a};
