import{b as M}from"./chunk-BNNKLWXU.js";import{a as O}from"./chunk-FULEFYAM.js";import{_ as T,g as I,l as m,n as d}from"./chunk-6UWMO7JM.js";import{a as f,b as u,f as A,h as c}from"./chunk-LNJ3S2LQ.js";var p=(()=>{let S=class S{constructor(t){this.http=t,this.STORAGE_KEYS={EVACUATION_CENTERS:"offline_evacuation_centers",ROUTES:"offline_routes",MAP_TILES:"offline_map_tiles",LAST_SYNC:"last_data_sync",OFFLINE_MODE:"offline_mode_enabled",USER_LOCATION:"last_user_location"},this.MAX_STORAGE_SIZE=50*1024*1024,this.TILE_CACHE_LIMIT=1e3,this.initializeStorage()}initializeStorage(){localStorage.getItem(this.STORAGE_KEYS.EVACUATION_CENTERS)||localStorage.setItem(this.STORAGE_KEYS.EVACUATION_CENTERS,JSON.stringify([])),localStorage.getItem(this.STORAGE_KEYS.ROUTES)||localStorage.setItem(this.STORAGE_KEYS.ROUTES,JSON.stringify([])),localStorage.getItem(this.STORAGE_KEYS.MAP_TILES)||localStorage.setItem(this.STORAGE_KEYS.MAP_TILES,JSON.stringify({})),console.log("\u2705 Offline storage initialized")}syncEvacuationCenters(){return c(this,null,function*(){try{console.log("\u{1F504} Syncing evacuation centers from backend...");let t=yield I(this.http.get(`${O.apiUrl}/offline/evacuation-centers`));return t.success&&t.data?(yield this.saveEvacuationCenters(t.data),localStorage.setItem(this.STORAGE_KEYS.LAST_SYNC,t.sync_timestamp),console.log(`\u2705 Synced ${t.count} evacuation centers`),!0):(console.error("\u274C Invalid response from server"),!1)}catch(t){return console.error("\u274C Failed to sync evacuation centers:",t),!1}})}saveEvacuationCenters(t){return c(this,null,function*(){try{let e=t.map(o=>u(f({},o),{last_updated:new Date().toISOString()}));localStorage.setItem(this.STORAGE_KEYS.EVACUATION_CENTERS,JSON.stringify(e)),console.log(`\u{1F4BE} Saved ${t.length} evacuation centers to local storage`)}catch(e){throw console.error("\u274C Error saving evacuation centers:",e),e}})}getEvacuationCenters(t){return c(this,null,function*(){try{let e=localStorage.getItem(this.STORAGE_KEYS.EVACUATION_CENTERS);if(!e)return[];let o=JSON.parse(e);return t?o.filter(a=>a.disaster_type===t):o}catch(e){return console.error("\u274C Error fetching evacuation centers:",e),[]}})}getNearestCenters(t,e,o,a=2){return c(this,null,function*(){return(yield this.getEvacuationCenters(o)).map(i=>u(f({},i),{distance:this.calculateDistance(t,e,i.latitude,i.longitude)})).sort((i,n)=>i.distance-n.distance).slice(0,a)})}saveRoute(t){return c(this,null,function*(){try{let e=this.getStoredRoutes(),o=u(f({},t),{id:`${t.start_lat}_${t.start_lng}_${t.end_lat}_${t.end_lng}_${t.travel_mode}`,created_at:new Date().toISOString()}),a=e.filter(s=>s.id!==o.id);a.push(o);let r=a.slice(-100);localStorage.setItem(this.STORAGE_KEYS.ROUTES,JSON.stringify(r)),console.log("\u{1F4BE} Route saved to cache")}catch(e){console.error("\u274C Error saving route:",e)}})}getRoute(t,e,o,a,r){return c(this,null,function*(){try{let s=this.getStoredRoutes(),i=`${t}_${e}_${o}_${a}_${r}`,n=s.find(l=>l.id===i);if(n&&n.created_at){let l=Date.now()-new Date(n.created_at).getTime(),h=24*60*60*1e3;if(l<h)return n}return null}catch(s){return console.error("\u274C Error fetching route:",s),null}})}getStoredRoutes(){try{let t=localStorage.getItem(this.STORAGE_KEYS.ROUTES);return t?JSON.parse(t):[]}catch(t){return console.error("\u274C Error parsing stored routes:",t),[]}}saveMapTile(t,e,o,a){return c(this,null,function*(){try{let r=this.getStoredTiles(),s=`${t}_${e}_${o}`;r[s]={key:s,z:t,x:e,y:o,tile_data:a,created_at:new Date().toISOString()};let i=Object.keys(r);i.length>this.TILE_CACHE_LIMIT&&i.map(h=>({key:h,created_at:r[h].created_at})).sort((h,g)=>new Date(h.created_at).getTime()-new Date(g.created_at).getTime()).slice(0,i.length-this.TILE_CACHE_LIMIT).forEach(h=>delete r[h.key]),localStorage.setItem(this.STORAGE_KEYS.MAP_TILES,JSON.stringify(r))}catch(r){console.error("\u274C Error saving map tile:",r)}})}getMapTile(t,e,o){return c(this,null,function*(){try{let a=this.getStoredTiles(),r=`${t}_${e}_${o}`;return a[r]||null}catch(a){return console.error("\u274C Error getting map tile:",a),null}})}getStoredTiles(){try{let t=localStorage.getItem(this.STORAGE_KEYS.MAP_TILES);return t?JSON.parse(t):{}}catch(t){return console.error("\u274C Error parsing stored tiles:",t),{}}}setOfflineMode(t){localStorage.setItem(this.STORAGE_KEYS.OFFLINE_MODE,t.toString()),console.log(`\u{1F504} Offline mode ${t?"enabled":"disabled"}`)}isOfflineMode(){return localStorage.getItem(this.STORAGE_KEYS.OFFLINE_MODE)==="true"}saveUserLocation(t,e){let o={lat:t,lng:e,timestamp:new Date().toISOString()};localStorage.setItem(this.STORAGE_KEYS.USER_LOCATION,JSON.stringify(o))}getLastUserLocation(){try{let t=localStorage.getItem(this.STORAGE_KEYS.USER_LOCATION);return t?JSON.parse(t):null}catch(t){return console.error("\u274C Error getting user location:",t),null}}isDataAvailable(){return c(this,null,function*(){return(yield this.getEvacuationCenters()).length>0})}getLastSyncTime(){return localStorage.getItem(this.STORAGE_KEYS.LAST_SYNC)}calculateDistance(t,e,o,a){let s=this.toRadians(o-t),i=this.toRadians(a-e),n=Math.sin(s/2)*Math.sin(s/2)+Math.cos(this.toRadians(t))*Math.cos(this.toRadians(o))*Math.sin(i/2)*Math.sin(i/2);return 6371*(2*Math.atan2(Math.sqrt(n),Math.sqrt(1-n)))}toRadians(t){return t*(Math.PI/180)}clearOfflineData(){Object.values(this.STORAGE_KEYS).forEach(t=>{localStorage.removeItem(t)}),this.initializeStorage(),console.log("\u{1F5D1}\uFE0F All offline data cleared")}getStorageInfo(){let t=0;Object.values(this.STORAGE_KEYS).forEach(a=>{let r=localStorage.getItem(a);r&&(t+=new Blob([r]).size)});let e=this.MAX_STORAGE_SIZE-t,o=t/this.MAX_STORAGE_SIZE*100;return{used:t,available:e,percentage:o}}isOnline(){return navigator.onLine}};S.\u0275fac=function(e){return new(e||S)(d(T))},S.\u0275prov=m({token:S,factory:S.\u0275fac,providedIn:"root"});let E=S;return E})();var y=A(M());var P=(()=>{let S=class S{constructor(t,e){this.http=t,this.offlineStorage=e,this.TILE_CACHE_SIZE=1e3,this.CACHE_EXPIRY_DAYS=30,this.PHILIPPINES_BOUNDS={north:21,south:4.5,east:127,west:116}}createOfflineTileLayer(){let t=y.tileLayer("",{attribution:"\xA9 OpenStreetMap contributors (Offline Mode)",maxZoom:18,minZoom:8});return t.createTile=(e,o)=>{let a=document.createElement("img");return this.getTileFromCache(e.z,e.x,e.y).then(r=>{r?(a.src=`data:image/png;base64,${r.tile_data}`,o(null,a)):(a.src=this.createPlaceholderTile(e),o(null,a))}).catch(r=>{console.error("Error loading cached tile:",r),a.src=this.createPlaceholderTile(e),o(null,a)}),a},t}preloadMapTiles(t,e,o=50,a){return c(this,null,function*(){console.log("\u{1F5FA}\uFE0F Starting map tile preload...");let r=[10,11,12,13,14,15],s=0,i=0;for(let n of r){let l=this.calculateTileBounds(t,e,o,n);s+=(l.maxX-l.minX+1)*(l.maxY-l.minY+1)}console.log(`\u{1F4CA} Total tiles to download: ${s}`);for(let n of r){let l=this.calculateTileBounds(t,e,o,n);for(let h=l.minX;h<=l.maxX;h++)for(let g=l.minY;g<=l.maxY;g++)try{yield this.downloadAndCacheTile(n,h,g),i++,a&&a(i,s),yield this.delay(100)}catch(_){console.warn(`Failed to cache tile ${n}/${h}/${g}:`,_),i++}}console.log("\u2705 Map tile preload completed")})}downloadAndCacheTile(t,e,o){return c(this,null,function*(){let a=yield this.getTileFromCache(t,e,o);if(a&&!this.isTileExpired(a.created_at))return;let r=`https://tile.openstreetmap.org/${t}/${e}/${o}.png`;try{let s=yield this.http.get(r,{responseType:"blob"}).toPromise();if(s){let i=yield this.blobToBase64(s);yield this.saveTileToCache(t,e,o,i)}}catch(s){throw new Error(`Failed to download tile: ${s}`)}})}getTileFromCache(t,e,o){return c(this,null,function*(){let a=yield this.offlineStorage.getMapTile(t,e,o);return a?{z:a.z,x:a.x,y:a.y,tile_data:a.tile_data,created_at:a.created_at}:null})}saveTileToCache(t,e,o,a){return c(this,null,function*(){yield this.offlineStorage.saveMapTile(t,e,o,a)})}calculateTileBounds(t,e,o,a){let r=t*Math.PI/180,s=Math.pow(2,a),i=o/111,n=o/(111*Math.cos(r)),l=Math.max(t-i,this.PHILIPPINES_BOUNDS.south),h=Math.min(t+i,this.PHILIPPINES_BOUNDS.north),g=Math.max(e-n,this.PHILIPPINES_BOUNDS.west),_=Math.min(e+n,this.PHILIPPINES_BOUNDS.east);return{minX:Math.floor((g+180)/360*s),maxX:Math.floor((_+180)/360*s),minY:Math.floor((1-Math.log(Math.tan(h*Math.PI/180)+1/Math.cos(h*Math.PI/180))/Math.PI)/2*s),maxY:Math.floor((1-Math.log(Math.tan(l*Math.PI/180)+1/Math.cos(l*Math.PI/180))/Math.PI)/2*s)}}createPlaceholderTile(t){let e=document.createElement("canvas");e.width=256,e.height=256;let o=e.getContext("2d");return o&&(o.fillStyle="#f0f0f0",o.fillRect(0,0,256,256),o.strokeStyle="#ccc",o.strokeRect(0,0,256,256),o.fillStyle="#999",o.font="12px Arial",o.textAlign="center",o.fillText("Offline Mode",128,120),o.fillText(`${t.z}/${t.x}/${t.y}`,128,140)),e.toDataURL()}isTileExpired(t){let e=new Date(t);return(new Date().getTime()-e.getTime())/(1e3*60*60*24)>this.CACHE_EXPIRY_DAYS}blobToBase64(t){return new Promise((e,o)=>{let a=new FileReader;a.onload=()=>{let r=a.result;e(r.split(",")[1])},a.onerror=o,a.readAsDataURL(t)})}delay(t){return new Promise(e=>setTimeout(e,t))}cleanupOldTiles(){return c(this,null,function*(){console.log("\u{1F9F9} Cleaning up old map tiles..."),console.log("\u2705 Tile cleanup completed")})}getCacheStats(){return c(this,null,function*(){let t=this.offlineStorage.getStorageInfo(),o=Math.floor(t.used/15e3),a=this.formatBytes(t.used);return{tileCount:o,sizeEstimate:a}})}formatBytes(t){if(t===0)return"0 Bytes";let e=1024,o=["Bytes","KB","MB","GB"],a=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,a)).toFixed(2))+" "+o[a]}};S.\u0275fac=function(e){return new(e||S)(d(T),d(p))},S.\u0275prov=m({token:S,factory:S.\u0275fac,providedIn:"root"});let E=S;return E})();export{p as a,P as b};
