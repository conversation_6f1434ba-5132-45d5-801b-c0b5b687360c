import{a as G,b as H}from"./chunk-BNNKLWXU.js";import{a as w}from"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{$ as k,$a as I,A as y,Ab as B,Cb as V,D as P,Db as j,F as h,G as m,H as f,J as b,M as C,Na as $,O as L,Oa as A,Pa as D,Qa as T,Wa as N,X as x,ab as E,ca as F,ea as _,g as v,p as d,ub as R,vb as S,y as O,zb as z}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{f as U,h as s}from"./chunk-LNJ3S2LQ.js";var a=U(H());var it=(()=>{let g=class g{constructor(){this.userMarker=null,this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.loadingCtrl=d(B),this.toastCtrl=d(V),this.alertCtrl=d(z),this.http=d(k),this.router=d(_),this.route=d(F)}ngOnInit(){console.log("\u{1F535} FLOOD MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F535} FLOOD MAP: New center to highlight:",this.newCenterId))})}ngAfterViewInit(){return s(this,null,function*(){console.log("\u{1F535} FLOOD MAP: View initialized, loading map..."),setTimeout(()=>s(this,null,function*(){yield this.loadFloodMap()}),100)})}loadFloodMap(){return s(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading flood evacuation centers...",spinner:"crescent"});yield t.present();try{let n=yield G.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),o=n.coords.latitude,e=n.coords.longitude;console.log(`\u{1F535} FLOOD MAP: User location [${o}, ${e}]`),this.userLocation={lat:o,lng:e},this.initializeMap(o,e),yield this.loadFloodCenters(o,e),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F535} Showing ${this.evacuationCenters.length} flood evacuation centers`,duration:3e3,color:"primary",position:"top"})).present()}catch(n){yield t.dismiss(),console.error("\u{1F535} FLOOD MAP: Error loading map",n),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadFloodMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,n){if(console.log(`\u{1F535} FLOOD MAP: Initializing map at [${t}, ${n}]`),!document.getElementById("flood-map"))throw console.error("\u{1F535} FLOOD MAP: Container #flood-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=a.map("flood-map").setView([t,n],13),a.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=a.marker([t,n],{icon:a.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadFloodCenters(t,n){return s(this,null,function*(){try{console.log("\u{1F535} FLOOD MAP: Fetching flood centers...");let o=yield v(this.http.get(`${w.apiUrl}/evacuation-centers`));if(console.log("\u{1F535} FLOOD MAP: Total centers received:",(o==null?void 0:o.length)||0),this.evacuationCenters=o.filter(e=>e.disaster_type==="Flood"),console.log(`\u{1F535} FLOOD MAP: Filtered to ${this.evacuationCenters.length} flood centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Flood Centers",message:"No flood evacuation centers found in the database.",buttons:["OK"]})).present();return}if(this.evacuationCenters.forEach(e=>{let i=Number(e.latitude),r=Number(e.longitude);if(!isNaN(i)&&!isNaN(r)){let c=a.marker([i,r],{icon:a.icon({iconUrl:"assets/forFlood.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),l=this.calculateDistance(t,n,i,r);c.on("click",()=>{this.showTransportationOptions(e)});let u=this.newCenterId&&e.id.toString()===this.newCenterId;c.bindPopup(`
            <div class="evacuation-popup">
              <h3>\u{1F535} ${e.name} ${u?"\u2B50 NEW!":""}</h3>
              <p><strong>Type:</strong> Flood Center</p>
              <p><strong>Distance:</strong> ${(l/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
              <p><em>Click marker for route options</em></p>
              ${u?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
            </div>
          `),u&&(c.openPopup(),this.map.setView([i,r],15),this.toastCtrl.create({message:`\u{1F195} New flood evacuation center: ${e.name}`,duration:5e3,color:"primary",position:"top"}).then(p=>p.present())),c.addTo(this.map),console.log(`\u{1F535} Added flood marker: ${e.name}`)}}),yield this.routeToTwoNearestCenters(),this.evacuationCenters.length>0){let e=a.latLngBounds([]);e.extend([t,n]),this.evacuationCenters.forEach(i=>{e.extend([Number(i.latitude),Number(i.longitude)])}),this.map.fitBounds(e,{padding:[50,50]})}}catch(o){console.error("\u{1F535} FLOOD MAP: Error loading centers",o),yield(yield this.toastCtrl.create({message:"Error loading flood centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}routeToTwoNearestCenters(){return s(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F535} FLOOD MAP: No user location or evacuation centers available");return}try{console.log("\u{1F535} FLOOD MAP: Finding 2 nearest flood centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0)return;this.clearRoutes(),yield this.calculateRoutes(t)}catch(t){console.error("\u{1F535} FLOOD MAP: Error calculating routes",t)}})}getTwoNearestCenters(t,n){return[...this.evacuationCenters].sort((e,i)=>{let r=this.calculateDistance(t,n,Number(e.latitude),Number(e.longitude)),c=this.calculateDistance(t,n,Number(i.latitude),Number(i.longitude));return r-c}).slice(0,2)}clearRoutes(){this.map.eachLayer(t=>{t instanceof a.GeoJSON&&this.map.removeLayer(t)})}calculateRoutes(t){return s(this,null,function*(){for(let n of t)yield this.calculateRoute(n,"walking")})}calculateRoute(t,n){return s(this,null,function*(){try{if(!this.userLocation){console.error("\u{1F535} FLOOD MAP: No user location available for routing");return}let o=yield fetch(`https://api.mapbox.com/directions/v5/mapbox/${n}/${this.userLocation.lng},${this.userLocation.lat};${t.longitude},${t.latitude}?geometries=geojson&access_token=${w.mapboxAccessToken}`);if(!o.ok)throw new Error(`HTTP error! status: ${o.status}`);let e=yield o.json();if(e.routes&&e.routes.length>0){let r={type:"Feature",geometry:e.routes[0].geometry,properties:{}};a.geoJSON(r,{style:{color:"#0066CC",weight:4,opacity:.8}}).addTo(this.map),console.log(`\u{1F535} FLOOD MAP: Route added to ${t.name}`)}}catch(o){console.error("\u{1F535} FLOOD MAP: Error calculating route:",o)}})}showTransportationOptions(t){return s(this,null,function*(){yield(yield this.alertCtrl.create({header:`Route to ${t.name}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToCenter(t,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToCenter(t,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToCenter(t,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToCenter(t,n){return s(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let o="walking";switch(n){case"walking":o="walking";break;case"cycling":o="cycling";break;case"driving":o="driving";break}let e=yield fetch(`https://api.mapbox.com/directions/v5/mapbox/${o}/${this.userLocation.lng},${this.userLocation.lat};${t.longitude},${t.latitude}?geometries=geojson&access_token=${w.mapboxAccessToken}`);if(e.ok){let i=yield e.json();if(i&&i.routes&&i.routes.length>0){let r=i.routes[0],l=a.polyline(r.geometry.coordinates.map(p=>[p[1],p[0]]),{color:"#0066CC",weight:5,opacity:.8});l.addTo(this.map),yield(yield this.toastCtrl.create({message:`\u{1F535} Route: ${(r.distance/1e3).toFixed(2)}km, ${(r.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(l.getBounds(),{padding:[50,50]})}}}catch(o){console.error("\u{1F535} FLOOD MAP: Error calculating individual route:",o),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,n,o,e){let r=t*Math.PI/180,c=o*Math.PI/180,l=(o-t)*Math.PI/180,u=(e-n)*Math.PI/180,p=Math.sin(l/2)*Math.sin(l/2)+Math.cos(r)*Math.cos(c)*Math.sin(u/2)*Math.sin(u/2);return 6371e3*(2*Math.atan2(Math.sqrt(p),Math.sqrt(1-p)))}goBack(){this.router.navigate(["/tabs/home"])}ionViewWillLeave(){this.map&&this.map.remove()}};g.\u0275fac=function(n){return new(n||g)},g.\u0275cmp=y({type:g,selectors:[["app-flood-map"]],decls:18,vars:3,consts:[[3,"translucent"],["color","primary"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[3,"fullscreen"],["id","flood-map",2,"height","100%","width","100%"],[1,"floating-info"],[1,"info-row"],["name","water","color","primary"],[1,"info-text"]],template:function(n,o){n&1&&(h(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),b("click",function(){return o.goBack()}),f(4,"ion-icon",4),m()(),h(5,"ion-title"),C(6,"\u{1F535} Flood Evacuation Centers"),m()()(),h(7,"ion-content",5),f(8,"div",6),h(9,"div",7)(10,"ion-card")(11,"ion-card-content")(12,"div",8),f(13,"ion-icon",9),h(14,"span"),C(15),m()(),h(16,"div",10),C(17," Showing evacuation centers specifically for flood disasters "),m()()()()()),n&2&&(P("translucent",!0),O(7),P("fullscreen",!0),O(8),L("Flood Centers: ",o.evacuationCenters.length,""))},dependencies:[j,$,A,D,T,N,I,E,R,S,x],styles:["#flood-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:250px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-primary);margin-bottom:4px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);line-height:1.3}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-primary);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}"]});let M=g;return M})();export{it as FloodMapPage};
