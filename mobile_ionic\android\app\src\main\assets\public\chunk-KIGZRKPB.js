import{a as vi}from"./chunk-5QR2BYRY.js";import{a as mi}from"./chunk-MOPC3N2Z.js";import{a as $t}from"./chunk-XUHFMYON.js";import{a as pA,b as jB}from"./chunk-BNNKLWXU.js";import{a as Fi}from"./chunk-KGX7B5OW.js";import{a as te}from"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{$ as Jt,$a as wi,A as ve,Bb as Zt,C as wA,Cb as At,D as k,Db as Le,E as ei,F as H,G as T,H as V,I as ee,J as sA,K as tA,L as je,M as X,N as yA,Na as Ie,O as bA,Oa as oi,P as ti,Pa as ii,Qa as ai,Ra as si,Ta as ci,V as Xt,Va as li,W as Ee,Wa as Wt,X as He,Xa as Bi,Ya as gi,Za as ui,_a as fi,ab as ye,ca as ri,cb as Yt,fb as be,g as ze,gb as di,hb as hi,oa as ni,p as xA,q as GA,qb as Ci,r as kA,u as jo,ub as Qi,vb as pi,w as Ai,y as O,z as qe,zb as Ui}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{e as qB,f as qo,h as J}from"./chunk-LNJ3S2LQ.js";var Li=qB((Yr,Zr)=>{"use strict";(function(v,S){typeof Yr=="object"&&typeof Zr<"u"?Zr.exports=S():typeof define=="function"&&define.amd?define(S):(v=typeof globalThis<"u"?globalThis:v||self,v.html2canvas=S())})(Yr,function(){"use strict";var v=function(e,A){return v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n])},v(e,A)};function S(e,A){if(typeof A!="function"&&A!==null)throw new TypeError("Class extends value "+String(A)+" is not a constructor or null");v(e,A);function r(){this.constructor=e}e.prototype=A===null?Object.create(A):(r.prototype=A.prototype,new r)}var p=function(){return p=Object.assign||function(A){for(var r,t=1,n=arguments.length;t<n;t++){r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(A[o]=r[o])}return A},p.apply(this,arguments)};function a(e,A,r,t){function n(o){return o instanceof r?o:new r(function(i){i(o)})}return new(r||(r=Promise))(function(o,i){function l(f){try{u(t.next(f))}catch(w){i(w)}}function s(f){try{u(t.throw(f))}catch(w){i(w)}}function u(f){f.done?o(f.value):n(f.value).then(l,s)}u((t=t.apply(e,A||[])).next())})}function c(e,A){var r={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},t,n,o,i;return i={next:l(0),throw:l(1),return:l(2)},typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function l(u){return function(f){return s([u,f])}}function s(u){if(t)throw new TypeError("Generator is already executing.");for(;r;)try{if(t=1,n&&(o=u[0]&2?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[u[0]&2,o.value]),u[0]){case 0:case 1:o=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,n=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(o=r.trys,!(o=o.length>0&&o[o.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!o||u[1]>o[0]&&u[1]<o[3])){r.label=u[1];break}if(u[0]===6&&r.label<o[1]){r.label=o[1],o=u;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(u);break}o[2]&&r.ops.pop(),r.trys.pop();continue}u=A.call(e,r)}catch(f){u=[6,f],n=0}finally{t=o=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function B(e,A,r){if(r||arguments.length===2)for(var t=0,n=A.length,o;t<n;t++)(o||!(t in A))&&(o||(o=Array.prototype.slice.call(A,0,t)),o[t]=A[t]);return e.concat(o||A)}for(var g=function(){function e(A,r,t,n){this.left=A,this.top=r,this.width=t,this.height=n}return e.prototype.add=function(A,r,t,n){return new e(this.left+A,this.top+r,this.width+t,this.height+n)},e.fromClientRect=function(A,r){return new e(r.left+A.windowBounds.left,r.top+A.windowBounds.top,r.width,r.height)},e.fromDOMRectList=function(A,r){var t=Array.from(r).find(function(n){return n.width!==0});return t?new e(t.left+A.windowBounds.left,t.top+A.windowBounds.top,t.width,t.height):e.EMPTY},e.EMPTY=new e(0,0,0,0),e}(),Q=function(e,A){return g.fromClientRect(e,A.getBoundingClientRect())},C=function(e){var A=e.body,r=e.documentElement;if(!A||!r)throw new Error("Unable to get document size");var t=Math.max(Math.max(A.scrollWidth,r.scrollWidth),Math.max(A.offsetWidth,r.offsetWidth),Math.max(A.clientWidth,r.clientWidth)),n=Math.max(Math.max(A.scrollHeight,r.scrollHeight),Math.max(A.offsetHeight,r.offsetHeight),Math.max(A.clientHeight,r.clientHeight));return new g(0,0,t,n)},U=function(e){for(var A=[],r=0,t=e.length;r<t;){var n=e.charCodeAt(r++);if(n>=55296&&n<=56319&&r<t){var o=e.charCodeAt(r++);(o&64512)===56320?A.push(((n&1023)<<10)+(o&1023)+65536):(A.push(n),r--)}else A.push(n)}return A},d=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var r=e.length;if(!r)return"";for(var t=[],n=-1,o="";++n<r;){var i=e[n];i<=65535?t.push(i):(i-=65536,t.push((i>>10)+55296,i%1024+56320)),(n+1===r||t.length>16384)&&(o+=String.fromCharCode.apply(String,t),t.length=0)}return o},m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",_=typeof Uint8Array>"u"?[]:new Uint8Array(256),D=0;D<m.length;D++)_[m.charCodeAt(D)]=D;for(var z="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Y=typeof Uint8Array>"u"?[]:new Uint8Array(256),TA=0;TA<z.length;TA++)Y[z.charCodeAt(TA)]=TA;for(var mA=function(e){var A=e.length*.75,r=e.length,t,n=0,o,i,l,s;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var u=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),f=Array.isArray(u)?u:new Uint8Array(u);for(t=0;t<r;t+=4)o=Y[e.charCodeAt(t)],i=Y[e.charCodeAt(t+1)],l=Y[e.charCodeAt(t+2)],s=Y[e.charCodeAt(t+3)],f[n++]=o<<2|i>>4,f[n++]=(i&15)<<4|l>>2,f[n++]=(l&3)<<6|s&63;return u},zt=function(e){for(var A=e.length,r=[],t=0;t<A;t+=2)r.push(e[t+1]<<8|e[t]);return r},et=function(e){for(var A=e.length,r=[],t=0;t<A;t+=4)r.push(e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]);return r},re=5,qt=11,jt=2,Mi=qt-re,$r=65536>>re,Ki=1<<re,Ar=Ki-1,Si=1024>>re,Di=$r+Si,Ti=Di,Oi=32,_i=Ti+Oi,Pi=65536>>qt,Ni=1<<Mi,Ri=Ni-1,zr=function(e,A,r){return e.slice?e.slice(A,r):new Uint16Array(Array.prototype.slice.call(e,A,r))},Gi=function(e,A,r){return e.slice?e.slice(A,r):new Uint32Array(Array.prototype.slice.call(e,A,r))},ki=function(e,A){var r=mA(e),t=Array.isArray(r)?et(r):new Uint32Array(r),n=Array.isArray(r)?zt(r):new Uint16Array(r),o=24,i=zr(n,o/2,t[4]/2),l=t[5]===2?zr(n,(o+t[4])/2):Gi(t,Math.ceil((o+t[4])/4));return new Vi(t[0],t[1],t[2],t[3],i,l)},Vi=function(){function e(A,r,t,n,o,i){this.initialValue=A,this.errorValue=r,this.highStart=t,this.highValueIndex=n,this.index=o,this.data=i}return e.prototype.get=function(A){var r;if(A>=0){if(A<55296||A>56319&&A<=65535)return r=this.index[A>>re],r=(r<<jt)+(A&Ar),this.data[r];if(A<=65535)return r=this.index[$r+(A-55296>>re)],r=(r<<jt)+(A&Ar),this.data[r];if(A<this.highStart)return r=_i-Pi+(A>>qt),r=this.index[r],r+=A>>re&Ri,r=this.index[r],r=(r<<jt)+(A&Ar),this.data[r];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),qr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Xi=typeof Uint8Array>"u"?[]:new Uint8Array(256),tt=0;tt<qr.length;tt++)Xi[qr.charCodeAt(tt)]=tt;var Ji="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",jr=50,Wi=1,An=2,en=3,Yi=4,Zi=5,tn=7,rn=8,nn=9,VA=10,er=11,on=12,tr=13,$i=14,xe=15,rr=16,rt=17,Me=18,zi=19,an=20,nr=21,Ke=22,or=23,ge=24,UA=25,Se=26,De=27,ue=28,qi=29,ne=30,ji=31,nt=32,ot=33,ir=34,ar=35,sr=36,Te=37,cr=38,it=39,at=40,lr=41,sn=42,Aa=43,ea=[9001,65288],cn="!",W="\xD7",st="\xF7",Br=ki(Ji),OA=[ne,sr],gr=[Wi,An,en,Zi],ln=[VA,rn],Bn=[De,Se],ta=gr.concat(ln),gn=[cr,it,at,ir,ar],ra=[xe,tr],na=function(e,A){A===void 0&&(A="strict");var r=[],t=[],n=[];return e.forEach(function(o,i){var l=Br.get(o);if(l>jr?(n.push(!0),l-=jr):n.push(!1),["normal","auto","loose"].indexOf(A)!==-1&&[8208,8211,12316,12448].indexOf(o)!==-1)return t.push(i),r.push(rr);if(l===Yi||l===er){if(i===0)return t.push(i),r.push(ne);var s=r[i-1];return ta.indexOf(s)===-1?(t.push(t[i-1]),r.push(s)):(t.push(i),r.push(ne))}if(t.push(i),l===ji)return r.push(A==="strict"?nr:Te);if(l===sn||l===qi)return r.push(ne);if(l===Aa)return o>=131072&&o<=196605||o>=196608&&o<=262141?r.push(Te):r.push(ne);r.push(l)}),[t,r,n]},ur=function(e,A,r,t){var n=t[r];if(Array.isArray(e)?e.indexOf(n)!==-1:e===n)for(var o=r;o<=t.length;){o++;var i=t[o];if(i===A)return!0;if(i!==VA)break}if(n===VA)for(var o=r;o>0;){o--;var l=t[o];if(Array.isArray(e)?e.indexOf(l)!==-1:e===l)for(var s=r;s<=t.length;){s++;var i=t[s];if(i===A)return!0;if(i!==VA)break}if(l!==VA)break}return!1},un=function(e,A){for(var r=e;r>=0;){var t=A[r];if(t===VA)r--;else return t}return 0},oa=function(e,A,r,t,n){if(r[t]===0)return W;var o=t-1;if(Array.isArray(n)&&n[o]===!0)return W;var i=o-1,l=o+1,s=A[o],u=i>=0?A[i]:0,f=A[l];if(s===An&&f===en)return W;if(gr.indexOf(s)!==-1)return cn;if(gr.indexOf(f)!==-1||ln.indexOf(f)!==-1)return W;if(un(o,A)===rn)return st;if(Br.get(e[o])===er||(s===nt||s===ot)&&Br.get(e[l])===er||s===tn||f===tn||s===nn||[VA,tr,xe].indexOf(s)===-1&&f===nn||[rt,Me,zi,ge,ue].indexOf(f)!==-1||un(o,A)===Ke||ur(or,Ke,o,A)||ur([rt,Me],nr,o,A)||ur(on,on,o,A))return W;if(s===VA)return st;if(s===or||f===or)return W;if(f===rr||s===rr)return st;if([tr,xe,nr].indexOf(f)!==-1||s===$i||u===sr&&ra.indexOf(s)!==-1||s===ue&&f===sr||f===an||OA.indexOf(f)!==-1&&s===UA||OA.indexOf(s)!==-1&&f===UA||s===De&&[Te,nt,ot].indexOf(f)!==-1||[Te,nt,ot].indexOf(s)!==-1&&f===Se||OA.indexOf(s)!==-1&&Bn.indexOf(f)!==-1||Bn.indexOf(s)!==-1&&OA.indexOf(f)!==-1||[De,Se].indexOf(s)!==-1&&(f===UA||[Ke,xe].indexOf(f)!==-1&&A[l+1]===UA)||[Ke,xe].indexOf(s)!==-1&&f===UA||s===UA&&[UA,ue,ge].indexOf(f)!==-1)return W;if([UA,ue,ge,rt,Me].indexOf(f)!==-1)for(var w=o;w>=0;){var h=A[w];if(h===UA)return W;if([ue,ge].indexOf(h)!==-1)w--;else break}if([De,Se].indexOf(f)!==-1)for(var w=[rt,Me].indexOf(s)!==-1?i:o;w>=0;){var h=A[w];if(h===UA)return W;if([ue,ge].indexOf(h)!==-1)w--;else break}if(cr===s&&[cr,it,ir,ar].indexOf(f)!==-1||[it,ir].indexOf(s)!==-1&&[it,at].indexOf(f)!==-1||[at,ar].indexOf(s)!==-1&&f===at||gn.indexOf(s)!==-1&&[an,Se].indexOf(f)!==-1||gn.indexOf(f)!==-1&&s===De||OA.indexOf(s)!==-1&&OA.indexOf(f)!==-1||s===ge&&OA.indexOf(f)!==-1||OA.concat(UA).indexOf(s)!==-1&&f===Ke&&ea.indexOf(e[l])===-1||OA.concat(UA).indexOf(f)!==-1&&s===Me)return W;if(s===lr&&f===lr){for(var L=r[o],F=1;L>0&&(L--,A[L]===lr);)F++;if(F%2!==0)return W}return s===nt&&f===ot?W:st},ia=function(e,A){A||(A={lineBreak:"normal",wordBreak:"normal"});var r=na(e,A.lineBreak),t=r[0],n=r[1],o=r[2];(A.wordBreak==="break-all"||A.wordBreak==="break-word")&&(n=n.map(function(l){return[UA,ne,sn].indexOf(l)!==-1?Te:l}));var i=A.wordBreak==="keep-all"?o.map(function(l,s){return l&&e[s]>=19968&&e[s]<=40959}):void 0;return[t,n,i]},aa=function(){function e(A,r,t,n){this.codePoints=A,this.required=r===cn,this.start=t,this.end=n}return e.prototype.slice=function(){return d.apply(void 0,this.codePoints.slice(this.start,this.end))},e}(),sa=function(e,A){var r=U(e),t=ia(r,A),n=t[0],o=t[1],i=t[2],l=r.length,s=0,u=0;return{next:function(){if(u>=l)return{done:!0,value:null};for(var f=W;u<l&&(f=oa(r,o,n,++u,i))===W;);if(f!==W||u===l){var w=new aa(r,f,s,u);return s=u,{value:w,done:!1}}return{done:!0,value:null}}}},ca=1,la=2,Oe=4,fn=8,ct=10,wn=47,_e=92,Ba=9,ga=32,lt=34,Pe=61,ua=35,fa=36,wa=37,Bt=39,gt=40,Ne=41,da=95,dA=45,ha=33,Ca=60,Qa=62,pa=64,Ua=91,Fa=93,ma=61,va=123,ut=63,Ea=125,dn=124,Ha=126,Ia=128,hn=65533,fr=42,oe=43,ya=44,ba=58,La=59,Re=46,xa=0,Ma=8,Ka=11,Sa=14,Da=31,Ta=127,MA=-1,Cn=48,Qn=97,pn=101,Oa=102,_a=117,Pa=122,Un=65,Fn=69,mn=70,Na=85,Ra=90,fA=function(e){return e>=Cn&&e<=57},Ga=function(e){return e>=55296&&e<=57343},fe=function(e){return fA(e)||e>=Un&&e<=mn||e>=Qn&&e<=Oa},ka=function(e){return e>=Qn&&e<=Pa},Va=function(e){return e>=Un&&e<=Ra},Xa=function(e){return ka(e)||Va(e)},Ja=function(e){return e>=Ia},ft=function(e){return e===ct||e===Ba||e===ga},wt=function(e){return Xa(e)||Ja(e)||e===da},vn=function(e){return wt(e)||fA(e)||e===dA},Wa=function(e){return e>=xa&&e<=Ma||e===Ka||e>=Sa&&e<=Da||e===Ta},XA=function(e,A){return e!==_e?!1:A!==ct},dt=function(e,A,r){return e===dA?wt(A)||XA(A,r):wt(e)?!0:!!(e===_e&&XA(e,A))},wr=function(e,A,r){return e===oe||e===dA?fA(A)?!0:A===Re&&fA(r):fA(e===Re?A:e)},Ya=function(e){var A=0,r=1;(e[A]===oe||e[A]===dA)&&(e[A]===dA&&(r=-1),A++);for(var t=[];fA(e[A]);)t.push(e[A++]);var n=t.length?parseInt(d.apply(void 0,t),10):0;e[A]===Re&&A++;for(var o=[];fA(e[A]);)o.push(e[A++]);var i=o.length,l=i?parseInt(d.apply(void 0,o),10):0;(e[A]===Fn||e[A]===pn)&&A++;var s=1;(e[A]===oe||e[A]===dA)&&(e[A]===dA&&(s=-1),A++);for(var u=[];fA(e[A]);)u.push(e[A++]);var f=u.length?parseInt(d.apply(void 0,u),10):0;return r*(n+l*Math.pow(10,-i))*Math.pow(10,s*f)},Za={type:2},$a={type:3},za={type:4},qa={type:13},ja={type:8},As={type:21},es={type:9},ts={type:10},rs={type:11},ns={type:12},os={type:14},ht={type:23},is={type:1},as={type:25},ss={type:24},cs={type:26},ls={type:27},Bs={type:28},gs={type:29},us={type:31},dr={type:32},En=function(){function e(){this._value=[]}return e.prototype.write=function(A){this._value=this._value.concat(U(A))},e.prototype.read=function(){for(var A=[],r=this.consumeToken();r!==dr;)A.push(r),r=this.consumeToken();return A},e.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case lt:return this.consumeStringToken(lt);case ua:var r=this.peekCodePoint(0),t=this.peekCodePoint(1),n=this.peekCodePoint(2);if(vn(r)||XA(t,n)){var o=dt(r,t,n)?la:ca,i=this.consumeName();return{type:5,value:i,flags:o}}break;case fa:if(this.peekCodePoint(0)===Pe)return this.consumeCodePoint(),qa;break;case Bt:return this.consumeStringToken(Bt);case gt:return Za;case Ne:return $a;case fr:if(this.peekCodePoint(0)===Pe)return this.consumeCodePoint(),os;break;case oe:if(wr(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case ya:return za;case dA:var l=A,s=this.peekCodePoint(0),u=this.peekCodePoint(1);if(wr(l,s,u))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(dt(l,s,u))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(s===dA&&u===Qa)return this.consumeCodePoint(),this.consumeCodePoint(),ss;break;case Re:if(wr(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case wn:if(this.peekCodePoint(0)===fr)for(this.consumeCodePoint();;){var f=this.consumeCodePoint();if(f===fr&&(f=this.consumeCodePoint(),f===wn))return this.consumeToken();if(f===MA)return this.consumeToken()}break;case ba:return cs;case La:return ls;case Ca:if(this.peekCodePoint(0)===ha&&this.peekCodePoint(1)===dA&&this.peekCodePoint(2)===dA)return this.consumeCodePoint(),this.consumeCodePoint(),as;break;case pa:var w=this.peekCodePoint(0),h=this.peekCodePoint(1),L=this.peekCodePoint(2);if(dt(w,h,L)){var i=this.consumeName();return{type:7,value:i}}break;case Ua:return Bs;case _e:if(XA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case Fa:return gs;case ma:if(this.peekCodePoint(0)===Pe)return this.consumeCodePoint(),ja;break;case va:return rs;case Ea:return ns;case _a:case Na:var F=this.peekCodePoint(0),E=this.peekCodePoint(1);return F===oe&&(fe(E)||E===ut)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case dn:if(this.peekCodePoint(0)===Pe)return this.consumeCodePoint(),es;if(this.peekCodePoint(0)===dn)return this.consumeCodePoint(),As;break;case Ha:if(this.peekCodePoint(0)===Pe)return this.consumeCodePoint(),ts;break;case MA:return dr}return ft(A)?(this.consumeWhiteSpace(),us):fA(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):wt(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:d(A)}},e.prototype.consumeCodePoint=function(){var A=this._value.shift();return typeof A>"u"?-1:A},e.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},e.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},e.prototype.consumeUnicodeRangeToken=function(){for(var A=[],r=this.consumeCodePoint();fe(r)&&A.length<6;)A.push(r),r=this.consumeCodePoint();for(var t=!1;r===ut&&A.length<6;)A.push(r),r=this.consumeCodePoint(),t=!0;if(t){var n=parseInt(d.apply(void 0,A.map(function(s){return s===ut?Cn:s})),16),o=parseInt(d.apply(void 0,A.map(function(s){return s===ut?mn:s})),16);return{type:30,start:n,end:o}}var i=parseInt(d.apply(void 0,A),16);if(this.peekCodePoint(0)===dA&&fe(this.peekCodePoint(1))){this.consumeCodePoint(),r=this.consumeCodePoint();for(var l=[];fe(r)&&l.length<6;)l.push(r),r=this.consumeCodePoint();var o=parseInt(d.apply(void 0,l),16);return{type:30,start:i,end:o}}else return{type:30,start:i,end:i}},e.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return A.toLowerCase()==="url"&&this.peekCodePoint(0)===gt?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===gt?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},e.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===MA)return{type:22,value:""};var r=this.peekCodePoint(0);if(r===Bt||r===lt){var t=this.consumeStringToken(this.consumeCodePoint());return t.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===MA||this.peekCodePoint(0)===Ne)?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),ht)}for(;;){var n=this.consumeCodePoint();if(n===MA||n===Ne)return{type:22,value:d.apply(void 0,A)};if(ft(n))return this.consumeWhiteSpace(),this.peekCodePoint(0)===MA||this.peekCodePoint(0)===Ne?(this.consumeCodePoint(),{type:22,value:d.apply(void 0,A)}):(this.consumeBadUrlRemnants(),ht);if(n===lt||n===Bt||n===gt||Wa(n))return this.consumeBadUrlRemnants(),ht;if(n===_e)if(XA(n,this.peekCodePoint(0)))A.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),ht;else A.push(n)}},e.prototype.consumeWhiteSpace=function(){for(;ft(this.peekCodePoint(0));)this.consumeCodePoint()},e.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(A===Ne||A===MA)return;XA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},e.prototype.consumeStringSlice=function(A){for(var r=5e4,t="";A>0;){var n=Math.min(r,A);t+=d.apply(void 0,this._value.splice(0,n)),A-=n}return this._value.shift(),t},e.prototype.consumeStringToken=function(A){var r="",t=0;do{var n=this._value[t];if(n===MA||n===void 0||n===A)return r+=this.consumeStringSlice(t),{type:0,value:r};if(n===ct)return this._value.splice(0,t),is;if(n===_e){var o=this._value[t+1];o!==MA&&o!==void 0&&(o===ct?(r+=this.consumeStringSlice(t),t=-1,this._value.shift()):XA(n,o)&&(r+=this.consumeStringSlice(t),r+=d(this.consumeEscapedCodePoint()),t=-1))}t++}while(!0)},e.prototype.consumeNumber=function(){var A=[],r=Oe,t=this.peekCodePoint(0);for((t===oe||t===dA)&&A.push(this.consumeCodePoint());fA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var n=this.peekCodePoint(1);if(t===Re&&fA(n))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),r=fn;fA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0),n=this.peekCodePoint(1);var o=this.peekCodePoint(2);if((t===Fn||t===pn)&&((n===oe||n===dA)&&fA(o)||fA(n)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),r=fn;fA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[Ya(A),r]},e.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),r=A[0],t=A[1],n=this.peekCodePoint(0),o=this.peekCodePoint(1),i=this.peekCodePoint(2);if(dt(n,o,i)){var l=this.consumeName();return{type:15,number:r,flags:t,unit:l}}return n===wa?(this.consumeCodePoint(),{type:16,number:r,flags:t}):{type:17,number:r,flags:t}},e.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(fe(A)){for(var r=d(A);fe(this.peekCodePoint(0))&&r.length<6;)r+=d(this.consumeCodePoint());ft(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(r,16);return t===0||Ga(t)||t>1114111?hn:t}return A===MA?hn:A},e.prototype.consumeName=function(){for(var A="";;){var r=this.consumeCodePoint();if(vn(r))A+=d(r);else if(XA(r,this.peekCodePoint(0)))A+=d(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(r),A}},e}(),Hn=function(){function e(A){this._tokens=A}return e.create=function(A){var r=new En;return r.write(A),new e(r.read())},e.parseValue=function(A){return e.create(A).parseComponentValue()},e.parseValues=function(A){return e.create(A).parseComponentValues()},e.prototype.parseComponentValue=function(){for(var A=this.consumeToken();A.type===31;)A=this.consumeToken();if(A.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var r=this.consumeComponentValue();do A=this.consumeToken();while(A.type===31);if(A.type===32)return r;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},e.prototype.parseComponentValues=function(){for(var A=[];;){var r=this.consumeComponentValue();if(r.type===32)return A;A.push(r),A.push()}},e.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},e.prototype.consumeSimpleBlock=function(A){for(var r={type:A,values:[]},t=this.consumeToken();;){if(t.type===32||ws(t,A))return r;this.reconsumeToken(t),r.values.push(this.consumeComponentValue()),t=this.consumeToken()}},e.prototype.consumeFunction=function(A){for(var r={name:A.value,values:[],type:18};;){var t=this.consumeToken();if(t.type===32||t.type===3)return r;this.reconsumeToken(t),r.values.push(this.consumeComponentValue())}},e.prototype.consumeToken=function(){var A=this._tokens.shift();return typeof A>"u"?dr:A},e.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},e}(),Ge=function(e){return e.type===15},we=function(e){return e.type===17},AA=function(e){return e.type===20},fs=function(e){return e.type===0},hr=function(e,A){return AA(e)&&e.value===A},In=function(e){return e.type!==31},de=function(e){return e.type!==31&&e.type!==4},KA=function(e){var A=[],r=[];return e.forEach(function(t){if(t.type===4){if(r.length===0)throw new Error("Error parsing function args, zero tokens for arg");A.push(r),r=[];return}t.type!==31&&r.push(t)}),r.length&&A.push(r),A},ws=function(e,A){return A===11&&e.type===12||A===28&&e.type===29?!0:A===2&&e.type===3},JA=function(e){return e.type===17||e.type===15},iA=function(e){return e.type===16||JA(e)},yn=function(e){return e.length>1?[e[0],e[1]]:[e[0]]},BA={type:17,number:0,flags:Oe},Cr={type:16,number:50,flags:Oe},WA={type:16,number:100,flags:Oe},ke=function(e,A,r){var t=e[0],n=e[1];return[rA(t,A),rA(typeof n<"u"?n:t,r)]},rA=function(e,A){if(e.type===16)return e.number/100*A;if(Ge(e))switch(e.unit){case"rem":case"em":return 16*e.number;case"px":default:return e.number}return e.number},bn="deg",Ln="grad",xn="rad",Mn="turn",Ct={name:"angle",parse:function(e,A){if(A.type===15)switch(A.unit){case bn:return Math.PI*A.number/180;case Ln:return Math.PI/200*A.number;case xn:return A.number;case Mn:return Math.PI*2*A.number}throw new Error("Unsupported angle type")}},Kn=function(e){return e.type===15&&(e.unit===bn||e.unit===Ln||e.unit===xn||e.unit===Mn)},Sn=function(e){var A=e.filter(AA).map(function(r){return r.value}).join(" ");switch(A){case"to bottom right":case"to right bottom":case"left top":case"top left":return[BA,BA];case"to top":case"bottom":return vA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[BA,WA];case"to right":case"left":return vA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[WA,WA];case"to bottom":case"top":return vA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[WA,BA];case"to left":case"right":return vA(270)}return 0},vA=function(e){return Math.PI*e/180},YA={name:"color",parse:function(e,A){if(A.type===18){var r=ds[A.name];if(typeof r>"u")throw new Error('Attempting to parse an unsupported color function "'+A.name+'"');return r(e,A.values)}if(A.type===5){if(A.value.length===3){var t=A.value.substring(0,1),n=A.value.substring(1,2),o=A.value.substring(2,3);return $A(parseInt(t+t,16),parseInt(n+n,16),parseInt(o+o,16),1)}if(A.value.length===4){var t=A.value.substring(0,1),n=A.value.substring(1,2),o=A.value.substring(2,3),i=A.value.substring(3,4);return $A(parseInt(t+t,16),parseInt(n+n,16),parseInt(o+o,16),parseInt(i+i,16)/255)}if(A.value.length===6){var t=A.value.substring(0,2),n=A.value.substring(2,4),o=A.value.substring(4,6);return $A(parseInt(t,16),parseInt(n,16),parseInt(o,16),1)}if(A.value.length===8){var t=A.value.substring(0,2),n=A.value.substring(2,4),o=A.value.substring(4,6),i=A.value.substring(6,8);return $A(parseInt(t,16),parseInt(n,16),parseInt(o,16),parseInt(i,16)/255)}}if(A.type===20){var l=_A[A.value.toUpperCase()];if(typeof l<"u")return l}return _A.TRANSPARENT}},ZA=function(e){return(255&e)===0},lA=function(e){var A=255&e,r=255&e>>8,t=255&e>>16,n=255&e>>24;return A<255?"rgba("+n+","+t+","+r+","+A/255+")":"rgb("+n+","+t+","+r+")"},$A=function(e,A,r,t){return(e<<24|A<<16|r<<8|Math.round(t*255)<<0)>>>0},Dn=function(e,A){if(e.type===17)return e.number;if(e.type===16){var r=A===3?1:255;return A===3?e.number/100*r:Math.round(e.number/100*r)}return 0},Tn=function(e,A){var r=A.filter(de);if(r.length===3){var t=r.map(Dn),n=t[0],o=t[1],i=t[2];return $A(n,o,i,1)}if(r.length===4){var l=r.map(Dn),n=l[0],o=l[1],i=l[2],s=l[3];return $A(n,o,i,s)}return 0};function Qr(e,A,r){return r<0&&(r+=1),r>=1&&(r-=1),r<1/6?(A-e)*r*6+e:r<1/2?A:r<2/3?(A-e)*6*(2/3-r)+e:e}var On=function(e,A){var r=A.filter(de),t=r[0],n=r[1],o=r[2],i=r[3],l=(t.type===17?vA(t.number):Ct.parse(e,t))/(Math.PI*2),s=iA(n)?n.number/100:0,u=iA(o)?o.number/100:0,f=typeof i<"u"&&iA(i)?rA(i,1):1;if(s===0)return $A(u*255,u*255,u*255,1);var w=u<=.5?u*(s+1):u+s-u*s,h=u*2-w,L=Qr(h,w,l+1/3),F=Qr(h,w,l),E=Qr(h,w,l-1/3);return $A(L*255,F*255,E*255,f)},ds={hsl:On,hsla:On,rgb:Tn,rgba:Tn},Ve=function(e,A){return YA.parse(e,Hn.create(A).parseComponentValue())},_A={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},hs={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(r){if(AA(r))switch(r.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Cs={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Qt=function(e,A){var r=YA.parse(e,A[0]),t=A[1];return t&&iA(t)?{color:r,stop:t}:{color:r,stop:null}},_n=function(e,A){var r=e[0],t=e[e.length-1];r.stop===null&&(r.stop=BA),t.stop===null&&(t.stop=WA);for(var n=[],o=0,i=0;i<e.length;i++){var l=e[i].stop;if(l!==null){var s=rA(l,A);s>o?n.push(s):n.push(o),o=s}else n.push(null)}for(var u=null,i=0;i<n.length;i++){var f=n[i];if(f===null)u===null&&(u=i);else if(u!==null){for(var w=i-u,h=n[u-1],L=(f-h)/(w+1),F=1;F<=w;F++)n[u+F-1]=L*F;u=null}}return e.map(function(E,R){var K=E.color;return{color:K,stop:Math.max(Math.min(1,n[R]/A),0)}})},Qs=function(e,A,r){var t=A/2,n=r/2,o=rA(e[0],A)-t,i=n-rA(e[1],r);return(Math.atan2(i,o)+Math.PI*2)%(Math.PI*2)},ps=function(e,A,r){var t=typeof e=="number"?e:Qs(e,A,r),n=Math.abs(A*Math.sin(t))+Math.abs(r*Math.cos(t)),o=A/2,i=r/2,l=n/2,s=Math.sin(t-Math.PI/2)*l,u=Math.cos(t-Math.PI/2)*l;return[n,o-u,o+u,i-s,i+s]},LA=function(e,A){return Math.sqrt(e*e+A*A)},Pn=function(e,A,r,t,n){var o=[[0,0],[0,A],[e,0],[e,A]];return o.reduce(function(i,l){var s=l[0],u=l[1],f=LA(r-s,t-u);return(n?f<i.optimumDistance:f>i.optimumDistance)?{optimumCorner:l,optimumDistance:f}:i},{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},Us=function(e,A,r,t,n){var o=0,i=0;switch(e.size){case 0:e.shape===0?o=i=Math.min(Math.abs(A),Math.abs(A-t),Math.abs(r),Math.abs(r-n)):e.shape===1&&(o=Math.min(Math.abs(A),Math.abs(A-t)),i=Math.min(Math.abs(r),Math.abs(r-n)));break;case 2:if(e.shape===0)o=i=Math.min(LA(A,r),LA(A,r-n),LA(A-t,r),LA(A-t,r-n));else if(e.shape===1){var l=Math.min(Math.abs(r),Math.abs(r-n))/Math.min(Math.abs(A),Math.abs(A-t)),s=Pn(t,n,A,r,!0),u=s[0],f=s[1];o=LA(u-A,(f-r)/l),i=l*o}break;case 1:e.shape===0?o=i=Math.max(Math.abs(A),Math.abs(A-t),Math.abs(r),Math.abs(r-n)):e.shape===1&&(o=Math.max(Math.abs(A),Math.abs(A-t)),i=Math.max(Math.abs(r),Math.abs(r-n)));break;case 3:if(e.shape===0)o=i=Math.max(LA(A,r),LA(A,r-n),LA(A-t,r),LA(A-t,r-n));else if(e.shape===1){var l=Math.max(Math.abs(r),Math.abs(r-n))/Math.max(Math.abs(A),Math.abs(A-t)),w=Pn(t,n,A,r,!1),u=w[0],f=w[1];o=LA(u-A,(f-r)/l),i=l*o}break}return Array.isArray(e.size)&&(o=rA(e.size[0],t),i=e.size.length===2?rA(e.size[1],n):o),[o,i]},Fs=function(e,A){var r=vA(180),t=[];return KA(A).forEach(function(n,o){if(o===0){var i=n[0];if(i.type===20&&i.value==="to"){r=Sn(n);return}else if(Kn(i)){r=Ct.parse(e,i);return}}var l=Qt(e,n);t.push(l)}),{angle:r,stops:t,type:1}},pt=function(e,A){var r=vA(180),t=[];return KA(A).forEach(function(n,o){if(o===0){var i=n[0];if(i.type===20&&["top","left","right","bottom"].indexOf(i.value)!==-1){r=Sn(n);return}else if(Kn(i)){r=(Ct.parse(e,i)+vA(270))%vA(360);return}}var l=Qt(e,n);t.push(l)}),{angle:r,stops:t,type:1}},ms=function(e,A){var r=vA(180),t=[],n=1,o=0,i=3,l=[];return KA(A).forEach(function(s,u){var f=s[0];if(u===0){if(AA(f)&&f.value==="linear"){n=1;return}else if(AA(f)&&f.value==="radial"){n=2;return}}if(f.type===18){if(f.name==="from"){var w=YA.parse(e,f.values[0]);t.push({stop:BA,color:w})}else if(f.name==="to"){var w=YA.parse(e,f.values[0]);t.push({stop:WA,color:w})}else if(f.name==="color-stop"){var h=f.values.filter(de);if(h.length===2){var w=YA.parse(e,h[1]),L=h[0];we(L)&&t.push({stop:{type:16,number:L.number*100,flags:L.flags},color:w})}}}}),n===1?{angle:(r+vA(180))%vA(360),stops:t,type:n}:{size:i,shape:o,stops:t,position:l,type:n}},Nn="closest-side",Rn="farthest-side",Gn="closest-corner",kn="farthest-corner",Vn="circle",Xn="ellipse",Jn="cover",Wn="contain",vs=function(e,A){var r=0,t=3,n=[],o=[];return KA(A).forEach(function(i,l){var s=!0;if(l===0){var u=!1;s=i.reduce(function(w,h){if(u)if(AA(h))switch(h.value){case"center":return o.push(Cr),w;case"top":case"left":return o.push(BA),w;case"right":case"bottom":return o.push(WA),w}else(iA(h)||JA(h))&&o.push(h);else if(AA(h))switch(h.value){case Vn:return r=0,!1;case Xn:return r=1,!1;case"at":return u=!0,!1;case Nn:return t=0,!1;case Jn:case Rn:return t=1,!1;case Wn:case Gn:return t=2,!1;case kn:return t=3,!1}else if(JA(h)||iA(h))return Array.isArray(t)||(t=[]),t.push(h),!1;return w},s)}if(s){var f=Qt(e,i);n.push(f)}}),{size:t,shape:r,stops:n,position:o,type:2}},Ut=function(e,A){var r=0,t=3,n=[],o=[];return KA(A).forEach(function(i,l){var s=!0;if(l===0?s=i.reduce(function(f,w){if(AA(w))switch(w.value){case"center":return o.push(Cr),!1;case"top":case"left":return o.push(BA),!1;case"right":case"bottom":return o.push(WA),!1}else if(iA(w)||JA(w))return o.push(w),!1;return f},s):l===1&&(s=i.reduce(function(f,w){if(AA(w))switch(w.value){case Vn:return r=0,!1;case Xn:return r=1,!1;case Wn:case Nn:return t=0,!1;case Rn:return t=1,!1;case Gn:return t=2,!1;case Jn:case kn:return t=3,!1}else if(JA(w)||iA(w))return Array.isArray(t)||(t=[]),t.push(w),!1;return f},s)),s){var u=Qt(e,i);n.push(u)}}),{size:t,shape:r,stops:n,position:o,type:2}},Es=function(e){return e.type===1},Hs=function(e){return e.type===2},pr={name:"image",parse:function(e,A){if(A.type===22){var r={url:A.value,type:0};return e.cache.addImage(A.value),r}if(A.type===18){var t=Yn[A.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported image function "'+A.name+'"');return t(e,A.values)}throw new Error("Unsupported image type "+A.type)}};function Is(e){return!(e.type===20&&e.value==="none")&&(e.type!==18||!!Yn[e.name])}for(var Yn={"linear-gradient":Fs,"-moz-linear-gradient":pt,"-ms-linear-gradient":pt,"-o-linear-gradient":pt,"-webkit-linear-gradient":pt,"radial-gradient":vs,"-moz-radial-gradient":Ut,"-ms-radial-gradient":Ut,"-o-radial-gradient":Ut,"-webkit-radial-gradient":Ut,"-webkit-gradient":ms},ys={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var r=A[0];return r.type===20&&r.value==="none"?[]:A.filter(function(t){return de(t)&&Is(t)}).map(function(t){return pr.parse(e,t)})}},bs={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(r){if(AA(r))switch(r.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Ls={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(e,A){return KA(A).map(function(r){return r.filter(iA)}).map(yn)}},xs={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(e,A){return KA(A).map(function(r){return r.filter(AA).map(function(t){return t.value}).join(" ")}).map(Ms)}},Ms=function(e){switch(e){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},Xe=function(e){return e.AUTO="auto",e.CONTAIN="contain",e.COVER="cover",e}(Xe||{}),Ks={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(e,A){return KA(A).map(function(r){return r.filter(Ss)})}},Ss=function(e){return AA(e)||iA(e)},Ft=function(e){return{name:"border-"+e+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},Ds=Ft("top"),Ts=Ft("right"),Os=Ft("bottom"),_s=Ft("left"),mt=function(e){return{name:"border-radius-"+e,initialValue:"0 0",prefix:!1,type:1,parse:function(A,r){return yn(r.filter(iA))}}},Ps=mt("top-left"),Ns=mt("top-right"),Rs=mt("bottom-right"),Gs=mt("bottom-left"),vt=function(e){return{name:"border-"+e+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,r){switch(r){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},ks=vt("top"),Vs=vt("right"),Xs=vt("bottom"),Js=vt("left"),Et=function(e){return{name:"border-"+e+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,r){return Ge(r)?r.number:0}}},Ws=Et("top"),Ys=Et("right"),Zs=Et("bottom"),$s=Et("left"),zs={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},qs={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(e,A){switch(A){case"rtl":return 1;case"ltr":default:return 0}}},js={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(e,A){return A.filter(AA).reduce(function(r,t){return r|Ac(t.value)},0)}},Ac=function(e){switch(e){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},ec={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},tc={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(e,A){return A.type===20&&A.value==="normal"?0:A.type===17||A.type===15?A.number:0}},Ur=function(e){return e.NORMAL="normal",e.STRICT="strict",e}(Ur||{}),rc={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"strict":return Ur.STRICT;case"normal":default:return Ur.NORMAL}}},nc={name:"line-height",initialValue:"normal",prefix:!1,type:4},Zn=function(e,A){return AA(e)&&e.value==="normal"?1.2*A:e.type===17?A*e.number:iA(e)?rA(e,A):A},oc={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(e,A){return A.type===20&&A.value==="none"?null:pr.parse(e,A)}},ic={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(e,A){switch(A){case"inside":return 0;case"outside":default:return 1}}},Fr={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},Ht=function(e){return{name:"margin-"+e,initialValue:"0",prefix:!1,type:4}},ac=Ht("top"),sc=Ht("right"),cc=Ht("bottom"),lc=Ht("left"),Bc={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(e,A){return A.filter(AA).map(function(r){switch(r.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},gc={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-word":return"break-word";case"normal":default:return"normal"}}},It=function(e){return{name:"padding-"+e,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},uc=It("top"),fc=It("right"),wc=It("bottom"),dc=It("left"),hc={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(e,A){switch(A){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},Cc={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(e,A){switch(A){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},Qc={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&hr(A[0],"none")?[]:KA(A).map(function(r){for(var t={color:_A.TRANSPARENT,offsetX:BA,offsetY:BA,blur:BA},n=0,o=0;o<r.length;o++){var i=r[o];JA(i)?(n===0?t.offsetX=i:n===1?t.offsetY=i:t.blur=i,n++):t.color=YA.parse(e,i)}return t})}},pc={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},Uc={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(e,A){if(A.type===20&&A.value==="none")return null;if(A.type===18){var r=vc[A.name];if(typeof r>"u")throw new Error('Attempting to parse an unsupported transform function "'+A.name+'"');return r(A.values)}return null}},Fc=function(e){var A=e.filter(function(r){return r.type===17}).map(function(r){return r.number});return A.length===6?A:null},mc=function(e){var A=e.filter(function(s){return s.type===17}).map(function(s){return s.number}),r=A[0],t=A[1];A[2],A[3];var n=A[4],o=A[5];A[6],A[7],A[8],A[9],A[10],A[11];var i=A[12],l=A[13];return A[14],A[15],A.length===16?[r,t,n,o,i,l]:null},vc={matrix:Fc,matrix3d:mc},$n={type:16,number:50,flags:Oe},Ec=[$n,$n],Hc={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(e,A){var r=A.filter(iA);return r.length!==2?Ec:[r[0],r[1]]}},Ic={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},yt=function(e){return e.NORMAL="normal",e.BREAK_ALL="break-all",e.KEEP_ALL="keep-all",e}(yt||{}),yc={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-all":return yt.BREAK_ALL;case"keep-all":return yt.KEEP_ALL;case"normal":default:return yt.NORMAL}}},bc={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(e,A){if(A.type===20)return{auto:!0,order:0};if(we(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},zn={name:"time",parse:function(e,A){if(A.type===15)switch(A.unit.toLowerCase()){case"s":return 1e3*A.number;case"ms":return A.number}throw new Error("Unsupported time type")}},Lc={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(e,A){return we(A)?A.number:1}},xc={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Mc={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(e,A){return A.filter(AA).map(function(r){switch(r.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(r){return r!==0})}},Kc={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(e,A){var r=[],t=[];return A.forEach(function(n){switch(n.type){case 20:case 0:r.push(n.value);break;case 17:r.push(n.number.toString());break;case 4:t.push(r.join(" ")),r.length=0;break}}),r.length&&t.push(r.join(" ")),t.map(function(n){return n.indexOf(" ")===-1?n:"'"+n+"'"})}},Sc={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Dc={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(e,A){if(we(A))return A.number;if(AA(A))switch(A.value){case"bold":return 700;case"normal":default:return 400}return 400}},Tc={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.filter(AA).map(function(r){return r.value})}},Oc={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},cA=function(e,A){return(e&A)!==0},_c={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var r=A[0];return r.type===20&&r.value==="none"?[]:A}},Pc={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var r=A[0];if(r.type===20&&r.value==="none")return null;for(var t=[],n=A.filter(In),o=0;o<n.length;o++){var i=n[o],l=n[o+1];if(i.type===20){var s=l&&we(l)?l.number:1;t.push({counter:i.value,increment:s})}}return t}},Nc={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return[];for(var r=[],t=A.filter(In),n=0;n<t.length;n++){var o=t[n],i=t[n+1];if(AA(o)&&o.value!=="none"){var l=i&&we(i)?i.number:0;r.push({counter:o.value,reset:l})}}return r}},Rc={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(e,A){return A.filter(Ge).map(function(r){return zn.parse(e,r)})}},Gc={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var r=A[0];if(r.type===20&&r.value==="none")return null;var t=[],n=A.filter(fs);if(n.length%2!==0)return null;for(var o=0;o<n.length;o+=2){var i=n[o].value,l=n[o+1].value;t.push({open:i,close:l})}return t}},qn=function(e,A,r){if(!e)return"";var t=e[Math.min(A,e.length-1)];return t?r?t.open:t.close:""},kc={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&hr(A[0],"none")?[]:KA(A).map(function(r){for(var t={color:255,offsetX:BA,offsetY:BA,blur:BA,spread:BA,inset:!1},n=0,o=0;o<r.length;o++){var i=r[o];hr(i,"inset")?t.inset=!0:JA(i)?(n===0?t.offsetX=i:n===1?t.offsetY=i:n===2?t.blur=i:t.spread=i,n++):t.color=YA.parse(e,i)}return t})}},Vc={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(e,A){var r=[0,1,2],t=[];return A.filter(AA).forEach(function(n){switch(n.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2);break}}),r.forEach(function(n){t.indexOf(n)===-1&&t.push(n)}),t}},Xc={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},Jc={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(e,A){return Ge(A)?A.number:0}},Wc=function(){function e(A,r){var t,n;this.animationDuration=x(A,Rc,r.animationDuration),this.backgroundClip=x(A,hs,r.backgroundClip),this.backgroundColor=x(A,Cs,r.backgroundColor),this.backgroundImage=x(A,ys,r.backgroundImage),this.backgroundOrigin=x(A,bs,r.backgroundOrigin),this.backgroundPosition=x(A,Ls,r.backgroundPosition),this.backgroundRepeat=x(A,xs,r.backgroundRepeat),this.backgroundSize=x(A,Ks,r.backgroundSize),this.borderTopColor=x(A,Ds,r.borderTopColor),this.borderRightColor=x(A,Ts,r.borderRightColor),this.borderBottomColor=x(A,Os,r.borderBottomColor),this.borderLeftColor=x(A,_s,r.borderLeftColor),this.borderTopLeftRadius=x(A,Ps,r.borderTopLeftRadius),this.borderTopRightRadius=x(A,Ns,r.borderTopRightRadius),this.borderBottomRightRadius=x(A,Rs,r.borderBottomRightRadius),this.borderBottomLeftRadius=x(A,Gs,r.borderBottomLeftRadius),this.borderTopStyle=x(A,ks,r.borderTopStyle),this.borderRightStyle=x(A,Vs,r.borderRightStyle),this.borderBottomStyle=x(A,Xs,r.borderBottomStyle),this.borderLeftStyle=x(A,Js,r.borderLeftStyle),this.borderTopWidth=x(A,Ws,r.borderTopWidth),this.borderRightWidth=x(A,Ys,r.borderRightWidth),this.borderBottomWidth=x(A,Zs,r.borderBottomWidth),this.borderLeftWidth=x(A,$s,r.borderLeftWidth),this.boxShadow=x(A,kc,r.boxShadow),this.color=x(A,zs,r.color),this.direction=x(A,qs,r.direction),this.display=x(A,js,r.display),this.float=x(A,ec,r.cssFloat),this.fontFamily=x(A,Kc,r.fontFamily),this.fontSize=x(A,Sc,r.fontSize),this.fontStyle=x(A,Oc,r.fontStyle),this.fontVariant=x(A,Tc,r.fontVariant),this.fontWeight=x(A,Dc,r.fontWeight),this.letterSpacing=x(A,tc,r.letterSpacing),this.lineBreak=x(A,rc,r.lineBreak),this.lineHeight=x(A,nc,r.lineHeight),this.listStyleImage=x(A,oc,r.listStyleImage),this.listStylePosition=x(A,ic,r.listStylePosition),this.listStyleType=x(A,Fr,r.listStyleType),this.marginTop=x(A,ac,r.marginTop),this.marginRight=x(A,sc,r.marginRight),this.marginBottom=x(A,cc,r.marginBottom),this.marginLeft=x(A,lc,r.marginLeft),this.opacity=x(A,Lc,r.opacity);var o=x(A,Bc,r.overflow);this.overflowX=o[0],this.overflowY=o[o.length>1?1:0],this.overflowWrap=x(A,gc,r.overflowWrap),this.paddingTop=x(A,uc,r.paddingTop),this.paddingRight=x(A,fc,r.paddingRight),this.paddingBottom=x(A,wc,r.paddingBottom),this.paddingLeft=x(A,dc,r.paddingLeft),this.paintOrder=x(A,Vc,r.paintOrder),this.position=x(A,Cc,r.position),this.textAlign=x(A,hc,r.textAlign),this.textDecorationColor=x(A,xc,(t=r.textDecorationColor)!==null&&t!==void 0?t:r.color),this.textDecorationLine=x(A,Mc,(n=r.textDecorationLine)!==null&&n!==void 0?n:r.textDecoration),this.textShadow=x(A,Qc,r.textShadow),this.textTransform=x(A,pc,r.textTransform),this.transform=x(A,Uc,r.transform),this.transformOrigin=x(A,Hc,r.transformOrigin),this.visibility=x(A,Ic,r.visibility),this.webkitTextStrokeColor=x(A,Xc,r.webkitTextStrokeColor),this.webkitTextStrokeWidth=x(A,Jc,r.webkitTextStrokeWidth),this.wordBreak=x(A,yc,r.wordBreak),this.zIndex=x(A,bc,r.zIndex)}return e.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},e.prototype.isTransparent=function(){return ZA(this.backgroundColor)},e.prototype.isTransformed=function(){return this.transform!==null},e.prototype.isPositioned=function(){return this.position!==0},e.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},e.prototype.isFloating=function(){return this.float!==0},e.prototype.isInlineLevel=function(){return cA(this.display,4)||cA(this.display,33554432)||cA(this.display,268435456)||cA(this.display,536870912)||cA(this.display,67108864)||cA(this.display,134217728)},e}(),Yc=function(){function e(A,r){this.content=x(A,_c,r.content),this.quotes=x(A,Gc,r.quotes)}return e}(),jn=function(){function e(A,r){this.counterIncrement=x(A,Pc,r.counterIncrement),this.counterReset=x(A,Nc,r.counterReset)}return e}(),x=function(e,A,r){var t=new En,n=r!==null&&typeof r<"u"?r.toString():A.initialValue;t.write(n);var o=new Hn(t.read());switch(A.type){case 2:var i=o.parseComponentValue();return A.parse(e,AA(i)?i.value:A.initialValue);case 0:return A.parse(e,o.parseComponentValue());case 1:return A.parse(e,o.parseComponentValues());case 4:return o.parseComponentValue();case 3:switch(A.format){case"angle":return Ct.parse(e,o.parseComponentValue());case"color":return YA.parse(e,o.parseComponentValue());case"image":return pr.parse(e,o.parseComponentValue());case"length":var l=o.parseComponentValue();return JA(l)?l:BA;case"length-percentage":var s=o.parseComponentValue();return iA(s)?s:BA;case"time":return zn.parse(e,o.parseComponentValue())}break}},Zc="data-html2canvas-debug",$c=function(e){var A=e.getAttribute(Zc);switch(A){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},mr=function(e,A){var r=$c(e);return r===1||A===r},SA=function(){function e(A,r){if(this.context=A,this.textNodes=[],this.elements=[],this.flags=0,mr(r,3))debugger;this.styles=new Wc(A,window.getComputedStyle(r,null)),Or(r)&&(this.styles.animationDuration.some(function(t){return t>0})&&(r.style.animationDuration="0s"),this.styles.transform!==null&&(r.style.transform="none")),this.bounds=Q(this.context,r),mr(r,4)&&(this.flags|=16)}return e}(),zc="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",Ao="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Je=typeof Uint8Array>"u"?[]:new Uint8Array(256),bt=0;bt<Ao.length;bt++)Je[Ao.charCodeAt(bt)]=bt;for(var qc=function(e){var A=e.length*.75,r=e.length,t,n=0,o,i,l,s;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var u=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),f=Array.isArray(u)?u:new Uint8Array(u);for(t=0;t<r;t+=4)o=Je[e.charCodeAt(t)],i=Je[e.charCodeAt(t+1)],l=Je[e.charCodeAt(t+2)],s=Je[e.charCodeAt(t+3)],f[n++]=o<<2|i>>4,f[n++]=(i&15)<<4|l>>2,f[n++]=(l&3)<<6|s&63;return u},jc=function(e){for(var A=e.length,r=[],t=0;t<A;t+=2)r.push(e[t+1]<<8|e[t]);return r},Al=function(e){for(var A=e.length,r=[],t=0;t<A;t+=4)r.push(e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]);return r},ie=5,vr=11,Er=2,el=vr-ie,eo=65536>>ie,tl=1<<ie,Hr=tl-1,rl=1024>>ie,nl=eo+rl,ol=nl,il=32,al=ol+il,sl=65536>>vr,cl=1<<el,ll=cl-1,to=function(e,A,r){return e.slice?e.slice(A,r):new Uint16Array(Array.prototype.slice.call(e,A,r))},Bl=function(e,A,r){return e.slice?e.slice(A,r):new Uint32Array(Array.prototype.slice.call(e,A,r))},gl=function(e,A){var r=qc(e),t=Array.isArray(r)?Al(r):new Uint32Array(r),n=Array.isArray(r)?jc(r):new Uint16Array(r),o=24,i=to(n,o/2,t[4]/2),l=t[5]===2?to(n,(o+t[4])/2):Bl(t,Math.ceil((o+t[4])/4));return new ul(t[0],t[1],t[2],t[3],i,l)},ul=function(){function e(A,r,t,n,o,i){this.initialValue=A,this.errorValue=r,this.highStart=t,this.highValueIndex=n,this.index=o,this.data=i}return e.prototype.get=function(A){var r;if(A>=0){if(A<55296||A>56319&&A<=65535)return r=this.index[A>>ie],r=(r<<Er)+(A&Hr),this.data[r];if(A<=65535)return r=this.index[eo+(A-55296>>ie)],r=(r<<Er)+(A&Hr),this.data[r];if(A<this.highStart)return r=al-sl+(A>>vr),r=this.index[r],r+=A>>ie&ll,r=this.index[r],r=(r<<Er)+(A&Hr),this.data[r];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),ro="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",fl=typeof Uint8Array>"u"?[]:new Uint8Array(256),Lt=0;Lt<ro.length;Lt++)fl[ro.charCodeAt(Lt)]=Lt;var wl=1,Ir=2,yr=3,no=4,oo=5,dl=7,io=8,br=9,Lr=10,ao=11,so=12,co=13,lo=14,xr=15,hl=function(e){for(var A=[],r=0,t=e.length;r<t;){var n=e.charCodeAt(r++);if(n>=55296&&n<=56319&&r<t){var o=e.charCodeAt(r++);(o&64512)===56320?A.push(((n&1023)<<10)+(o&1023)+65536):(A.push(n),r--)}else A.push(n)}return A},Cl=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var r=e.length;if(!r)return"";for(var t=[],n=-1,o="";++n<r;){var i=e[n];i<=65535?t.push(i):(i-=65536,t.push((i>>10)+55296,i%1024+56320)),(n+1===r||t.length>16384)&&(o+=String.fromCharCode.apply(String,t),t.length=0)}return o},Ql=gl(zc),EA="\xD7",Mr="\xF7",pl=function(e){return Ql.get(e)},Ul=function(e,A,r){var t=r-2,n=A[t],o=A[r-1],i=A[r];if(o===Ir&&i===yr)return EA;if(o===Ir||o===yr||o===no||i===Ir||i===yr||i===no)return Mr;if(o===io&&[io,br,ao,so].indexOf(i)!==-1||(o===ao||o===br)&&(i===br||i===Lr)||(o===so||o===Lr)&&i===Lr||i===co||i===oo||i===dl||o===wl)return EA;if(o===co&&i===lo){for(;n===oo;)n=A[--t];if(n===lo)return EA}if(o===xr&&i===xr){for(var l=0;n===xr;)l++,n=A[--t];if(l%2===0)return EA}return Mr},Fl=function(e){var A=hl(e),r=A.length,t=0,n=0,o=A.map(pl);return{next:function(){if(t>=r)return{done:!0,value:null};for(var i=EA;t<r&&(i=Ul(A,o,++t))===EA;);if(i!==EA||t===r){var l=Cl.apply(null,A.slice(n,t));return n=t,{value:l,done:!1}}return{done:!0,value:null}}}},ml=function(e){for(var A=Fl(e),r=[],t;!(t=A.next()).done;)t.value&&r.push(t.value.slice());return r},vl=function(e){var A=123;if(e.createRange){var r=e.createRange();if(r.getBoundingClientRect){var t=e.createElement("boundtest");t.style.height=A+"px",t.style.display="block",e.body.appendChild(t),r.selectNode(t);var n=r.getBoundingClientRect(),o=Math.round(n.height);if(e.body.removeChild(t),o===A)return!0}}return!1},El=function(e){var A=e.createElement("boundtest");A.style.width="50px",A.style.display="block",A.style.fontSize="12px",A.style.letterSpacing="0px",A.style.wordSpacing="0px",e.body.appendChild(A);var r=e.createRange();A.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var t=A.firstChild,n=U(t.data).map(function(s){return d(s)}),o=0,i={},l=n.every(function(s,u){r.setStart(t,o),r.setEnd(t,o+s.length);var f=r.getBoundingClientRect();o+=s.length;var w=f.x>i.x||f.y>i.y;return i=f,u===0?!0:w});return e.body.removeChild(A),l},Hl=function(){return typeof new Image().crossOrigin<"u"},Il=function(){return typeof new XMLHttpRequest().responseType=="string"},yl=function(e){var A=new Image,r=e.createElement("canvas"),t=r.getContext("2d");if(!t)return!1;A.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{t.drawImage(A,0,0),r.toDataURL()}catch{return!1}return!0},Bo=function(e){return e[0]===0&&e[1]===255&&e[2]===0&&e[3]===255},bl=function(e){var A=e.createElement("canvas"),r=100;A.width=r,A.height=r;var t=A.getContext("2d");if(!t)return Promise.reject(!1);t.fillStyle="rgb(0, 255, 0)",t.fillRect(0,0,r,r);var n=new Image,o=A.toDataURL();n.src=o;var i=Kr(r,r,0,0,n);return t.fillStyle="red",t.fillRect(0,0,r,r),go(i).then(function(l){t.drawImage(l,0,0);var s=t.getImageData(0,0,r,r).data;t.fillStyle="red",t.fillRect(0,0,r,r);var u=e.createElement("div");return u.style.backgroundImage="url("+o+")",u.style.height=r+"px",Bo(s)?go(Kr(r,r,0,0,u)):Promise.reject(!1)}).then(function(l){return t.drawImage(l,0,0),Bo(t.getImageData(0,0,r,r).data)}).catch(function(){return!1})},Kr=function(e,A,r,t,n){var o="http://www.w3.org/2000/svg",i=document.createElementNS(o,"svg"),l=document.createElementNS(o,"foreignObject");return i.setAttributeNS(null,"width",e.toString()),i.setAttributeNS(null,"height",A.toString()),l.setAttributeNS(null,"width","100%"),l.setAttributeNS(null,"height","100%"),l.setAttributeNS(null,"x",r.toString()),l.setAttributeNS(null,"y",t.toString()),l.setAttributeNS(null,"externalResourcesRequired","true"),i.appendChild(l),l.appendChild(n),i},go=function(e){return new Promise(function(A,r){var t=new Image;t.onload=function(){return A(t)},t.onerror=r,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},gA={get SUPPORT_RANGE_BOUNDS(){var e=vl(document);return Object.defineProperty(gA,"SUPPORT_RANGE_BOUNDS",{value:e}),e},get SUPPORT_WORD_BREAKING(){var e=gA.SUPPORT_RANGE_BOUNDS&&El(document);return Object.defineProperty(gA,"SUPPORT_WORD_BREAKING",{value:e}),e},get SUPPORT_SVG_DRAWING(){var e=yl(document);return Object.defineProperty(gA,"SUPPORT_SVG_DRAWING",{value:e}),e},get SUPPORT_FOREIGNOBJECT_DRAWING(){var e=typeof Array.from=="function"&&typeof window.fetch=="function"?bl(document):Promise.resolve(!1);return Object.defineProperty(gA,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:e}),e},get SUPPORT_CORS_IMAGES(){var e=Hl();return Object.defineProperty(gA,"SUPPORT_CORS_IMAGES",{value:e}),e},get SUPPORT_RESPONSE_TYPE(){var e=Il();return Object.defineProperty(gA,"SUPPORT_RESPONSE_TYPE",{value:e}),e},get SUPPORT_CORS_XHR(){var e="withCredentials"in new XMLHttpRequest;return Object.defineProperty(gA,"SUPPORT_CORS_XHR",{value:e}),e},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var e=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty(gA,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:e}),e}},We=function(){function e(A,r){this.text=A,this.bounds=r}return e}(),Ll=function(e,A,r,t){var n=Kl(A,r),o=[],i=0;return n.forEach(function(l){if(r.textDecorationLine.length||l.trim().length>0)if(gA.SUPPORT_RANGE_BOUNDS){var s=uo(t,i,l.length).getClientRects();if(s.length>1){var u=Sr(l),f=0;u.forEach(function(h){o.push(new We(h,g.fromDOMRectList(e,uo(t,f+i,h.length).getClientRects()))),f+=h.length})}else o.push(new We(l,g.fromDOMRectList(e,s)))}else{var w=t.splitText(l.length);o.push(new We(l,xl(e,t))),t=w}else gA.SUPPORT_RANGE_BOUNDS||(t=t.splitText(l.length));i+=l.length}),o},xl=function(e,A){var r=A.ownerDocument;if(r){var t=r.createElement("html2canvaswrapper");t.appendChild(A.cloneNode(!0));var n=A.parentNode;if(n){n.replaceChild(t,A);var o=Q(e,t);return t.firstChild&&n.replaceChild(t.firstChild,t),o}}return g.EMPTY},uo=function(e,A,r){var t=e.ownerDocument;if(!t)throw new Error("Node has no owner document");var n=t.createRange();return n.setStart(e,A),n.setEnd(e,A+r),n},Sr=function(e){if(gA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var A=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(A.segment(e)).map(function(r){return r.segment})}return ml(e)},Ml=function(e,A){if(gA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var r=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(r.segment(e)).map(function(t){return t.segment})}return Dl(e,A)},Kl=function(e,A){return A.letterSpacing!==0?Sr(e):Ml(e,A)},Sl=[32,160,4961,65792,65793,4153,4241],Dl=function(e,A){for(var r=sa(e,{lineBreak:A.lineBreak,wordBreak:A.overflowWrap==="break-word"?"break-word":A.wordBreak}),t=[],n,o=function(){if(n.value){var i=n.value.slice(),l=U(i),s="";l.forEach(function(u){Sl.indexOf(u)===-1?s+=d(u):(s.length&&t.push(s),t.push(d(u)),s="")}),s.length&&t.push(s)}};!(n=r.next()).done;)o();return t},Tl=function(){function e(A,r,t){this.text=Ol(r.data,t.textTransform),this.textBounds=Ll(A,this.text,t,r)}return e}(),Ol=function(e,A){switch(A){case 1:return e.toLowerCase();case 3:return e.replace(_l,Pl);case 2:return e.toUpperCase();default:return e}},_l=/(^|\s|:|-|\(|\))([a-z])/g,Pl=function(e,A,r){return e.length>0?A+r.toUpperCase():e},fo=function(e){S(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.src=t.currentSrc||t.src,n.intrinsicWidth=t.naturalWidth,n.intrinsicHeight=t.naturalHeight,n.context.cache.addImage(n.src),n}return A}(SA),wo=function(e){S(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.canvas=t,n.intrinsicWidth=t.width,n.intrinsicHeight=t.height,n}return A}(SA),ho=function(e){S(A,e);function A(r,t){var n=e.call(this,r,t)||this,o=new XMLSerializer,i=Q(r,t);return t.setAttribute("width",i.width+"px"),t.setAttribute("height",i.height+"px"),n.svg="data:image/svg+xml,"+encodeURIComponent(o.serializeToString(t)),n.intrinsicWidth=t.width.baseVal.value,n.intrinsicHeight=t.height.baseVal.value,n.context.cache.addImage(n.svg),n}return A}(SA),Co=function(e){S(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.value=t.value,n}return A}(SA),Dr=function(e){S(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.start=t.start,n.reversed=typeof t.reversed=="boolean"&&t.reversed===!0,n}return A}(SA),Nl=[{type:15,flags:0,unit:"px",number:3}],Rl=[{type:16,flags:0,number:50}],Gl=function(e){return e.width>e.height?new g(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new g(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e},kl=function(e){var A=e.type===Vl?new Array(e.value.length+1).join("\u2022"):e.value;return A.length===0?e.placeholder||"":A},xt="checkbox",Mt="radio",Vl="password",Qo=707406591,Tr=function(e){S(A,e);function A(r,t){var n=e.call(this,r,t)||this;switch(n.type=t.type.toLowerCase(),n.checked=t.checked,n.value=kl(t),(n.type===xt||n.type===Mt)&&(n.styles.backgroundColor=3739148031,n.styles.borderTopColor=n.styles.borderRightColor=n.styles.borderBottomColor=n.styles.borderLeftColor=2779096575,n.styles.borderTopWidth=n.styles.borderRightWidth=n.styles.borderBottomWidth=n.styles.borderLeftWidth=1,n.styles.borderTopStyle=n.styles.borderRightStyle=n.styles.borderBottomStyle=n.styles.borderLeftStyle=1,n.styles.backgroundClip=[0],n.styles.backgroundOrigin=[0],n.bounds=Gl(n.bounds)),n.type){case xt:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=Nl;break;case Mt:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=Rl;break}return n}return A}(SA),po=function(e){S(A,e);function A(r,t){var n=e.call(this,r,t)||this,o=t.options[t.selectedIndex||0];return n.value=o&&o.text||"",n}return A}(SA),Uo=function(e){S(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.value=t.value,n}return A}(SA),Fo=function(e){S(A,e);function A(r,t){var n=e.call(this,r,t)||this;n.src=t.src,n.width=parseInt(t.width,10)||0,n.height=parseInt(t.height,10)||0,n.backgroundColor=n.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){n.tree=vo(r,t.contentWindow.document.documentElement);var o=t.contentWindow.document.documentElement?Ve(r,getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):_A.TRANSPARENT,i=t.contentWindow.document.body?Ve(r,getComputedStyle(t.contentWindow.document.body).backgroundColor):_A.TRANSPARENT;n.backgroundColor=ZA(o)?ZA(i)?n.styles.backgroundColor:i:o}}catch{}return n}return A}(SA),Xl=["OL","UL","MENU"],Kt=function(e,A,r,t){for(var n=A.firstChild,o=void 0;n;n=o)if(o=n.nextSibling,Eo(n)&&n.data.trim().length>0)r.textNodes.push(new Tl(e,n,r.styles));else if(he(n))if(xo(n)&&n.assignedNodes)n.assignedNodes().forEach(function(l){return Kt(e,l,r,t)});else{var i=mo(e,n);i.styles.isVisible()&&(Jl(n,i,t)?i.flags|=4:Wl(i.styles)&&(i.flags|=2),Xl.indexOf(n.tagName)!==-1&&(i.flags|=8),r.elements.push(i),n.slot,n.shadowRoot?Kt(e,n.shadowRoot,i,t):!Dt(n)&&!Ho(n)&&!Tt(n)&&Kt(e,n,i,t))}},mo=function(e,A){return Pr(A)?new fo(e,A):Io(A)?new wo(e,A):Ho(A)?new ho(e,A):Yl(A)?new Co(e,A):Zl(A)?new Dr(e,A):$l(A)?new Tr(e,A):Tt(A)?new po(e,A):Dt(A)?new Uo(e,A):bo(A)?new Fo(e,A):new SA(e,A)},vo=function(e,A){var r=mo(e,A);return r.flags|=4,Kt(e,A,r,r),r},Jl=function(e,A,r){return A.styles.isPositionedWithZIndex()||A.styles.opacity<1||A.styles.isTransformed()||_r(e)&&r.styles.isTransparent()},Wl=function(e){return e.isPositioned()||e.isFloating()},Eo=function(e){return e.nodeType===Node.TEXT_NODE},he=function(e){return e.nodeType===Node.ELEMENT_NODE},Or=function(e){return he(e)&&typeof e.style<"u"&&!St(e)},St=function(e){return typeof e.className=="object"},Yl=function(e){return e.tagName==="LI"},Zl=function(e){return e.tagName==="OL"},$l=function(e){return e.tagName==="INPUT"},zl=function(e){return e.tagName==="HTML"},Ho=function(e){return e.tagName==="svg"},_r=function(e){return e.tagName==="BODY"},Io=function(e){return e.tagName==="CANVAS"},yo=function(e){return e.tagName==="VIDEO"},Pr=function(e){return e.tagName==="IMG"},bo=function(e){return e.tagName==="IFRAME"},Lo=function(e){return e.tagName==="STYLE"},ql=function(e){return e.tagName==="SCRIPT"},Dt=function(e){return e.tagName==="TEXTAREA"},Tt=function(e){return e.tagName==="SELECT"},xo=function(e){return e.tagName==="SLOT"},Mo=function(e){return e.tagName.indexOf("-")>0},jl=function(){function e(){this.counters={}}return e.prototype.getCounterValue=function(A){var r=this.counters[A];return r&&r.length?r[r.length-1]:1},e.prototype.getCounterValues=function(A){var r=this.counters[A];return r||[]},e.prototype.pop=function(A){var r=this;A.forEach(function(t){return r.counters[t].pop()})},e.prototype.parse=function(A){var r=this,t=A.counterIncrement,n=A.counterReset,o=!0;t!==null&&t.forEach(function(l){var s=r.counters[l.counter];s&&l.increment!==0&&(o=!1,s.length||s.push(1),s[Math.max(0,s.length-1)]+=l.increment)});var i=[];return o&&n.forEach(function(l){var s=r.counters[l.counter];i.push(l.counter),s||(s=r.counters[l.counter]=[]),s.push(l.reset)}),i},e}(),Ko={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},So={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u0554","\u0553","\u0552","\u0551","\u0550","\u054F","\u054E","\u054D","\u054C","\u054B","\u054A","\u0549","\u0548","\u0547","\u0546","\u0545","\u0544","\u0543","\u0542","\u0541","\u0540","\u053F","\u053E","\u053D","\u053C","\u053B","\u053A","\u0539","\u0538","\u0537","\u0536","\u0535","\u0534","\u0533","\u0532","\u0531"]},AB={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["\u05D9\u05F3","\u05D8\u05F3","\u05D7\u05F3","\u05D6\u05F3","\u05D5\u05F3","\u05D4\u05F3","\u05D3\u05F3","\u05D2\u05F3","\u05D1\u05F3","\u05D0\u05F3","\u05EA","\u05E9","\u05E8","\u05E7","\u05E6","\u05E4","\u05E2","\u05E1","\u05E0","\u05DE","\u05DC","\u05DB","\u05D9\u05D8","\u05D9\u05D7","\u05D9\u05D6","\u05D8\u05D6","\u05D8\u05D5","\u05D9","\u05D8","\u05D7","\u05D6","\u05D5","\u05D4","\u05D3","\u05D2","\u05D1","\u05D0"]},eB={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u10F5","\u10F0","\u10EF","\u10F4","\u10EE","\u10ED","\u10EC","\u10EB","\u10EA","\u10E9","\u10E8","\u10E7","\u10E6","\u10E5","\u10E4","\u10F3","\u10E2","\u10E1","\u10E0","\u10DF","\u10DE","\u10DD","\u10F2","\u10DC","\u10DB","\u10DA","\u10D9","\u10D8","\u10D7","\u10F1","\u10D6","\u10D5","\u10D4","\u10D3","\u10D2","\u10D1","\u10D0"]},Ce=function(e,A,r,t,n,o){return e<A||e>r?Ze(e,n,o.length>0):t.integers.reduce(function(i,l,s){for(;e>=l;)e-=l,i+=t.values[s];return i},"")+o},Do=function(e,A,r,t){var n="";do r||e--,n=t(e)+n,e/=A;while(e*A>=A);return n},oA=function(e,A,r,t,n){var o=r-A+1;return(e<0?"-":"")+(Do(Math.abs(e),o,t,function(i){return d(Math.floor(i%o)+A)})+n)},ae=function(e,A,r){r===void 0&&(r=". ");var t=A.length;return Do(Math.abs(e),t,!1,function(n){return A[Math.floor(n%t)]})+r},Qe=1,zA=2,qA=4,Ye=8,PA=function(e,A,r,t,n,o){if(e<-9999||e>9999)return Ze(e,4,n.length>0);var i=Math.abs(e),l=n;if(i===0)return A[0]+l;for(var s=0;i>0&&s<=4;s++){var u=i%10;u===0&&cA(o,Qe)&&l!==""?l=A[u]+l:u>1||u===1&&s===0||u===1&&s===1&&cA(o,zA)||u===1&&s===1&&cA(o,qA)&&e>100||u===1&&s>1&&cA(o,Ye)?l=A[u]+(s>0?r[s-1]:"")+l:u===1&&s>0&&(l=r[s-1]+l),i=Math.floor(i/10)}return(e<0?t:"")+l},To="\u5341\u767E\u5343\u842C",Oo="\u62FE\u4F70\u4EDF\u842C",_o="\u30DE\u30A4\u30CA\u30B9",Nr="\uB9C8\uC774\uB108\uC2A4",Ze=function(e,A,r){var t=r?". ":"",n=r?"\u3001":"",o=r?", ":"",i=r?" ":"";switch(A){case 0:return"\u2022"+i;case 1:return"\u25E6"+i;case 2:return"\u25FE"+i;case 5:var l=oA(e,48,57,!0,t);return l.length<4?"0"+l:l;case 4:return ae(e,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",n);case 6:return Ce(e,1,3999,Ko,3,t).toLowerCase();case 7:return Ce(e,1,3999,Ko,3,t);case 8:return oA(e,945,969,!1,t);case 9:return oA(e,97,122,!1,t);case 10:return oA(e,65,90,!1,t);case 11:return oA(e,1632,1641,!0,t);case 12:case 49:return Ce(e,1,9999,So,3,t);case 35:return Ce(e,1,9999,So,3,t).toLowerCase();case 13:return oA(e,2534,2543,!0,t);case 14:case 30:return oA(e,6112,6121,!0,t);case 15:return ae(e,"\u5B50\u4E11\u5BC5\u536F\u8FB0\u5DF3\u5348\u672A\u7533\u9149\u620C\u4EA5",n);case 16:return ae(e,"\u7532\u4E59\u4E19\u4E01\u620A\u5DF1\u5E9A\u8F9B\u58EC\u7678",n);case 17:case 48:return PA(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",To,"\u8CA0",n,zA|qA|Ye);case 47:return PA(e,"\u96F6\u58F9\u8CB3\u53C3\u8086\u4F0D\u9678\u67D2\u634C\u7396",Oo,"\u8CA0",n,Qe|zA|qA|Ye);case 42:return PA(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",To,"\u8D1F",n,zA|qA|Ye);case 41:return PA(e,"\u96F6\u58F9\u8D30\u53C1\u8086\u4F0D\u9646\u67D2\u634C\u7396",Oo,"\u8D1F",n,Qe|zA|qA|Ye);case 26:return PA(e,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u4E07",_o,n,0);case 25:return PA(e,"\u96F6\u58F1\u5F10\u53C2\u56DB\u4F0D\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343\u4E07",_o,n,Qe|zA|qA);case 31:return PA(e,"\uC601\uC77C\uC774\uC0BC\uC0AC\uC624\uC721\uCE60\uD314\uAD6C","\uC2ED\uBC31\uCC9C\uB9CC",Nr,o,Qe|zA|qA);case 33:return PA(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u842C",Nr,o,0);case 32:return PA(e,"\u96F6\u58F9\u8CB3\u53C3\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343",Nr,o,Qe|zA|qA);case 18:return oA(e,2406,2415,!0,t);case 20:return Ce(e,1,19999,eB,3,t);case 21:return oA(e,2790,2799,!0,t);case 22:return oA(e,2662,2671,!0,t);case 22:return Ce(e,1,10999,AB,3,t);case 23:return ae(e,"\u3042\u3044\u3046\u3048\u304A\u304B\u304D\u304F\u3051\u3053\u3055\u3057\u3059\u305B\u305D\u305F\u3061\u3064\u3066\u3068\u306A\u306B\u306C\u306D\u306E\u306F\u3072\u3075\u3078\u307B\u307E\u307F\u3080\u3081\u3082\u3084\u3086\u3088\u3089\u308A\u308B\u308C\u308D\u308F\u3090\u3091\u3092\u3093");case 24:return ae(e,"\u3044\u308D\u306F\u306B\u307B\u3078\u3068\u3061\u308A\u306C\u308B\u3092\u308F\u304B\u3088\u305F\u308C\u305D\u3064\u306D\u306A\u3089\u3080\u3046\u3090\u306E\u304A\u304F\u3084\u307E\u3051\u3075\u3053\u3048\u3066\u3042\u3055\u304D\u3086\u3081\u307F\u3057\u3091\u3072\u3082\u305B\u3059");case 27:return oA(e,3302,3311,!0,t);case 28:return ae(e,"\u30A2\u30A4\u30A6\u30A8\u30AA\u30AB\u30AD\u30AF\u30B1\u30B3\u30B5\u30B7\u30B9\u30BB\u30BD\u30BF\u30C1\u30C4\u30C6\u30C8\u30CA\u30CB\u30CC\u30CD\u30CE\u30CF\u30D2\u30D5\u30D8\u30DB\u30DE\u30DF\u30E0\u30E1\u30E2\u30E4\u30E6\u30E8\u30E9\u30EA\u30EB\u30EC\u30ED\u30EF\u30F0\u30F1\u30F2\u30F3",n);case 29:return ae(e,"\u30A4\u30ED\u30CF\u30CB\u30DB\u30D8\u30C8\u30C1\u30EA\u30CC\u30EB\u30F2\u30EF\u30AB\u30E8\u30BF\u30EC\u30BD\u30C4\u30CD\u30CA\u30E9\u30E0\u30A6\u30F0\u30CE\u30AA\u30AF\u30E4\u30DE\u30B1\u30D5\u30B3\u30A8\u30C6\u30A2\u30B5\u30AD\u30E6\u30E1\u30DF\u30B7\u30F1\u30D2\u30E2\u30BB\u30B9",n);case 34:return oA(e,3792,3801,!0,t);case 37:return oA(e,6160,6169,!0,t);case 38:return oA(e,4160,4169,!0,t);case 39:return oA(e,2918,2927,!0,t);case 40:return oA(e,1776,1785,!0,t);case 43:return oA(e,3046,3055,!0,t);case 44:return oA(e,3174,3183,!0,t);case 45:return oA(e,3664,3673,!0,t);case 46:return oA(e,3872,3881,!0,t);case 3:default:return oA(e,48,57,!0,t)}},Po="data-html2canvas-ignore",No=function(){function e(A,r,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=r,this.counters=new jl,this.quoteDepth=0,!r.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(r.ownerDocument.documentElement,!1)}return e.prototype.toIFrame=function(A,r){var t=this,n=tB(A,r);if(!n.contentWindow)return Promise.reject("Unable to find iframe window");var o=A.defaultView.pageXOffset,i=A.defaultView.pageYOffset,l=n.contentWindow,s=l.document,u=oB(n).then(function(){return a(t,void 0,void 0,function(){var f,w;return c(this,function(h){switch(h.label){case 0:return this.scrolledElements.forEach(cB),l&&(l.scrollTo(r.left,r.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(l.scrollY!==r.top||l.scrollX!==r.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(l.scrollX-r.left,l.scrollY-r.top,0,0))),f=this.options.onclone,w=this.clonedReferenceElement,typeof w>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:s.fonts&&s.fonts.ready?[4,s.fonts.ready]:[3,2];case 1:h.sent(),h.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,nB(s)]:[3,4];case 3:h.sent(),h.label=4;case 4:return typeof f=="function"?[2,Promise.resolve().then(function(){return f(s,w)}).then(function(){return n})]:[2,n]}})})});return s.open(),s.write(aB(document.doctype)+"<html></html>"),sB(this.referenceElement.ownerDocument,o,i),s.replaceChild(s.adoptNode(this.documentElement),s.documentElement),s.close(),u},e.prototype.createElementClone=function(A){if(mr(A,2))debugger;if(Io(A))return this.createCanvasClone(A);if(yo(A))return this.createVideoClone(A);if(Lo(A))return this.createStyleClone(A);var r=A.cloneNode(!1);return Pr(r)&&(Pr(A)&&A.currentSrc&&A.currentSrc!==A.src&&(r.src=A.currentSrc,r.srcset=""),r.loading==="lazy"&&(r.loading="eager")),Mo(r)?this.createCustomElementClone(r):r},e.prototype.createCustomElementClone=function(A){var r=document.createElement("html2canvascustomelement");return Rr(A.style,r),r},e.prototype.createStyleClone=function(A){try{var r=A.sheet;if(r&&r.cssRules){var t=[].slice.call(r.cssRules,0).reduce(function(o,i){return i&&typeof i.cssText=="string"?o+i.cssText:o},""),n=A.cloneNode(!1);return n.textContent=t,n}}catch(o){if(this.context.logger.error("Unable to access cssRules property",o),o.name!=="SecurityError")throw o}return A.cloneNode(!1)},e.prototype.createCanvasClone=function(A){var r;if(this.options.inlineImages&&A.ownerDocument){var t=A.ownerDocument.createElement("img");try{return t.src=A.toDataURL(),t}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var n=A.cloneNode(!1);try{n.width=A.width,n.height=A.height;var o=A.getContext("2d"),i=n.getContext("2d");if(i)if(!this.options.allowTaint&&o)i.putImageData(o.getImageData(0,0,A.width,A.height),0,0);else{var l=(r=A.getContext("webgl2"))!==null&&r!==void 0?r:A.getContext("webgl");if(l){var s=l.getContextAttributes();(s==null?void 0:s.preserveDrawingBuffer)===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}i.drawImage(A,0,0)}return n}catch{this.context.logger.info("Unable to clone canvas as it is tainted",A)}return n},e.prototype.createVideoClone=function(A){var r=A.ownerDocument.createElement("canvas");r.width=A.offsetWidth,r.height=A.offsetHeight;var t=r.getContext("2d");try{return t&&(t.drawImage(A,0,0,r.width,r.height),this.options.allowTaint||t.getImageData(0,0,r.width,r.height)),r}catch{this.context.logger.info("Unable to clone video as it is tainted",A)}var n=A.ownerDocument.createElement("canvas");return n.width=A.offsetWidth,n.height=A.offsetHeight,n},e.prototype.appendChildNode=function(A,r,t){(!he(r)||!ql(r)&&!r.hasAttribute(Po)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(r)))&&(!this.options.copyStyles||!he(r)||!Lo(r))&&A.appendChild(this.cloneNode(r,t))},e.prototype.cloneChildNodes=function(A,r,t){for(var n=this,o=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;o;o=o.nextSibling)if(he(o)&&xo(o)&&typeof o.assignedNodes=="function"){var i=o.assignedNodes();i.length&&i.forEach(function(l){return n.appendChildNode(r,l,t)})}else this.appendChildNode(r,o,t)},e.prototype.cloneNode=function(A,r){if(Eo(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var t=A.ownerDocument.defaultView;if(t&&he(A)&&(Or(A)||St(A))){var n=this.createElementClone(A);n.style.transitionProperty="none";var o=t.getComputedStyle(A),i=t.getComputedStyle(A,":before"),l=t.getComputedStyle(A,":after");this.referenceElement===A&&Or(n)&&(this.clonedReferenceElement=n),_r(n)&&gB(n);var s=this.counters.parse(new jn(this.context,o)),u=this.resolvePseudoContent(A,n,i,Ot.BEFORE);Mo(A)&&(r=!0),yo(A)||this.cloneChildNodes(A,n,r),u&&n.insertBefore(u,n.firstChild);var f=this.resolvePseudoContent(A,n,l,Ot.AFTER);return f&&n.appendChild(f),this.counters.pop(s),(o&&(this.options.copyStyles||St(A))&&!bo(A)||r)&&Rr(o,n),(A.scrollTop!==0||A.scrollLeft!==0)&&this.scrolledElements.push([n,A.scrollLeft,A.scrollTop]),(Dt(A)||Tt(A))&&(Dt(n)||Tt(n))&&(n.value=A.value),n}return A.cloneNode(!1)},e.prototype.resolvePseudoContent=function(A,r,t,n){var o=this;if(t){var i=t.content,l=r.ownerDocument;if(!(!l||!i||i==="none"||i==="-moz-alt-content"||t.display==="none")){this.counters.parse(new jn(this.context,t));var s=new Yc(this.context,t),u=l.createElement("html2canvaspseudoelement");Rr(t,u),s.content.forEach(function(w){if(w.type===0)u.appendChild(l.createTextNode(w.value));else if(w.type===22){var h=l.createElement("img");h.src=w.value,h.style.opacity="1",u.appendChild(h)}else if(w.type===18){if(w.name==="attr"){var L=w.values.filter(AA);L.length&&u.appendChild(l.createTextNode(A.getAttribute(L[0].value)||""))}else if(w.name==="counter"){var F=w.values.filter(de),E=F[0],R=F[1];if(E&&AA(E)){var K=o.counters.getCounterValue(E.value),M=R&&AA(R)?Fr.parse(o.context,R.value):3;u.appendChild(l.createTextNode(Ze(K,M,!1)))}}else if(w.name==="counters"){var q=w.values.filter(de),E=q[0],G=q[1],R=q[2];if(E&&AA(E)){var P=o.counters.getCounterValues(E.value),b=R&&AA(R)?Fr.parse(o.context,R.value):3,Z=G&&G.type===0?G.value:"",$=P.map(function(hA){return Ze(hA,b,!1)}).join(Z);u.appendChild(l.createTextNode($))}}}else if(w.type===20)switch(w.value){case"open-quote":u.appendChild(l.createTextNode(qn(s.quotes,o.quoteDepth++,!0)));break;case"close-quote":u.appendChild(l.createTextNode(qn(s.quotes,--o.quoteDepth,!1)));break;default:u.appendChild(l.createTextNode(w.value))}}),u.className=Gr+" "+kr;var f=n===Ot.BEFORE?" "+Gr:" "+kr;return St(r)?r.className.baseValue+=f:r.className+=f,u}}},e.destroy=function(A){return A.parentNode?(A.parentNode.removeChild(A),!0):!1},e}(),Ot=function(e){return e[e.BEFORE=0]="BEFORE",e[e.AFTER=1]="AFTER",e}(Ot||{}),tB=function(e,A){var r=e.createElement("iframe");return r.className="html2canvas-container",r.style.visibility="hidden",r.style.position="fixed",r.style.left="-10000px",r.style.top="0px",r.style.border="0",r.width=A.width.toString(),r.height=A.height.toString(),r.scrolling="no",r.setAttribute(Po,"true"),e.body.appendChild(r),r},rB=function(e){return new Promise(function(A){if(e.complete){A();return}if(!e.src){A();return}e.onload=A,e.onerror=A})},nB=function(e){return Promise.all([].slice.call(e.images,0).map(rB))},oB=function(e){return new Promise(function(A,r){var t=e.contentWindow;if(!t)return r("No window assigned for iframe");var n=t.document;t.onload=e.onload=function(){t.onload=e.onload=null;var o=setInterval(function(){n.body.childNodes.length>0&&n.readyState==="complete"&&(clearInterval(o),A(e))},50)}})},iB=["all","d","content"],Rr=function(e,A){for(var r=e.length-1;r>=0;r--){var t=e.item(r);iB.indexOf(t)===-1&&A.style.setProperty(t,e.getPropertyValue(t))}return A},aB=function(e){var A="";return e&&(A+="<!DOCTYPE ",e.name&&(A+=e.name),e.internalSubset&&(A+=e.internalSubset),e.publicId&&(A+='"'+e.publicId+'"'),e.systemId&&(A+='"'+e.systemId+'"'),A+=">"),A},sB=function(e,A,r){e&&e.defaultView&&(A!==e.defaultView.pageXOffset||r!==e.defaultView.pageYOffset)&&e.defaultView.scrollTo(A,r)},cB=function(e){var A=e[0],r=e[1],t=e[2];A.scrollLeft=r,A.scrollTop=t},lB=":before",BB=":after",Gr="___html2canvas___pseudoelement_before",kr="___html2canvas___pseudoelement_after",Ro=`{
    content: "" !important;
    display: none !important;
}`,gB=function(e){uB(e,"."+Gr+lB+Ro+`
         .`+kr+BB+Ro)},uB=function(e,A){var r=e.ownerDocument;if(r){var t=r.createElement("style");t.textContent=A,e.appendChild(t)}},Go=function(){function e(){}return e.getOrigin=function(A){var r=e._link;return r?(r.href=A,r.href=r.href,r.protocol+r.hostname+r.port):"about:blank"},e.isSameOrigin=function(A){return e.getOrigin(A)===e._origin},e.setContext=function(A){e._link=A.document.createElement("a"),e._origin=e.getOrigin(A.location.href)},e._origin="about:blank",e}(),fB=function(){function e(A,r){this.context=A,this._options=r,this._cache={}}return e.prototype.addImage=function(A){var r=Promise.resolve();return this.has(A)||(Xr(A)||CB(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),r},e.prototype.match=function(A){return this._cache[A]},e.prototype.loadImage=function(A){return a(this,void 0,void 0,function(){var r,t,n,o,i=this;return c(this,function(l){switch(l.label){case 0:return r=Go.isSameOrigin(A),t=!Vr(A)&&this._options.useCORS===!0&&gA.SUPPORT_CORS_IMAGES&&!r,n=!Vr(A)&&!r&&!Xr(A)&&typeof this._options.proxy=="string"&&gA.SUPPORT_CORS_XHR&&!t,!r&&this._options.allowTaint===!1&&!Vr(A)&&!Xr(A)&&!n&&!t?[2]:(o=A,n?[4,this.proxy(o)]:[3,2]);case 1:o=l.sent(),l.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise(function(s,u){var f=new Image;f.onload=function(){return s(f)},f.onerror=u,(QB(o)||t)&&(f.crossOrigin="anonymous"),f.src=o,f.complete===!0&&setTimeout(function(){return s(f)},500),i._options.imageTimeout>0&&setTimeout(function(){return u("Timed out ("+i._options.imageTimeout+"ms) loading image")},i._options.imageTimeout)})];case 3:return[2,l.sent()]}})})},e.prototype.has=function(A){return typeof this._cache[A]<"u"},e.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},e.prototype.proxy=function(A){var r=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var n=A.substring(0,256);return new Promise(function(o,i){var l=gA.SUPPORT_RESPONSE_TYPE?"blob":"text",s=new XMLHttpRequest;s.onload=function(){if(s.status===200)if(l==="text")o(s.response);else{var w=new FileReader;w.addEventListener("load",function(){return o(w.result)},!1),w.addEventListener("error",function(h){return i(h)},!1),w.readAsDataURL(s.response)}else i("Failed to proxy resource "+n+" with status code "+s.status)},s.onerror=i;var u=t.indexOf("?")>-1?"&":"?";if(s.open("GET",""+t+u+"url="+encodeURIComponent(A)+"&responseType="+l),l!=="text"&&s instanceof XMLHttpRequest&&(s.responseType=l),r._options.imageTimeout){var f=r._options.imageTimeout;s.timeout=f,s.ontimeout=function(){return i("Timed out ("+f+"ms) proxying "+n)}}s.send()})},e}(),wB=/^data:image\/svg\+xml/i,dB=/^data:image\/.*;base64,/i,hB=/^data:image\/.*/i,CB=function(e){return gA.SUPPORT_SVG_DRAWING||!pB(e)},Vr=function(e){return hB.test(e)},QB=function(e){return dB.test(e)},Xr=function(e){return e.substr(0,4)==="blob"},pB=function(e){return e.substr(-3).toLowerCase()==="svg"||wB.test(e)},I=function(){function e(A,r){this.type=0,this.x=A,this.y=r}return e.prototype.add=function(A,r){return new e(this.x+A,this.y+r)},e}(),pe=function(e,A,r){return new I(e.x+(A.x-e.x)*r,e.y+(A.y-e.y)*r)},_t=function(){function e(A,r,t,n){this.type=1,this.start=A,this.startControl=r,this.endControl=t,this.end=n}return e.prototype.subdivide=function(A,r){var t=pe(this.start,this.startControl,A),n=pe(this.startControl,this.endControl,A),o=pe(this.endControl,this.end,A),i=pe(t,n,A),l=pe(n,o,A),s=pe(i,l,A);return r?new e(this.start,t,i,s):new e(s,l,o,this.end)},e.prototype.add=function(A,r){return new e(this.start.add(A,r),this.startControl.add(A,r),this.endControl.add(A,r),this.end.add(A,r))},e.prototype.reverse=function(){return new e(this.end,this.endControl,this.startControl,this.start)},e}(),HA=function(e){return e.type===1},UB=function(){function e(A){var r=A.styles,t=A.bounds,n=ke(r.borderTopLeftRadius,t.width,t.height),o=n[0],i=n[1],l=ke(r.borderTopRightRadius,t.width,t.height),s=l[0],u=l[1],f=ke(r.borderBottomRightRadius,t.width,t.height),w=f[0],h=f[1],L=ke(r.borderBottomLeftRadius,t.width,t.height),F=L[0],E=L[1],R=[];R.push((o+s)/t.width),R.push((F+w)/t.width),R.push((i+E)/t.height),R.push((u+h)/t.height);var K=Math.max.apply(Math,R);K>1&&(o/=K,i/=K,s/=K,u/=K,w/=K,h/=K,F/=K,E/=K);var M=t.width-s,q=t.height-h,G=t.width-w,P=t.height-E,b=r.borderTopWidth,Z=r.borderRightWidth,$=r.borderBottomWidth,N=r.borderLeftWidth,aA=rA(r.paddingTop,A.bounds.width),hA=rA(r.paddingRight,A.bounds.width),FA=rA(r.paddingBottom,A.bounds.width),eA=rA(r.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=o>0||i>0?nA(t.left+N/3,t.top+b/3,o-N/3,i-b/3,j.TOP_LEFT):new I(t.left+N/3,t.top+b/3),this.topRightBorderDoubleOuterBox=o>0||i>0?nA(t.left+M,t.top+b/3,s-Z/3,u-b/3,j.TOP_RIGHT):new I(t.left+t.width-Z/3,t.top+b/3),this.bottomRightBorderDoubleOuterBox=w>0||h>0?nA(t.left+G,t.top+q,w-Z/3,h-$/3,j.BOTTOM_RIGHT):new I(t.left+t.width-Z/3,t.top+t.height-$/3),this.bottomLeftBorderDoubleOuterBox=F>0||E>0?nA(t.left+N/3,t.top+P,F-N/3,E-$/3,j.BOTTOM_LEFT):new I(t.left+N/3,t.top+t.height-$/3),this.topLeftBorderDoubleInnerBox=o>0||i>0?nA(t.left+N*2/3,t.top+b*2/3,o-N*2/3,i-b*2/3,j.TOP_LEFT):new I(t.left+N*2/3,t.top+b*2/3),this.topRightBorderDoubleInnerBox=o>0||i>0?nA(t.left+M,t.top+b*2/3,s-Z*2/3,u-b*2/3,j.TOP_RIGHT):new I(t.left+t.width-Z*2/3,t.top+b*2/3),this.bottomRightBorderDoubleInnerBox=w>0||h>0?nA(t.left+G,t.top+q,w-Z*2/3,h-$*2/3,j.BOTTOM_RIGHT):new I(t.left+t.width-Z*2/3,t.top+t.height-$*2/3),this.bottomLeftBorderDoubleInnerBox=F>0||E>0?nA(t.left+N*2/3,t.top+P,F-N*2/3,E-$*2/3,j.BOTTOM_LEFT):new I(t.left+N*2/3,t.top+t.height-$*2/3),this.topLeftBorderStroke=o>0||i>0?nA(t.left+N/2,t.top+b/2,o-N/2,i-b/2,j.TOP_LEFT):new I(t.left+N/2,t.top+b/2),this.topRightBorderStroke=o>0||i>0?nA(t.left+M,t.top+b/2,s-Z/2,u-b/2,j.TOP_RIGHT):new I(t.left+t.width-Z/2,t.top+b/2),this.bottomRightBorderStroke=w>0||h>0?nA(t.left+G,t.top+q,w-Z/2,h-$/2,j.BOTTOM_RIGHT):new I(t.left+t.width-Z/2,t.top+t.height-$/2),this.bottomLeftBorderStroke=F>0||E>0?nA(t.left+N/2,t.top+P,F-N/2,E-$/2,j.BOTTOM_LEFT):new I(t.left+N/2,t.top+t.height-$/2),this.topLeftBorderBox=o>0||i>0?nA(t.left,t.top,o,i,j.TOP_LEFT):new I(t.left,t.top),this.topRightBorderBox=s>0||u>0?nA(t.left+M,t.top,s,u,j.TOP_RIGHT):new I(t.left+t.width,t.top),this.bottomRightBorderBox=w>0||h>0?nA(t.left+G,t.top+q,w,h,j.BOTTOM_RIGHT):new I(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=F>0||E>0?nA(t.left,t.top+P,F,E,j.BOTTOM_LEFT):new I(t.left,t.top+t.height),this.topLeftPaddingBox=o>0||i>0?nA(t.left+N,t.top+b,Math.max(0,o-N),Math.max(0,i-b),j.TOP_LEFT):new I(t.left+N,t.top+b),this.topRightPaddingBox=s>0||u>0?nA(t.left+Math.min(M,t.width-Z),t.top+b,M>t.width+Z?0:Math.max(0,s-Z),Math.max(0,u-b),j.TOP_RIGHT):new I(t.left+t.width-Z,t.top+b),this.bottomRightPaddingBox=w>0||h>0?nA(t.left+Math.min(G,t.width-N),t.top+Math.min(q,t.height-$),Math.max(0,w-Z),Math.max(0,h-$),j.BOTTOM_RIGHT):new I(t.left+t.width-Z,t.top+t.height-$),this.bottomLeftPaddingBox=F>0||E>0?nA(t.left+N,t.top+Math.min(P,t.height-$),Math.max(0,F-N),Math.max(0,E-$),j.BOTTOM_LEFT):new I(t.left+N,t.top+t.height-$),this.topLeftContentBox=o>0||i>0?nA(t.left+N+eA,t.top+b+aA,Math.max(0,o-(N+eA)),Math.max(0,i-(b+aA)),j.TOP_LEFT):new I(t.left+N+eA,t.top+b+aA),this.topRightContentBox=s>0||u>0?nA(t.left+Math.min(M,t.width+N+eA),t.top+b+aA,M>t.width+N+eA?0:s-N+eA,u-(b+aA),j.TOP_RIGHT):new I(t.left+t.width-(Z+hA),t.top+b+aA),this.bottomRightContentBox=w>0||h>0?nA(t.left+Math.min(G,t.width-(N+eA)),t.top+Math.min(q,t.height+b+aA),Math.max(0,w-(Z+hA)),h-($+FA),j.BOTTOM_RIGHT):new I(t.left+t.width-(Z+hA),t.top+t.height-($+FA)),this.bottomLeftContentBox=F>0||E>0?nA(t.left+N+eA,t.top+P,Math.max(0,F-(N+eA)),E-($+FA),j.BOTTOM_LEFT):new I(t.left+N+eA,t.top+t.height-($+FA))}return e}(),j=function(e){return e[e.TOP_LEFT=0]="TOP_LEFT",e[e.TOP_RIGHT=1]="TOP_RIGHT",e[e.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",e[e.BOTTOM_LEFT=3]="BOTTOM_LEFT",e}(j||{}),nA=function(e,A,r,t,n){var o=4*((Math.sqrt(2)-1)/3),i=r*o,l=t*o,s=e+r,u=A+t;switch(n){case j.TOP_LEFT:return new _t(new I(e,u),new I(e,u-l),new I(s-i,A),new I(s,A));case j.TOP_RIGHT:return new _t(new I(e,A),new I(e+i,A),new I(s,u-l),new I(s,u));case j.BOTTOM_RIGHT:return new _t(new I(s,A),new I(s,A+l),new I(e+i,u),new I(e,u));case j.BOTTOM_LEFT:default:return new _t(new I(s,u),new I(s-i,u),new I(e,A+l),new I(e,A))}},Pt=function(e){return[e.topLeftBorderBox,e.topRightBorderBox,e.bottomRightBorderBox,e.bottomLeftBorderBox]},FB=function(e){return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox]},Nt=function(e){return[e.topLeftPaddingBox,e.topRightPaddingBox,e.bottomRightPaddingBox,e.bottomLeftPaddingBox]},mB=function(){function e(A,r,t){this.offsetX=A,this.offsetY=r,this.matrix=t,this.type=0,this.target=6}return e}(),Rt=function(){function e(A,r){this.path=A,this.target=r,this.type=1}return e}(),vB=function(){function e(A){this.opacity=A,this.type=2,this.target=6}return e}(),EB=function(e){return e.type===0},ko=function(e){return e.type===1},HB=function(e){return e.type===2},Vo=function(e,A){return e.length===A.length?e.some(function(r,t){return r===A[t]}):!1},IB=function(e,A,r,t,n){return e.map(function(o,i){switch(i){case 0:return o.add(A,r);case 1:return o.add(A+t,r);case 2:return o.add(A+t,r+n);case 3:return o.add(A,r+n)}return o})},Xo=function(){function e(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return e}(),Jo=function(){function e(A,r){if(this.container=A,this.parent=r,this.effects=[],this.curves=new UB(this.container),this.container.styles.opacity<1&&this.effects.push(new vB(this.container.styles.opacity)),this.container.styles.transform!==null){var t=this.container.bounds.left+this.container.styles.transformOrigin[0].number,n=this.container.bounds.top+this.container.styles.transformOrigin[1].number,o=this.container.styles.transform;this.effects.push(new mB(t,n,o))}if(this.container.styles.overflowX!==0){var i=Pt(this.curves),l=Nt(this.curves);Vo(i,l)?this.effects.push(new Rt(i,6)):(this.effects.push(new Rt(i,2)),this.effects.push(new Rt(l,4)))}}return e.prototype.getEffects=function(A){for(var r=[2,3].indexOf(this.container.styles.position)===-1,t=this.parent,n=this.effects.slice(0);t;){var o=t.effects.filter(function(s){return!ko(s)});if(r||t.container.styles.position!==0||!t.parent){if(n.unshift.apply(n,o),r=[2,3].indexOf(t.container.styles.position)===-1,t.container.styles.overflowX!==0){var i=Pt(t.curves),l=Nt(t.curves);Vo(i,l)||n.unshift(new Rt(l,6))}}else n.unshift.apply(n,o);t=t.parent}return n.filter(function(s){return cA(s.target,A)})},e}(),Jr=function(e,A,r,t){e.container.elements.forEach(function(n){var o=cA(n.flags,4),i=cA(n.flags,2),l=new Jo(n,e);cA(n.styles.display,2048)&&t.push(l);var s=cA(n.flags,8)?[]:t;if(o||i){var u=o||n.styles.isPositioned()?r:A,f=new Xo(l);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var w=n.styles.zIndex.order;if(w<0){var h=0;u.negativeZIndex.some(function(F,E){return w>F.element.container.styles.zIndex.order?(h=E,!1):h>0}),u.negativeZIndex.splice(h,0,f)}else if(w>0){var L=0;u.positiveZIndex.some(function(F,E){return w>=F.element.container.styles.zIndex.order?(L=E+1,!1):L>0}),u.positiveZIndex.splice(L,0,f)}else u.zeroOrAutoZIndexOrTransformedOrOpacity.push(f)}else n.styles.isFloating()?u.nonPositionedFloats.push(f):u.nonPositionedInlineLevel.push(f);Jr(l,f,o?f:r,s)}else n.styles.isInlineLevel()?A.inlineLevel.push(l):A.nonInlineLevel.push(l),Jr(l,A,r,s);cA(n.flags,8)&&Wo(n,s)})},Wo=function(e,A){for(var r=e instanceof Dr?e.start:1,t=e instanceof Dr?e.reversed:!1,n=0;n<A.length;n++){var o=A[n];o.container instanceof Co&&typeof o.container.value=="number"&&o.container.value!==0&&(r=o.container.value),o.listValue=Ze(r,o.container.styles.listStyleType,!0),r+=t?-1:1}},yB=function(e){var A=new Jo(e,null),r=new Xo(A),t=[];return Jr(A,r,r,t),Wo(A.container,t),r},Yo=function(e,A){switch(A){case 0:return IA(e.topLeftBorderBox,e.topLeftPaddingBox,e.topRightBorderBox,e.topRightPaddingBox);case 1:return IA(e.topRightBorderBox,e.topRightPaddingBox,e.bottomRightBorderBox,e.bottomRightPaddingBox);case 2:return IA(e.bottomRightBorderBox,e.bottomRightPaddingBox,e.bottomLeftBorderBox,e.bottomLeftPaddingBox);case 3:default:return IA(e.bottomLeftBorderBox,e.bottomLeftPaddingBox,e.topLeftBorderBox,e.topLeftPaddingBox)}},bB=function(e,A){switch(A){case 0:return IA(e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox,e.topRightBorderBox,e.topRightBorderDoubleOuterBox);case 1:return IA(e.topRightBorderBox,e.topRightBorderDoubleOuterBox,e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox);case 2:return IA(e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox,e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox);case 3:default:return IA(e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox,e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox)}},LB=function(e,A){switch(A){case 0:return IA(e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox,e.topRightBorderDoubleInnerBox,e.topRightPaddingBox);case 1:return IA(e.topRightBorderDoubleInnerBox,e.topRightPaddingBox,e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox);case 2:return IA(e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox,e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox);case 3:default:return IA(e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox,e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox)}},xB=function(e,A){switch(A){case 0:return Gt(e.topLeftBorderStroke,e.topRightBorderStroke);case 1:return Gt(e.topRightBorderStroke,e.bottomRightBorderStroke);case 2:return Gt(e.bottomRightBorderStroke,e.bottomLeftBorderStroke);case 3:default:return Gt(e.bottomLeftBorderStroke,e.topLeftBorderStroke)}},Gt=function(e,A){var r=[];return HA(e)?r.push(e.subdivide(.5,!1)):r.push(e),HA(A)?r.push(A.subdivide(.5,!0)):r.push(A),r},IA=function(e,A,r,t){var n=[];return HA(e)?n.push(e.subdivide(.5,!1)):n.push(e),HA(r)?n.push(r.subdivide(.5,!0)):n.push(r),HA(t)?n.push(t.subdivide(.5,!0).reverse()):n.push(t),HA(A)?n.push(A.subdivide(.5,!1).reverse()):n.push(A),n},Zo=function(e){var A=e.bounds,r=e.styles;return A.add(r.borderLeftWidth,r.borderTopWidth,-(r.borderRightWidth+r.borderLeftWidth),-(r.borderTopWidth+r.borderBottomWidth))},kt=function(e){var A=e.styles,r=e.bounds,t=rA(A.paddingLeft,r.width),n=rA(A.paddingRight,r.width),o=rA(A.paddingTop,r.width),i=rA(A.paddingBottom,r.width);return r.add(t+A.borderLeftWidth,o+A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth+t+n),-(A.borderTopWidth+A.borderBottomWidth+o+i))},MB=function(e,A){return e===0?A.bounds:e===2?kt(A):Zo(A)},KB=function(e,A){return e===0?A.bounds:e===2?kt(A):Zo(A)},Wr=function(e,A,r){var t=MB(Fe(e.styles.backgroundOrigin,A),e),n=KB(Fe(e.styles.backgroundClip,A),e),o=SB(Fe(e.styles.backgroundSize,A),r,t),i=o[0],l=o[1],s=ke(Fe(e.styles.backgroundPosition,A),t.width-i,t.height-l),u=DB(Fe(e.styles.backgroundRepeat,A),s,o,t,n),f=Math.round(t.left+s[0]),w=Math.round(t.top+s[1]);return[u,f,w,i,l]},Ue=function(e){return AA(e)&&e.value===Xe.AUTO},Vt=function(e){return typeof e=="number"},SB=function(e,A,r){var t=A[0],n=A[1],o=A[2],i=e[0],l=e[1];if(!i)return[0,0];if(iA(i)&&l&&iA(l))return[rA(i,r.width),rA(l,r.height)];var s=Vt(o);if(AA(i)&&(i.value===Xe.CONTAIN||i.value===Xe.COVER)){if(Vt(o)){var u=r.width/r.height;return u<o!=(i.value===Xe.COVER)?[r.width,r.width/o]:[r.height*o,r.height]}return[r.width,r.height]}var f=Vt(t),w=Vt(n),h=f||w;if(Ue(i)&&(!l||Ue(l))){if(f&&w)return[t,n];if(!s&&!h)return[r.width,r.height];if(h&&s){var L=f?t:n*o,F=w?n:t/o;return[L,F]}var E=f?t:r.width,R=w?n:r.height;return[E,R]}if(s){var K=0,M=0;return iA(i)?K=rA(i,r.width):iA(l)&&(M=rA(l,r.height)),Ue(i)?K=M*o:(!l||Ue(l))&&(M=K/o),[K,M]}var q=null,G=null;if(iA(i)?q=rA(i,r.width):l&&iA(l)&&(G=rA(l,r.height)),q!==null&&(!l||Ue(l))&&(G=f&&w?q/t*n:r.height),G!==null&&Ue(i)&&(q=f&&w?G/n*t:r.width),q!==null&&G!==null)return[q,G];throw new Error("Unable to calculate background-size for element")},Fe=function(e,A){var r=e[A];return typeof r>"u"?e[0]:r},DB=function(e,A,r,t,n){var o=A[0],i=A[1],l=r[0],s=r[1];switch(e){case 2:return[new I(Math.round(t.left),Math.round(t.top+i)),new I(Math.round(t.left+t.width),Math.round(t.top+i)),new I(Math.round(t.left+t.width),Math.round(s+t.top+i)),new I(Math.round(t.left),Math.round(s+t.top+i))];case 3:return[new I(Math.round(t.left+o),Math.round(t.top)),new I(Math.round(t.left+o+l),Math.round(t.top)),new I(Math.round(t.left+o+l),Math.round(t.height+t.top)),new I(Math.round(t.left+o),Math.round(t.height+t.top))];case 1:return[new I(Math.round(t.left+o),Math.round(t.top+i)),new I(Math.round(t.left+o+l),Math.round(t.top+i)),new I(Math.round(t.left+o+l),Math.round(t.top+i+s)),new I(Math.round(t.left+o),Math.round(t.top+i+s))];default:return[new I(Math.round(n.left),Math.round(n.top)),new I(Math.round(n.left+n.width),Math.round(n.top)),new I(Math.round(n.left+n.width),Math.round(n.height+n.top)),new I(Math.round(n.left),Math.round(n.height+n.top))]}},TB="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",$o="Hidden Text",OB=function(){function e(A){this._data={},this._document=A}return e.prototype.parseMetrics=function(A,r){var t=this._document.createElement("div"),n=this._document.createElement("img"),o=this._document.createElement("span"),i=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=r,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",i.appendChild(t),n.src=TB,n.width=1,n.height=1,n.style.margin="0",n.style.padding="0",n.style.verticalAlign="baseline",o.style.fontFamily=A,o.style.fontSize=r,o.style.margin="0",o.style.padding="0",o.appendChild(this._document.createTextNode($o)),t.appendChild(o),t.appendChild(n);var l=n.offsetTop-o.offsetTop+2;t.removeChild(o),t.appendChild(this._document.createTextNode($o)),t.style.lineHeight="normal",n.style.verticalAlign="super";var s=n.offsetTop-t.offsetTop+2;return i.removeChild(t),{baseline:l,middle:s}},e.prototype.getMetrics=function(A,r){var t=A+" "+r;return typeof this._data[t]>"u"&&(this._data[t]=this.parseMetrics(A,r)),this._data[t]},e}(),zo=function(){function e(A,r){this.context=A,this.options=r}return e}(),_B=1e4,PB=function(e){S(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n._activeEffects=[],n.canvas=t.canvas?t.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),t.canvas||(n.canvas.width=Math.floor(t.width*t.scale),n.canvas.height=Math.floor(t.height*t.scale),n.canvas.style.width=t.width+"px",n.canvas.style.height=t.height+"px"),n.fontMetrics=new OB(document),n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-t.x,-t.y),n.ctx.textBaseline="bottom",n._activeEffects=[],n.context.logger.debug("Canvas renderer initialized ("+t.width+"x"+t.height+") with scale "+t.scale),n}return A.prototype.applyEffects=function(r){for(var t=this;this._activeEffects.length;)this.popEffect();r.forEach(function(n){return t.applyEffect(n)})},A.prototype.applyEffect=function(r){this.ctx.save(),HB(r)&&(this.ctx.globalAlpha=r.opacity),EB(r)&&(this.ctx.translate(r.offsetX,r.offsetY),this.ctx.transform(r.matrix[0],r.matrix[1],r.matrix[2],r.matrix[3],r.matrix[4],r.matrix[5]),this.ctx.translate(-r.offsetX,-r.offsetY)),ko(r)&&(this.path(r.path),this.ctx.clip()),this._activeEffects.push(r)},A.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},A.prototype.renderStack=function(r){return a(this,void 0,void 0,function(){var t;return c(this,function(n){switch(n.label){case 0:return t=r.element.container.styles,t.isVisible()?[4,this.renderStackContent(r)]:[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}})})},A.prototype.renderNode=function(r){return a(this,void 0,void 0,function(){return c(this,function(t){switch(t.label){case 0:if(cA(r.container.flags,16))debugger;return r.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(r)]:[3,3];case 1:return t.sent(),[4,this.renderNodeContent(r)];case 2:t.sent(),t.label=3;case 3:return[2]}})})},A.prototype.renderTextWithLetterSpacing=function(r,t,n){var o=this;if(t===0)this.ctx.fillText(r.text,r.bounds.left,r.bounds.top+n);else{var i=Sr(r.text);i.reduce(function(l,s){return o.ctx.fillText(s,l,r.bounds.top+n),l+o.ctx.measureText(s).width},r.bounds.left)}},A.prototype.createFontStyle=function(r){var t=r.fontVariant.filter(function(i){return i==="normal"||i==="small-caps"}).join(""),n=VB(r.fontFamily).join(", "),o=Ge(r.fontSize)?""+r.fontSize.number+r.fontSize.unit:r.fontSize.number+"px";return[[r.fontStyle,t,r.fontWeight,o,n].join(" "),n,o]},A.prototype.renderTextNode=function(r,t){return a(this,void 0,void 0,function(){var n,o,i,l,s,u,f,w,h=this;return c(this,function(L){return n=this.createFontStyle(t),o=n[0],i=n[1],l=n[2],this.ctx.font=o,this.ctx.direction=t.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",s=this.fontMetrics.getMetrics(i,l),u=s.baseline,f=s.middle,w=t.paintOrder,r.textBounds.forEach(function(F){w.forEach(function(E){switch(E){case 0:h.ctx.fillStyle=lA(t.color),h.renderTextWithLetterSpacing(F,t.letterSpacing,u);var R=t.textShadow;R.length&&F.text.trim().length&&(R.slice(0).reverse().forEach(function(K){h.ctx.shadowColor=lA(K.color),h.ctx.shadowOffsetX=K.offsetX.number*h.options.scale,h.ctx.shadowOffsetY=K.offsetY.number*h.options.scale,h.ctx.shadowBlur=K.blur.number,h.renderTextWithLetterSpacing(F,t.letterSpacing,u)}),h.ctx.shadowColor="",h.ctx.shadowOffsetX=0,h.ctx.shadowOffsetY=0,h.ctx.shadowBlur=0),t.textDecorationLine.length&&(h.ctx.fillStyle=lA(t.textDecorationColor||t.color),t.textDecorationLine.forEach(function(K){switch(K){case 1:h.ctx.fillRect(F.bounds.left,Math.round(F.bounds.top+u),F.bounds.width,1);break;case 2:h.ctx.fillRect(F.bounds.left,Math.round(F.bounds.top),F.bounds.width,1);break;case 3:h.ctx.fillRect(F.bounds.left,Math.ceil(F.bounds.top+f),F.bounds.width,1);break}}));break;case 1:t.webkitTextStrokeWidth&&F.text.trim().length&&(h.ctx.strokeStyle=lA(t.webkitTextStrokeColor),h.ctx.lineWidth=t.webkitTextStrokeWidth,h.ctx.lineJoin=window.chrome?"miter":"round",h.ctx.strokeText(F.text,F.bounds.left,F.bounds.top+u)),h.ctx.strokeStyle="",h.ctx.lineWidth=0,h.ctx.lineJoin="miter";break}})}),[2]})})},A.prototype.renderReplacedElement=function(r,t,n){if(n&&r.intrinsicWidth>0&&r.intrinsicHeight>0){var o=kt(r),i=Nt(t);this.path(i),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(n,0,0,r.intrinsicWidth,r.intrinsicHeight,o.left,o.top,o.width,o.height),this.ctx.restore()}},A.prototype.renderNodeContent=function(r){return a(this,void 0,void 0,function(){var t,n,o,i,l,s,M,M,u,f,w,h,G,L,F,P,E,R,K,M,q,G,P;return c(this,function(b){switch(b.label){case 0:this.applyEffects(r.getEffects(4)),t=r.container,n=r.curves,o=t.styles,i=0,l=t.textNodes,b.label=1;case 1:return i<l.length?(s=l[i],[4,this.renderTextNode(s,o)]):[3,4];case 2:b.sent(),b.label=3;case 3:return i++,[3,1];case 4:if(!(t instanceof fo))return[3,8];b.label=5;case 5:return b.trys.push([5,7,,8]),[4,this.context.cache.match(t.src)];case 6:return M=b.sent(),this.renderReplacedElement(t,n,M),[3,8];case 7:return b.sent(),this.context.logger.error("Error loading image "+t.src),[3,8];case 8:if(t instanceof wo&&this.renderReplacedElement(t,n,t.canvas),!(t instanceof ho))return[3,12];b.label=9;case 9:return b.trys.push([9,11,,12]),[4,this.context.cache.match(t.svg)];case 10:return M=b.sent(),this.renderReplacedElement(t,n,M),[3,12];case 11:return b.sent(),this.context.logger.error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof Fo&&t.tree?(u=new A(this.context,{scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,width:t.width,height:t.height}),[4,u.render(t.tree)]):[3,14];case 13:f=b.sent(),t.width&&t.height&&this.ctx.drawImage(f,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),b.label=14;case 14:if(t instanceof Tr&&(w=Math.min(t.bounds.width,t.bounds.height),t.type===xt?t.checked&&(this.ctx.save(),this.path([new I(t.bounds.left+w*.39363,t.bounds.top+w*.79),new I(t.bounds.left+w*.16,t.bounds.top+w*.5549),new I(t.bounds.left+w*.27347,t.bounds.top+w*.44071),new I(t.bounds.left+w*.39694,t.bounds.top+w*.5649),new I(t.bounds.left+w*.72983,t.bounds.top+w*.23),new I(t.bounds.left+w*.84,t.bounds.top+w*.34085),new I(t.bounds.left+w*.39363,t.bounds.top+w*.79)]),this.ctx.fillStyle=lA(Qo),this.ctx.fill(),this.ctx.restore()):t.type===Mt&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+w/2,t.bounds.top+w/2,w/4,0,Math.PI*2,!0),this.ctx.fillStyle=lA(Qo),this.ctx.fill(),this.ctx.restore())),NB(t)&&t.value.length){switch(h=this.createFontStyle(o),G=h[0],L=h[1],F=this.fontMetrics.getMetrics(G,L).baseline,this.ctx.font=G,this.ctx.fillStyle=lA(o.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=GB(t.styles.textAlign),P=kt(t),E=0,t.styles.textAlign){case 1:E+=P.width/2;break;case 2:E+=P.width;break}R=P.add(E,0,0,-P.height/2+1),this.ctx.save(),this.path([new I(P.left,P.top),new I(P.left+P.width,P.top),new I(P.left+P.width,P.top+P.height),new I(P.left,P.top+P.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new We(t.value,R),o.letterSpacing,F),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!cA(t.styles.display,2048))return[3,20];if(t.styles.listStyleImage===null)return[3,19];if(K=t.styles.listStyleImage,K.type!==0)return[3,18];M=void 0,q=K.url,b.label=15;case 15:return b.trys.push([15,17,,18]),[4,this.context.cache.match(q)];case 16:return M=b.sent(),this.ctx.drawImage(M,t.bounds.left-(M.width+10),t.bounds.top),[3,18];case 17:return b.sent(),this.context.logger.error("Error loading list-style-image "+q),[3,18];case 18:return[3,20];case 19:r.listValue&&t.styles.listStyleType!==-1&&(G=this.createFontStyle(o)[0],this.ctx.font=G,this.ctx.fillStyle=lA(o.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",P=new g(t.bounds.left,t.bounds.top+rA(t.styles.paddingTop,t.bounds.width),t.bounds.width,Zn(o.lineHeight,o.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new We(r.listValue,P),o.letterSpacing,Zn(o.lineHeight,o.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),b.label=20;case 20:return[2]}})})},A.prototype.renderStackContent=function(r){return a(this,void 0,void 0,function(){var t,n,K,o,i,K,l,s,K,u,f,K,w,h,K,L,F,K,E,R,K;return c(this,function(M){switch(M.label){case 0:if(cA(r.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(r.element)];case 1:M.sent(),t=0,n=r.negativeZIndex,M.label=2;case 2:return t<n.length?(K=n[t],[4,this.renderStack(K)]):[3,5];case 3:M.sent(),M.label=4;case 4:return t++,[3,2];case 5:return[4,this.renderNodeContent(r.element)];case 6:M.sent(),o=0,i=r.nonInlineLevel,M.label=7;case 7:return o<i.length?(K=i[o],[4,this.renderNode(K)]):[3,10];case 8:M.sent(),M.label=9;case 9:return o++,[3,7];case 10:l=0,s=r.nonPositionedFloats,M.label=11;case 11:return l<s.length?(K=s[l],[4,this.renderStack(K)]):[3,14];case 12:M.sent(),M.label=13;case 13:return l++,[3,11];case 14:u=0,f=r.nonPositionedInlineLevel,M.label=15;case 15:return u<f.length?(K=f[u],[4,this.renderStack(K)]):[3,18];case 16:M.sent(),M.label=17;case 17:return u++,[3,15];case 18:w=0,h=r.inlineLevel,M.label=19;case 19:return w<h.length?(K=h[w],[4,this.renderNode(K)]):[3,22];case 20:M.sent(),M.label=21;case 21:return w++,[3,19];case 22:L=0,F=r.zeroOrAutoZIndexOrTransformedOrOpacity,M.label=23;case 23:return L<F.length?(K=F[L],[4,this.renderStack(K)]):[3,26];case 24:M.sent(),M.label=25;case 25:return L++,[3,23];case 26:E=0,R=r.positiveZIndex,M.label=27;case 27:return E<R.length?(K=R[E],[4,this.renderStack(K)]):[3,30];case 28:M.sent(),M.label=29;case 29:return E++,[3,27];case 30:return[2]}})})},A.prototype.mask=function(r){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(r.slice(0).reverse()),this.ctx.closePath()},A.prototype.path=function(r){this.ctx.beginPath(),this.formatPath(r),this.ctx.closePath()},A.prototype.formatPath=function(r){var t=this;r.forEach(function(n,o){var i=HA(n)?n.start:n;o===0?t.ctx.moveTo(i.x,i.y):t.ctx.lineTo(i.x,i.y),HA(n)&&t.ctx.bezierCurveTo(n.startControl.x,n.startControl.y,n.endControl.x,n.endControl.y,n.end.x,n.end.y)})},A.prototype.renderRepeat=function(r,t,n,o){this.path(r),this.ctx.fillStyle=t,this.ctx.translate(n,o),this.ctx.fill(),this.ctx.translate(-n,-o)},A.prototype.resizeImage=function(r,t,n){var o;if(r.width===t&&r.height===n)return r;var i=(o=this.canvas.ownerDocument)!==null&&o!==void 0?o:document,l=i.createElement("canvas");l.width=Math.max(1,t),l.height=Math.max(1,n);var s=l.getContext("2d");return s.drawImage(r,0,0,r.width,r.height,0,0,t,n),l},A.prototype.renderBackgroundImage=function(r){return a(this,void 0,void 0,function(){var t,n,o,i,l,s;return c(this,function(u){switch(u.label){case 0:t=r.styles.backgroundImage.length-1,n=function(f){var w,h,L,aA,CA,QA,eA,uA,$,F,aA,CA,QA,eA,uA,E,R,K,M,q,G,P,b,Z,$,N,aA,hA,FA,eA,uA,jA,CA,QA,se,DA,Ae,ce,le,NA,Be,RA;return c(this,function(me){switch(me.label){case 0:if(f.type!==0)return[3,5];w=void 0,h=f.url,me.label=1;case 1:return me.trys.push([1,3,,4]),[4,o.context.cache.match(h)];case 2:return w=me.sent(),[3,4];case 3:return me.sent(),o.context.logger.error("Error loading background-image "+h),[3,4];case 4:return w&&(L=Wr(r,t,[w.width,w.height,w.width/w.height]),aA=L[0],CA=L[1],QA=L[2],eA=L[3],uA=L[4],$=o.ctx.createPattern(o.resizeImage(w,eA,uA),"repeat"),o.renderRepeat(aA,$,CA,QA)),[3,6];case 5:Es(f)?(F=Wr(r,t,[null,null,null]),aA=F[0],CA=F[1],QA=F[2],eA=F[3],uA=F[4],E=ps(f.angle,eA,uA),R=E[0],K=E[1],M=E[2],q=E[3],G=E[4],P=document.createElement("canvas"),P.width=eA,P.height=uA,b=P.getContext("2d"),Z=b.createLinearGradient(K,q,M,G),_n(f.stops,R).forEach(function($e){return Z.addColorStop($e.stop,lA($e.color))}),b.fillStyle=Z,b.fillRect(0,0,eA,uA),eA>0&&uA>0&&($=o.ctx.createPattern(P,"repeat"),o.renderRepeat(aA,$,CA,QA))):Hs(f)&&(N=Wr(r,t,[null,null,null]),aA=N[0],hA=N[1],FA=N[2],eA=N[3],uA=N[4],jA=f.position.length===0?[Cr]:f.position,CA=rA(jA[0],eA),QA=rA(jA[jA.length-1],uA),se=Us(f,CA,QA,eA,uA),DA=se[0],Ae=se[1],DA>0&&Ae>0&&(ce=o.ctx.createRadialGradient(hA+CA,FA+QA,0,hA+CA,FA+QA,DA),_n(f.stops,DA*2).forEach(function($e){return ce.addColorStop($e.stop,lA($e.color))}),o.path(aA),o.ctx.fillStyle=ce,DA!==Ae?(le=r.bounds.left+.5*r.bounds.width,NA=r.bounds.top+.5*r.bounds.height,Be=Ae/DA,RA=1/Be,o.ctx.save(),o.ctx.translate(le,NA),o.ctx.transform(1,0,0,Be,0,0),o.ctx.translate(-le,-NA),o.ctx.fillRect(hA,RA*(FA-NA)+NA,eA,uA*RA),o.ctx.restore()):o.ctx.fill())),me.label=6;case 6:return t--,[2]}})},o=this,i=0,l=r.styles.backgroundImage.slice(0).reverse(),u.label=1;case 1:return i<l.length?(s=l[i],[5,n(s)]):[3,4];case 2:u.sent(),u.label=3;case 3:return i++,[3,1];case 4:return[2]}})})},A.prototype.renderSolidBorder=function(r,t,n){return a(this,void 0,void 0,function(){return c(this,function(o){return this.path(Yo(n,t)),this.ctx.fillStyle=lA(r),this.ctx.fill(),[2]})})},A.prototype.renderDoubleBorder=function(r,t,n,o){return a(this,void 0,void 0,function(){var i,l;return c(this,function(s){switch(s.label){case 0:return t<3?[4,this.renderSolidBorder(r,n,o)]:[3,2];case 1:return s.sent(),[2];case 2:return i=bB(o,n),this.path(i),this.ctx.fillStyle=lA(r),this.ctx.fill(),l=LB(o,n),this.path(l),this.ctx.fill(),[2]}})})},A.prototype.renderNodeBackgroundAndBorders=function(r){return a(this,void 0,void 0,function(){var t,n,o,i,l,s,u,f,w=this;return c(this,function(h){switch(h.label){case 0:return this.applyEffects(r.getEffects(2)),t=r.container.styles,n=!ZA(t.backgroundColor)||t.backgroundImage.length,o=[{style:t.borderTopStyle,color:t.borderTopColor,width:t.borderTopWidth},{style:t.borderRightStyle,color:t.borderRightColor,width:t.borderRightWidth},{style:t.borderBottomStyle,color:t.borderBottomColor,width:t.borderBottomWidth},{style:t.borderLeftStyle,color:t.borderLeftColor,width:t.borderLeftWidth}],i=RB(Fe(t.backgroundClip,0),r.curves),n||t.boxShadow.length?(this.ctx.save(),this.path(i),this.ctx.clip(),ZA(t.backgroundColor)||(this.ctx.fillStyle=lA(t.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(r.container)]):[3,2];case 1:h.sent(),this.ctx.restore(),t.boxShadow.slice(0).reverse().forEach(function(L){w.ctx.save();var F=Pt(r.curves),E=L.inset?0:_B,R=IB(F,-E+(L.inset?1:-1)*L.spread.number,(L.inset?1:-1)*L.spread.number,L.spread.number*(L.inset?-2:2),L.spread.number*(L.inset?-2:2));L.inset?(w.path(F),w.ctx.clip(),w.mask(R)):(w.mask(F),w.ctx.clip(),w.path(R)),w.ctx.shadowOffsetX=L.offsetX.number+E,w.ctx.shadowOffsetY=L.offsetY.number,w.ctx.shadowColor=lA(L.color),w.ctx.shadowBlur=L.blur.number,w.ctx.fillStyle=L.inset?lA(L.color):"rgba(0,0,0,1)",w.ctx.fill(),w.ctx.restore()}),h.label=2;case 2:l=0,s=0,u=o,h.label=3;case 3:return s<u.length?(f=u[s],f.style!==0&&!ZA(f.color)&&f.width>0?f.style!==2?[3,5]:[4,this.renderDashedDottedBorder(f.color,f.width,l,r.curves,2)]:[3,11]):[3,13];case 4:return h.sent(),[3,11];case 5:return f.style!==3?[3,7]:[4,this.renderDashedDottedBorder(f.color,f.width,l,r.curves,3)];case 6:return h.sent(),[3,11];case 7:return f.style!==4?[3,9]:[4,this.renderDoubleBorder(f.color,f.width,l,r.curves)];case 8:return h.sent(),[3,11];case 9:return[4,this.renderSolidBorder(f.color,l,r.curves)];case 10:h.sent(),h.label=11;case 11:l++,h.label=12;case 12:return s++,[3,3];case 13:return[2]}})})},A.prototype.renderDashedDottedBorder=function(r,t,n,o,i){return a(this,void 0,void 0,function(){var l,s,u,f,w,h,L,F,E,R,K,M,q,G,P,b,P,b;return c(this,function(Z){return this.ctx.save(),l=xB(o,n),s=Yo(o,n),i===2&&(this.path(s),this.ctx.clip()),HA(s[0])?(u=s[0].start.x,f=s[0].start.y):(u=s[0].x,f=s[0].y),HA(s[1])?(w=s[1].end.x,h=s[1].end.y):(w=s[1].x,h=s[1].y),n===0||n===2?L=Math.abs(u-w):L=Math.abs(f-h),this.ctx.beginPath(),i===3?this.formatPath(l):this.formatPath(s.slice(0,2)),F=t<3?t*3:t*2,E=t<3?t*2:t,i===3&&(F=t,E=t),R=!0,L<=F*2?R=!1:L<=F*2+E?(K=L/(2*F+E),F*=K,E*=K):(M=Math.floor((L+E)/(F+E)),q=(L-M*F)/(M-1),G=(L-(M+1)*F)/M,E=G<=0||Math.abs(E-q)<Math.abs(E-G)?q:G),R&&(i===3?this.ctx.setLineDash([0,F+E]):this.ctx.setLineDash([F,E])),i===3?(this.ctx.lineCap="round",this.ctx.lineWidth=t):this.ctx.lineWidth=t*2+1.1,this.ctx.strokeStyle=lA(r),this.ctx.stroke(),this.ctx.setLineDash([]),i===2&&(HA(s[0])&&(P=s[3],b=s[0],this.ctx.beginPath(),this.formatPath([new I(P.end.x,P.end.y),new I(b.start.x,b.start.y)]),this.ctx.stroke()),HA(s[1])&&(P=s[1],b=s[2],this.ctx.beginPath(),this.formatPath([new I(P.end.x,P.end.y),new I(b.start.x,b.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},A.prototype.render=function(r){return a(this,void 0,void 0,function(){var t;return c(this,function(n){switch(n.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=lA(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),t=yB(r),[4,this.renderStack(t)];case 1:return n.sent(),this.applyEffects([]),[2,this.canvas]}})})},A}(zo),NB=function(e){return e instanceof Uo||e instanceof po?!0:e instanceof Tr&&e.type!==Mt&&e.type!==xt},RB=function(e,A){switch(e){case 0:return Pt(A);case 2:return FB(A);case 1:default:return Nt(A)}},GB=function(e){switch(e){case 1:return"center";case 2:return"right";case 0:default:return"left"}},kB=["-apple-system","system-ui"],VB=function(e){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?e.filter(function(A){return kB.indexOf(A)===-1}):e},XB=function(e){S(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.canvas=t.canvas?t.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),n.options=t,n.canvas.width=Math.floor(t.width*t.scale),n.canvas.height=Math.floor(t.height*t.scale),n.canvas.style.width=t.width+"px",n.canvas.style.height=t.height+"px",n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-t.x,-t.y),n.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale),n}return A.prototype.render=function(r){return a(this,void 0,void 0,function(){var t,n;return c(this,function(o){switch(o.label){case 0:return t=Kr(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,r),[4,JB(t)];case 1:return n=o.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=lA(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(n,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},A}(zo),JB=function(e){return new Promise(function(A,r){var t=new Image;t.onload=function(){A(t)},t.onerror=r,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},WB=function(){function e(A){var r=A.id,t=A.enabled;this.id=r,this.enabled=t,this.start=Date.now()}return e.prototype.debug=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,B([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.getTime=function(){return Date.now()-this.start},e.prototype.info=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,B([this.id,this.getTime()+"ms"],A))},e.prototype.warn=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,B([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.error=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,B([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.instances={},e}(),YB=function(){function e(A,r){var t;this.windowBounds=r,this.instanceName="#"+e.instanceCount++,this.logger=new WB({id:this.instanceName,enabled:A.logging}),this.cache=(t=A.cache)!==null&&t!==void 0?t:new fB(this,A)}return e.instanceCount=1,e}(),ZB=function(e,A){return A===void 0&&(A={}),$B(e,A)};typeof window<"u"&&Go.setContext(window);var $B=function(e,A){return a(void 0,void 0,void 0,function(){var r,t,n,o,i,l,s,u,f,w,h,L,F,E,R,K,M,q,G,P,Z,b,Z,$,N,aA,hA,FA,eA,uA,jA,CA,QA,se,DA,Ae,ce,le,NA,Be;return c(this,function(RA){switch(RA.label){case 0:if(!e||typeof e!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(r=e.ownerDocument,!r)throw new Error("Element is not attached to a Document");if(t=r.defaultView,!t)throw new Error("Document is not attached to a Window");return n={allowTaint:($=A.allowTaint)!==null&&$!==void 0?$:!1,imageTimeout:(N=A.imageTimeout)!==null&&N!==void 0?N:15e3,proxy:A.proxy,useCORS:(aA=A.useCORS)!==null&&aA!==void 0?aA:!1},o=p({logging:(hA=A.logging)!==null&&hA!==void 0?hA:!0,cache:A.cache},n),i={windowWidth:(FA=A.windowWidth)!==null&&FA!==void 0?FA:t.innerWidth,windowHeight:(eA=A.windowHeight)!==null&&eA!==void 0?eA:t.innerHeight,scrollX:(uA=A.scrollX)!==null&&uA!==void 0?uA:t.pageXOffset,scrollY:(jA=A.scrollY)!==null&&jA!==void 0?jA:t.pageYOffset},l=new g(i.scrollX,i.scrollY,i.windowWidth,i.windowHeight),s=new YB(o,l),u=(CA=A.foreignObjectRendering)!==null&&CA!==void 0?CA:!1,f={allowTaint:(QA=A.allowTaint)!==null&&QA!==void 0?QA:!1,onclone:A.onclone,ignoreElements:A.ignoreElements,inlineImages:u,copyStyles:u},s.logger.debug("Starting document clone with size "+l.width+"x"+l.height+" scrolled to "+-l.left+","+-l.top),w=new No(s,e,f),h=w.clonedReferenceElement,h?[4,w.toIFrame(r,l)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return L=RA.sent(),F=_r(h)||zl(h)?C(h.ownerDocument):Q(s,h),E=F.width,R=F.height,K=F.left,M=F.top,q=zB(s,h,A.backgroundColor),G={canvas:A.canvas,backgroundColor:q,scale:(DA=(se=A.scale)!==null&&se!==void 0?se:t.devicePixelRatio)!==null&&DA!==void 0?DA:1,x:((Ae=A.x)!==null&&Ae!==void 0?Ae:0)+K,y:((ce=A.y)!==null&&ce!==void 0?ce:0)+M,width:(le=A.width)!==null&&le!==void 0?le:Math.ceil(E),height:(NA=A.height)!==null&&NA!==void 0?NA:Math.ceil(R)},u?(s.logger.debug("Document cloned, using foreign object rendering"),Z=new XB(s,G),[4,Z.render(h)]):[3,3];case 2:return P=RA.sent(),[3,5];case 3:return s.logger.debug("Document cloned, element located at "+K+","+M+" with size "+E+"x"+R+" using computed rendering"),s.logger.debug("Starting DOM parsing"),b=vo(s,h),q===b.styles.backgroundColor&&(b.styles.backgroundColor=_A.TRANSPARENT),s.logger.debug("Starting renderer for element at "+G.x+","+G.y+" with size "+G.width+"x"+G.height),Z=new PB(s,G),[4,Z.render(b)];case 4:P=RA.sent(),RA.label=5;case 5:return(!((Be=A.removeContainer)!==null&&Be!==void 0)||Be)&&(No.destroy(L)||s.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),s.logger.debug("Finished rendering"),[2,P]}})})},zB=function(e,A,r){var t=A.ownerDocument,n=t.documentElement?Ve(e,getComputedStyle(t.documentElement).backgroundColor):_A.TRANSPARENT,o=t.body?Ve(e,getComputedStyle(t.body).backgroundColor):_A.TRANSPARENT,i=typeof r=="string"?Ve(e,r):r===null?_A.TRANSPARENT:4294967295;return A===t.documentElement?ZA(n)?ZA(o)?i:o:n:i};return ZB})});var y=qo(jB());function Ag(v,S){v&1&&(H(0,"div",8),V(1,"ion-spinner",9),H(2,"p"),X(3,"Calculating travel times..."),T()())}function eg(v,S){if(v&1&&V(0,"img",29),v&2){let p=tA(2);je("alt",p.center.name)}}function tg(v,S){if(v&1&&V(0,"img",30),v&2){let p=tA(2);je("alt",p.center.name)}}function rg(v,S){if(v&1&&V(0,"img",31),v&2){let p=tA(2);je("alt",p.center.name)}}function ng(v,S){if(v&1&&V(0,"img",32),v&2){let p=tA(2);je("alt",p.center.name)}}function og(v,S){if(v&1&&(H(0,"ion-chip",18),V(1,"ion-icon",33),H(2,"ion-label"),X(3),T()()),v&2){let p=tA(2);k("color",p.getStatusColor(p.center.status)),O(3),yA(p.center.status)}}function ig(v,S){if(v&1&&(H(0,"div",21)(1,"ion-item",22),V(2,"ion-icon",34),H(3,"ion-label")(4,"h2"),X(5,"Capacity"),T(),H(6,"p"),X(7),T()()()()),v&2){let p=tA(2);O(7),bA("",p.center.capacity," people")}}function ag(v,S){if(v&1){let p=ee();H(0,"ion-card",35),sA("click",function(){let c=GA(p).$implicit,B=tA(2);return kA(B.selectTravelMode(c.mode))}),H(1,"ion-card-header"),V(2,"ion-icon",36),H(3,"ion-card-title"),X(4),T()(),H(5,"ion-card-content")(6,"div",37)(7,"div",38),V(8,"ion-icon",39),H(9,"span"),X(10),T()(),H(11,"div",40),V(12,"ion-icon",41),H(13,"span"),X(14),T()()(),H(15,"ion-button",42),sA("click",function(){let c=GA(p).$implicit,B=tA(2);return kA(B.selectTravelMode(c.mode))}),X(16," Select "),V(17,"ion-icon",43),T()()()}if(v&2){let p=S.$implicit,a=tA(2);O(2),k("name",p.icon)("color",p.color),O(2),bA(" ",p.mode==="foot-walking"?"Walking":p.mode==="cycling-regular"?"Cycling":"Driving"," "),O(6),yA(a.formatTime(p.time)),O(4),yA(a.formatDistance(p.distance)),O(),k("color",p.color)}}function sg(v,S){if(v&1&&(H(0,"div",10)(1,"div",11),wA(2,eg,1,1,"img",12)(3,tg,1,1,"img",13)(4,rg,1,1,"img",14)(5,ng,1,1,"img",15),T(),H(6,"h1",16),X(7),T(),H(8,"div",17)(9,"ion-chip",18),V(10,"ion-icon",19),H(11,"ion-label"),X(12),T()(),wA(13,og,4,2,"ion-chip",20),T(),H(14,"div",21)(15,"ion-item",22),V(16,"ion-icon",23),H(17,"ion-label")(18,"h2"),X(19,"Address"),T(),H(20,"p"),X(21),T()()()(),H(22,"div",21)(23,"ion-item",22),V(24,"ion-icon",24),H(25,"ion-label")(26,"h2"),X(27,"Contact"),T(),H(28,"p"),X(29),T()()()(),wA(30,ig,8,1,"div",25),H(31,"div",26)(32,"h2"),X(33,"Travel Time Estimates"),T(),H(34,"div",27),wA(35,ag,18,6,"ion-card",28),T()()()),v&2){let p=tA();O(2),k("ngIf",p.center.disaster_type&&p.center.disaster_type.toLowerCase().includes("earthquake")),O(),k("ngIf",p.center.disaster_type&&p.center.disaster_type.toLowerCase().includes("flood")),O(),k("ngIf",p.center.disaster_type&&p.center.disaster_type.toLowerCase().includes("typhoon")),O(),k("ngIf",!p.center.disaster_type||!p.center.disaster_type.toLowerCase().includes("earthquake")&&!p.center.disaster_type.toLowerCase().includes("flood")&&!p.center.disaster_type.toLowerCase().includes("typhoon")),O(2),yA(p.center.name),O(2),k("color",p.getStatusColor(p.center.status)),O(),k("name",p.getDisasterTypeIcon(p.center.disaster_type)),O(2),yA(p.center.disaster_type||"General"),O(),k("ngIf",p.center.status),O(8),yA(p.center.address||"No address available"),O(8),yA(p.center.contact||"No contact information available"),O(),k("ngIf",p.center.capacity),O(5),k("ngForOf",p.travelEstimates)}}var yi=(()=>{let S=class S{constructor(a,c,B,g){this.modalCtrl=a,this.http=c,this.toastCtrl=B,this.mapboxRouting=g,this.travelEstimates=[],this.selectedMode="foot-walking",this.isLoading=!0}ngOnInit(){return J(this,null,function*(){this.isLoading=!0,yield this.calculateTravelTimes(),this.isLoading=!1})}calculateTravelTimes(){return J(this,null,function*(){let a=[{id:"foot-walking",name:"Walking",icon:"walk-outline",color:"primary"},{id:"cycling-regular",name:"Cycling",icon:"bicycle-outline",color:"success"},{id:"driving-car",name:"Driving",icon:"car-outline",color:"danger"}];this.travelEstimates=[];let c=Number(this.center.latitude),B=Number(this.center.longitude);if(console.log("Calculating travel times with coordinates:",{userLat:this.userLat,userLng:this.userLng,centerLat:c,centerLng:B}),isNaN(c)||isNaN(B)||isNaN(this.userLat)||isNaN(this.userLng)){console.error("Invalid coordinates for travel time calculations:",{userLat:this.userLat,userLng:this.userLng,centerLat:c,centerLng:B}),this.toastCtrl.create({message:"Invalid coordinates. Using estimated travel times.",duration:3e3,color:"warning",position:"bottom"}).then(g=>g.present()),this.useFallbackCalculations(a);return}for(let g of a)try{let Q=yield this.getTravelTimeEstimate(this.userLat,this.userLng,c,B,g.id);this.travelEstimates.push({mode:g.id,time:Q.time,distance:Q.distance,icon:g.icon,color:g.color})}catch(Q){if(console.error(`Error calculating ${g.name} time:`,Q),this.travelEstimates.length===0){let m="Using estimated travel times due to connection issues";Q.message&&(Q.message.includes("Invalid coordinates")?m="Invalid coordinates. Using estimated travel times.":Q.message.includes("API Error")&&(m=`${Q.message}. Using estimated travel times.`)),this.toastCtrl.create({message:m,duration:3e3,color:"warning",position:"bottom"}).then(_=>_.present())}let C=this.calculateStraightLineDistance(this.userLat,this.userLng,c,B),U;switch(g.id){case"foot-walking":U=5e3/3600;break;case"cycling-regular":U=15e3/3600;break;case"driving-car":U=4e4/3600;break;default:U=5e3/3600}let d=C/U;this.travelEstimates.push({mode:g.id,time:d,distance:C,icon:g.icon,color:g.color})}})}useFallbackCalculations(a){let c=Number(this.center.latitude),B=Number(this.center.longitude),g=isNaN(this.userLat)?10.3157:this.userLat,Q=isNaN(this.userLng)?123.8854:this.userLng,C=isNaN(c)?10.3257:c,U=isNaN(B)?123.8954:B,d=this.calculateStraightLineDistance(g,Q,C,U);console.log(`Using fallback calculation with distance: ${d} meters`);for(let m of a){let _;switch(m.id){case"foot-walking":_=5e3/3600;break;case"cycling-regular":_=15e3/3600;break;case"driving-car":_=4e4/3600;break;default:_=5e3/3600}let D=d/_;this.travelEstimates.push({mode:m.id,time:D,distance:d,icon:m.icon,color:m.color})}}getTravelTimeEstimate(a,c,B,g,Q){return J(this,null,function*(){if([a,c,B,g].some(C=>typeof C!="number"||isNaN(C)))throw console.error("Invalid coordinates for travel time estimate:",{startLat:a,startLng:c,endLat:B,endLng:g}),new Error("Invalid coordinates");if(Math.abs(a)>90||Math.abs(B)>90||Math.abs(c)>180||Math.abs(g)>180)throw console.error("Coordinates out of range for travel time estimate:",{startLat:a,startLng:c,endLat:B,endLng:g}),new Error("Coordinates out of range");console.log(`Calculating Mapbox route from [${a}, ${c}] to [${B}, ${g}] using mode: ${Q}`);try{let C=this.mapboxRouting.convertTravelModeToProfile(Q),U=yield this.mapboxRouting.getDirections(c,a,g,B,C,{geometries:"geojson",overview:"simplified",steps:!1});if(!U.routes||U.routes.length===0)throw new Error("No routes found");let d=U.routes[0];return console.log(`Received Mapbox response for ${Q} route:`,{duration:d.duration,distance:d.distance}),{time:d.duration,distance:d.distance}}catch(C){throw console.error(`Failed to fetch ${Q} route from Mapbox:`,C),C.message?C.message.includes("Invalid Mapbox access token")?new Error("Invalid Mapbox access token. Please check your token configuration."):C.message.includes("Rate limit exceeded")?new Error("Too many requests to Mapbox. Please wait a moment and try again."):C.message.includes("Network error")?new Error("Network error. Please check your internet connection."):C.message.includes("No routes found")?new Error("No route could be calculated between these points."):new Error(`Mapbox routing error: ${C.message}`):C}})}calculateStraightLineDistance(a,c,B,g){let C=a*Math.PI/180,U=B*Math.PI/180,d=(B-a)*Math.PI/180,m=(g-c)*Math.PI/180,_=Math.sin(d/2)*Math.sin(d/2)+Math.cos(C)*Math.cos(U)*Math.sin(m/2)*Math.sin(m/2);return 6371e3*(2*Math.atan2(Math.sqrt(_),Math.sqrt(1-_)))}formatTime(a){let c=Math.round(a/60);if(c<60)return`${c} min`;{let B=Math.floor(c/60),g=c%60;return`${B} hr ${g} min`}}formatDistance(a){return a<1e3?`${Math.round(a)} m`:`${(a/1e3).toFixed(2)} km`}selectTravelMode(a){this.selectedMode=a,this.dismiss(a)}dismiss(a){this.modalCtrl.dismiss({selectedMode:a||null})}getDisasterTypeIcon(a){if(!a)return"alert-circle-outline";let c=a.toLowerCase();return c.includes("earthquake")||c.includes("quake")?"earth-outline":c.includes("flood")||c.includes("flash")?"water-outline":c.includes("typhoon")||c.includes("storm")?"thunderstorm-outline":c.includes("fire")?"flame-outline":"alert-circle-outline"}getStatusColor(a){if(!a)return"medium";let c=a.toLowerCase();return c.includes("active")||c.includes("open")?"success":c.includes("inactive")||c.includes("closed")?"warning":c.includes("full")?"danger":"medium"}};S.\u0275fac=function(c){return new(c||S)(qe(Zt),qe(Jt),qe(At),qe($t))},S.\u0275cmp=ve({type:S,selectors:[["app-evacuation-center-details"]],inputs:{center:"center",userLat:"userLat",userLng:"userLng"},decls:14,vars:2,consts:[[1,"ion-no-border"],["slot","start"],[3,"click"],["name","chevron-back-outline","slot","icon-only"],[1,"ion-padding"],["class","loading-container",4,"ngIf"],["class","details-container",4,"ngIf"],["expand","block","color","medium",3,"click"],[1,"loading-container"],["name","circles"],[1,"details-container"],[1,"center-image"],["src","assets/earthquake.png",3,"alt",4,"ngIf"],["src","assets/flood.png",3,"alt",4,"ngIf"],["src","assets/typhoon.png",3,"alt",4,"ngIf"],["src","assets/ALERTO.png",3,"alt",4,"ngIf"],[1,"center-name"],[1,"center-type"],[3,"color"],[3,"name"],[3,"color",4,"ngIf"],[1,"info-section"],["lines","none"],["name","location-outline","slot","start","color","primary"],["name","call-outline","slot","start","color","primary"],["class","info-section",4,"ngIf"],[1,"travel-section"],[1,"travel-cards"],["class","travel-card",3,"click",4,"ngFor","ngForOf"],["src","assets/earthquake.png",3,"alt"],["src","assets/flood.png",3,"alt"],["src","assets/typhoon.png",3,"alt"],["src","assets/ALERTO.png",3,"alt"],["name","information-circle-outline"],["name","people-outline","slot","start","color","primary"],[1,"travel-card",3,"click"],[1,"travel-icon",3,"name","color"],[1,"travel-info"],[1,"travel-time"],["name","time-outline"],[1,"travel-distance"],["name","navigate-outline"],["expand","block","fill","clear",3,"click","color"],["name","arrow-forward-outline","slot","end"]],template:function(c,B){c&1&&(H(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),sA("click",function(){return B.dismiss()}),V(4,"ion-icon",3),T()(),H(5,"ion-title"),X(6,"Evacuation Center"),T()()(),H(7,"ion-content",4),wA(8,Ag,4,0,"div",5)(9,sg,36,13,"div",6),T(),H(10,"ion-footer",0)(11,"ion-toolbar")(12,"ion-button",7),sA("click",function(){return B.dismiss()}),X(13," Back to Map "),T()()()),c&2&&(O(8),k("ngIf",B.isLoading),O(),k("ngIf",!B.isLoading))},dependencies:[Le,Ie,oi,ii,ai,si,ci,li,Wt,fi,wi,ye,Yt,be,Ci,Qi,pi,He,Xt,Ee],styles:["ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--border-color: transparent}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:200px}.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{width:48px;height:48px;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium)}.details-container[_ngcontent-%COMP%]{padding-bottom:20px}.center-image[_ngcontent-%COMP%]{width:100%;height:180px;border-radius:12px;overflow:hidden;margin-bottom:16px;background-color:#f5f5f5;display:flex;justify-content:center;align-items:center}.center-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;max-height:100%;object-fit:contain;padding:16px}.center-name[_ngcontent-%COMP%]{font-size:24px;font-weight:700;margin:0 0 8px;color:var(--ion-color-dark)}.center-type[_ngcontent-%COMP%]{display:flex;gap:8px;margin-bottom:16px;flex-wrap:wrap}.info-section[_ngcontent-%COMP%]{margin-bottom:16px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;--inner-padding-end: 0;--background: transparent}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px;font-weight:600;margin-bottom:4px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:var(--ion-color-medium)}.travel-section[_ngcontent-%COMP%]{margin-top:24px}.travel-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:18px;font-weight:600;margin-bottom:16px;color:var(--ion-color-dark)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;box-shadow:0 4px 12px #00000014;transition:transform .2s ease,box-shadow .2s ease}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]:active{transform:scale(.98);box-shadow:0 2px 8px #0000001a}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 16px 0}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   .travel-icon[_ngcontent-%COMP%]{font-size:28px;margin-right:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px 16px 16px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]{display:flex;align-items:center}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;margin-right:6px;color:var(--ion-color-medium)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:15px;font-weight:500;color:var(--ion-color-dark)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:8px;font-weight:500}ion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--border-color: transparent;padding:0 16px 16px}"]});let v=S;return v})();function cg(v,S){if(v&1&&(H(0,"p"),X(1),T()),v&2){let p=tA();O(),ti(" ",p.formatDistance(p.totalDistance)," \u2022 ",p.formatTime(p.totalDuration)," ")}}function lg(v,S){if(v&1&&(H(0,"span"),X(1),T()),v&2){let p=tA(2).$implicit,a=tA();O(),bA(" \u2022 ",a.formatTime(p.duration),"")}}function Bg(v,S){if(v&1&&(H(0,"p"),X(1),wA(2,lg,2,1,"span",4),T()),v&2){let p=tA().$implicit,a=tA();O(),bA(" ",a.formatDistance(p.distance)," "),O(),k("ngIf",p.duration>0)}}function gg(v,S){if(v&1&&(H(0,"ion-item",9),V(1,"ion-icon",3),H(2,"ion-label"),V(3,"h3",10),wA(4,Bg,3,2,"p",4),T(),H(5,"ion-note",11),X(6),T()()),v&2){let p=S.$implicit,a=S.index,c=tA();O(),k("name",c.getDirectionIcon(p.type))("color",c.getTravelModeColor()),O(2),k("innerHTML",p.instruction,Ai),O(),k("ngIf",p.distance>0),O(2),yA(a+1)}}var bi=(()=>{let S=class S{constructor(){this.directions=[],this.travelMode="foot-walking",this.totalDistance=null,this.totalDuration=null,this.close=new jo}getTravelModeName(){switch(this.travelMode){case"foot-walking":return"Walking";case"cycling-regular":return"Cycling";case"driving-car":return"Driving";default:return"Traveling"}}getTravelModeIcon(){switch(this.travelMode){case"foot-walking":return"walk-outline";case"cycling-regular":return"bicycle-outline";case"driving-car":return"car-outline";default:return"navigate-outline"}}getTravelModeColor(){switch(this.travelMode){case"foot-walking":return"primary";case"cycling-regular":return"success";case"driving-car":return"danger";default:return"medium"}}formatTime(a){let c=Math.round(a/60);if(c<60)return`${c} min`;{let B=Math.floor(c/60),g=c%60;return`${B} hr ${g} min`}}formatDistance(a){return a<1e3?`${Math.round(a)} m`:`${(a/1e3).toFixed(2)} km`}closePanel(){this.close.emit()}getDirectionIcon(a){switch(a){case 0:return"arrow-forward-outline";case 1:return"arrow-forward-outline";case 2:return"arrow-forward-outline";case 3:return"arrow-forward-outline";case 4:return"arrow-back-outline";case 5:return"arrow-back-outline";case 6:return"arrow-back-outline";case 7:return"arrow-back-outline";case 8:return"arrow-down-outline";case 9:return"flag-outline";case 10:return"arrow-up-outline";case 11:return"arrow-forward-outline";case 12:return"arrow-forward-outline";case 13:return"arrow-forward-outline";case 14:return"arrow-forward-outline";case 15:return"flag-outline";default:return"navigate-outline"}}};S.\u0275fac=function(c){return new(c||S)},S.\u0275cmp=ve({type:S,selectors:[["app-directions-panel"]],inputs:{directions:"directions",travelMode:"travelMode",totalDistance:"totalDistance",totalDuration:"totalDuration"},outputs:{close:"close"},decls:13,vars:5,consts:[[1,"directions-panel"],[1,"directions-header"],["lines","none"],["slot","start",3,"name","color"],[4,"ngIf"],["fill","clear","slot","end",3,"click"],["name","close-outline","slot","icon-only"],[1,"directions-list"],["lines","full",4,"ngFor","ngForOf"],["lines","full"],[3,"innerHTML"],["slot","end"]],template:function(c,B){c&1&&(H(0,"div",0)(1,"div",1)(2,"ion-item",2),V(3,"ion-icon",3),H(4,"ion-label")(5,"h2"),X(6),T(),wA(7,cg,2,2,"p",4),T(),H(8,"ion-button",5),sA("click",function(){return B.closePanel()}),V(9,"ion-icon",6),T()()(),H(10,"div",7)(11,"ion-list"),wA(12,gg,7,5,"ion-item",8),T()()()),c&2&&(O(3),k("name",B.getTravelModeIcon())("color",B.getTravelModeColor()),O(3),bA("",B.getTravelModeName()," Directions"),O(),k("ngIf",B.totalDistance&&B.totalDuration),O(5),k("ngForOf",B.directions))},dependencies:[Le,Ie,ye,Yt,be,di,hi,He,Xt,Ee],styles:[".directions-panel[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background-color:#fff;border-top-left-radius:15px;border-top-right-radius:15px;box-shadow:0 -2px 10px #0000001a;max-height:50vh;overflow-y:auto;z-index:1000}.directions-header[_ngcontent-%COMP%]{padding:10px 0;border-bottom:1px solid #eee;position:sticky;top:0;background-color:#fff;z-index:1001}.directions-list[_ngcontent-%COMP%]{max-height:calc(50vh - 60px);overflow-y:auto}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--inner-padding-end: 16px}ion-icon[_ngcontent-%COMP%]{font-size:24px}h2[_ngcontent-%COMP%]{font-weight:600;margin:0}h3[_ngcontent-%COMP%]{font-size:14px;font-weight:500;margin:0}p[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);margin:4px 0 0}ion-note[_ngcontent-%COMP%]{font-size:12px;padding:4px 8px;border-radius:50%;background-color:var(--ion-color-light);color:var(--ion-color-dark);display:flex;align-items:center;justify-content:center;min-width:24px;min-height:24px}"]});let v=S;return v})();var xi=qo(Li());function ug(v,S){v&1&&(H(0,"div",29),V(1,"ion-icon",24),H(2,"span"),X(3,"Offline Mode - Using cached data"),T()())}function fg(v,S){if(v&1){let p=ee();H(0,"div",30)(1,"ion-button",31),sA("click",function(){GA(p);let c=tA();return kA(c.requestLocationExplicitly())}),V(2,"ion-icon",32),X(3," Enable Location Access "),T(),H(4,"p",33),X(5,"Tap the button above to enable location access"),T()()}}function wg(v,S){v&1&&(H(0,"div",34),V(1,"ion-icon",35),H(2,"p"),X(3,"Showing your current location"),T(),H(4,"small"),X(5,"Search for evacuation centers or select a disaster type to see routes"),T()())}function dg(v,S){if(v&1){let p=ee();H(0,"div",36),sA("click",function(){GA(p);let c=tA();return kA(c.showDirectionsPanel=!0)}),V(1,"ion-icon",37),H(2,"div",38)(3,"strong"),X(4),T(),X(5),H(6,"div",39),X(7),T()(),V(8,"ion-icon",40),T()}if(v&2){let p=tA();O(),k("name",p.travelMode==="foot-walking"?"walk-outline":p.travelMode==="cycling-regular"?"bicycle-outline":"car-outline")("color",p.travelMode==="foot-walking"?"primary":p.travelMode==="cycling-regular"?"success":"danger"),O(3),bA("",(p.routeTime/60).toFixed(0)," min"),O(),bA(" \u2022 ",(p.routeDistance/1e3).toFixed(2)," km "),O(2),yA(p.getTravelModeName())}}function hg(v,S){if(v&1){let p=ee();H(0,"app-directions-panel",41),sA("close",function(){GA(p);let c=tA();return kA(c.showDirectionsPanel=!1)}),T()}if(v&2){let p=tA();k("directions",p.currentDirections)("travelMode",p.travelMode)("totalDistance",p.routeDistance)("totalDuration",p.routeTime)}}function Cg(v,S){if(v&1&&(H(0,"div",42),V(1,"ion-icon",8),H(2,"span"),X(3),T()()),v&2){let p=tA();O(),k("name",p.currentDisasterType.toLowerCase().includes("earthquake")?"earth-outline":p.currentDisasterType.toLowerCase().includes("typhoon")?"thunderstorm-outline":p.currentDisasterType.toLowerCase().includes("flood")?"water-outline":"alert-circle-outline"),O(2),bA("",p.currentDisasterType," Evacuation Centers")}}function Qg(v,S){if(v&1){let p=ee();H(0,"ion-fab",43)(1,"ion-fab-button",44),sA("click",function(){GA(p);let c=tA();return kA(c.routeToTwoNearestCenters())}),V(2,"ion-icon",45),T(),H(3,"ion-label",17),X(4,"Route to Nearest Centers"),T()()}}function pg(v,S){if(v&1){let p=ee();H(0,"ion-fab",46)(1,"ion-fab-button",47),sA("click",function(){GA(p);let c=tA();return kA(c.showDirectionsPanel=!0)}),V(2,"ion-icon",48),T(),H(3,"ion-label",17),X(4,"Show Directions"),T()()}}var $g=(()=>{let S=class S{getTravelModeName(){switch(this.travelMode){case"foot-walking":return"Walking";case"cycling-regular":return"Cycling";case"driving-car":return"Driving";default:return"Traveling"}}findTwoNearestCenters(a,c,B){if(!B.length)return[];let g=B;if(this.currentDisasterType&&this.currentDisasterType!=="all"){console.log(`Filtering centers by disaster type: ${this.currentDisasterType}`);let C=this.currentDisasterType.toLowerCase();if(g=B.filter(U=>{if(!U.disaster_type)return!1;let d=U.disaster_type.toLowerCase();return C==="earthquake"||C==="earthquakes"?d.includes("earthquake")||d.includes("quake"):C==="typhoon"||C==="typhoons"?d.includes("typhoon")||d.includes("storm")||d.includes("hurricane"):C==="flood"||C==="floods"?d.includes("flood")||d.includes("flash"):d===C}),console.log(`Filtered to ${g.length} centers for disaster type: ${this.currentDisasterType}`),g.length===0)return console.log(`No centers found for disaster type: ${this.currentDisasterType}`),[]}return[...g].sort((C,U)=>{let d=this.calculateDistance(a,c,Number(C.latitude),Number(C.longitude)),m=this.calculateDistance(a,c,Number(U.latitude),Number(U.longitude));return d-m}).slice(0,2)}updateRoute(){this.routeToTwoNearestCenters()}requestLocationExplicitly(){return J(this,null,function*(){console.log("User explicitly requested location access via button click"),this.showLocationRequestButton=!1,yield this.loadingService.showLoading("Getting your location...");try{try{let g=yield pA.checkPermissions();if(console.log("Permission status:",g),g.location!=="granted"){console.log("Requesting permissions explicitly...");let Q=yield pA.requestPermissions();if(console.log("Permission request result:",Q),Q.location!=="granted")throw new Error("Location permission denied")}}catch(g){console.log("Permission check failed, might be in browser:",g)}let a=yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:3e4,maximumAge:0});console.log("Successfully got position:",a);let c=a.coords.latitude,B=a.coords.longitude;yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Location access successful!",duration:2e3,color:"success"}).then(g=>g.present()),this.gpsEnabled=!0,this.map?(this.userMarker?(this.userMarker.setLatLng([c,B]),this.map.setView([c,B],15)):this.updateUserMarker(c,B),this.startWatchingPosition()):this.initializeMap(c,B)}catch(a){console.error("Error getting location:",a),yield this.loadingService.dismissLoading(),this.showLocationRequestButton=!0,yield(yield this.alertCtrl.create({header:"Location Access Failed",message:"We couldn't access your location. Would you like to see help on enabling location access?",buttons:[{text:"Show Help",handler:()=>{this.showLocationHelp()}},{text:"Try Again",handler:()=>{this.requestLocationExplicitly()}},{text:"Cancel",role:"cancel"}]})).present()}})}showLocationHelp(){return J(this,null,function*(){let a="To use location services:";navigator.userAgent.includes("Chrome")?a+='<br><br><b>Chrome:</b><br>1. Click the lock/info icon in the address bar<br>2. Select "Site settings"<br>3. Change Location permission to "Allow"<br>':navigator.userAgent.includes("Firefox")?a+='<br><br><b>Firefox:</b><br>1. Click the lock icon in the address bar<br>2. Select "Site Permissions"<br>3. Change "Access Your Location" to "Allow"<br>':navigator.userAgent.includes("Safari")?a+='<br><br><b>Safari:</b><br>1. Open Safari settings<br>2. Go to Websites > Location<br>3. Ensure this website is set to "Allow"<br>':a+="<br><br>Please enable location access for this website in your browser settings.",a+="<br><br>On mobile devices, also ensure that:<br>1. Your device location/GPS is turned on<br>2. The app has permission to access your location",yield(yield this.alertCtrl.create({header:"Location Services Help",message:a,buttons:[{text:"Try Again",handler:()=>{this.requestLocationExplicitly()}},{text:"OK",role:"cancel"}]})).present()})}routeToTwoNearestCenters(){return J(this,null,function*(){try{if(!this.gpsEnabled){console.log("GPS is disabled, not calculating routes"),(yield this.toastCtrl.create({message:"Please enable GPS to see evacuation routes",duration:3e3,color:"warning"})).present();return}console.log("Forcing fresh GPS position check for routing...");try{let a=yield this.getCurrentPositionWithFallback(),c=a.coords.latitude,B=a.coords.longitude;console.log(`Got fresh GPS position: [${c}, ${B}]`),this.userMarker?(this.userMarker.setLatLng([c,B]),this.map.setView([c,B],15)):this.userMarker=y.marker([c,B],{icon:y.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map);let g=c,Q=B;this.toastCtrl.create({message:"Using your current real-time location",duration:2e3,color:"success"}).then(U=>U.present()),console.log(`Using FRESH GPS coordinates for routing: [${g}, ${Q}]`),(!this.evacuationCenters||this.evacuationCenters.length===0)&&(yield this.loadEvacuationCenters(g,Q));let C=this.findTwoNearestCenters(g,Q,this.evacuationCenters);if(C.length===0){(yield this.toastCtrl.create({message:"No evacuation centers found.",duration:3e3,color:"danger"})).present();return}console.log("Aggressively clearing ALL existing routes"),this.map.eachLayer(U=>{U instanceof y.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(U))});for(let U of C){let d=Number(U.latitude),m=Number(U.longitude);if(console.log(`Calculating route from [${g}, ${Q}] to center: ${U.name} with disaster type: ${U.disaster_type}`),console.log(`Center coordinates: [${d}, ${m}], types: [${typeof d}, ${typeof m}]`),isNaN(d)||isNaN(m)){console.error("Invalid center coordinates:",{centerLat:d,centerLng:m,center:U});continue}yield this.getRealRoute(g,Q,d,m,this.travelMode,U.disaster_type)}if(this.userMarker){let U="You are here!.";C.forEach((d,m)=>{let _=this.calculateDistance(g,Q,Number(d.latitude),Number(d.longitude));U+=`<br> \u2022 <strong>${d.name}</strong> <br> Distance: ${(_/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(U).openPopup()}}catch(a){console.error("Failed to get fresh GPS position:",a),(yield this.toastCtrl.create({message:"Could not get your current location. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}}catch(a){(yield this.toastCtrl.create({message:"Failed to get your location or route.",duration:3e3,color:"danger"})).present(),console.error("Failed to route to two nearest centers",a)}})}constructor(){this.travelMode="foot-walking",this.routeTime=null,this.routeDistance=null,this.userMarker=null,this.evacuationCenters=[],this.gpsEnabled=!0,this.isOnline=!0,this.loadingService=xA(vi),this.mapboxRouting=xA($t),this.offlineStorage=xA(Fi),this.offlineMapService=xA(mi),this.toastController=xA(At),this.alertCtrl=xA(Ui),this.toastCtrl=xA(At),this.modalCtrl=xA(Zt),this.http=xA(Jt),this.watchId=null,this.currentDisasterType="all",this.isFilterMode=!1,this.currentDirections=[],this.showDirectionsPanel=!1,this.showLocationRequestButton=!1,this.ORS_API_KEY=te.orsApiKey,this.isLoadingCenters=!1,this.lastErrorToast=0,this.ERROR_TOAST_DEBOUNCE=5e3,this.route=xA(ri)}setupNetworkMonitoring(){window.addEventListener("online",()=>{console.log("\u{1F310} Network connection restored"),this.handleNetworkOnline()}),window.addEventListener("offline",()=>{console.log("\u{1F4E1} Network connection lost - switching to offline mode"),this.handleNetworkOffline()}),navigator.onLine||(console.log("\u{1F4E1} Starting in offline mode"),this.handleNetworkOffline())}handleNetworkOnline(){return J(this,null,function*(){if(this.offlineStorage.isOfflineMode()){console.log("\u{1F504} Manual offline mode is enabled, staying offline");return}console.log("\u{1F310} Switching back to online mode"),this.map&&(this.map.eachLayer(a=>{a instanceof y.TileLayer&&this.map.removeLayer(a)}),y.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors",maxZoom:19,minZoom:8}).addTo(this.map)),yield this.syncOfflineData(),this.showNetworkStatusToast("\u{1F310} Connection restored - Online mode active","success")})}handleNetworkOffline(){return J(this,null,function*(){console.log("\u{1F4E1} Automatically switching to offline mode due to network loss"),console.log("\u{1F50D} DEBUG: handleNetworkOffline called"),this.map&&(console.log("\u{1F50D} DEBUG: Switching map tiles to offline"),this.map.eachLayer(c=>{c instanceof y.TileLayer&&this.map.removeLayer(c)}),this.offlineMapService.createOfflineTileLayer().addTo(this.map),console.log("\u{1F50D} DEBUG: Offline tiles added")),console.log("\u{1F50D} DEBUG: About to load offline evacuation centers"),yield this.loadOfflineEvacuationCenters(),this.showOfflineTransitionAlert()})}showOfflineTransitionAlert(){return J(this,null,function*(){yield(yield this.alertCtrl.create({header:"\u{1F4E1} Connection Lost",message:`Your internet connection has been lost. The app has automatically switched to offline mode using cached data.

      <strong>Available offline:</strong>
      \u2022 Cached evacuation centers
      \u2022 Basic map tiles
      \u2022 Your current location

      <strong>Limited offline:</strong>
      \u2022 No routing/directions
      \u2022 No real-time updates`,buttons:[{text:"Continue Offline",role:"confirm",cssClass:"alert-button-confirm"}],cssClass:"offline-transition-alert"})).present()})}showNetworkStatusToast(a,c){return J(this,null,function*(){yield(yield this.toastController.create({message:a,duration:3e3,position:"top",color:c,buttons:[{text:"OK",role:"cancel"}]})).present()})}syncOfflineData(){return J(this,null,function*(){try{console.log("\u{1F504} Syncing evacuation centers for offline use...");let a=yield ze(this.http.get(`${te.apiUrl}/evacuation-centers`));if(a&&a.length>0){console.log("\u{1F50D} DEBUG: Raw centers from API:",a);let c=a.map(g=>({id:g.id,name:g.name,address:g.address||"",latitude:g.latitude,longitude:g.longitude,capacity:g.capacity,status:g.status,disaster_type:g.disaster_type,contact:g.contact}));console.log("\u{1F50D} DEBUG: Converted offline centers:",c),yield this.offlineStorage.saveEvacuationCenters(c),console.log(`\u2705 Synced ${a.length} evacuation centers for offline use`);let B=yield this.offlineStorage.getEvacuationCenters();console.log("\u{1F50D} DEBUG: Verified saved centers:",B)}}catch(a){console.warn("\u26A0\uFE0F Failed to sync offline data:",a)}})}toggleOfflineMode(){return J(this,null,function*(){this.offlineStorage.isOfflineMode()?(this.offlineStorage.setOfflineMode(!1),console.log("\u{1F310} Switched to online mode"),this.map&&(this.map.eachLayer(c=>{c instanceof y.TileLayer&&this.map.removeLayer(c)}),y.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors",maxZoom:19,minZoom:8}).addTo(this.map))):(this.offlineStorage.setOfflineMode(!0),console.log("\u{1F504} Switched to offline mode"),this.map&&(this.map.eachLayer(B=>{B instanceof y.TileLayer&&this.map.removeLayer(B)}),this.offlineMapService.createOfflineTileLayer().addTo(this.map)),yield this.loadOfflineEvacuationCenters())})}loadOfflineEvacuationCenters(){return J(this,null,function*(){try{console.log("\u{1F50D} DEBUG: Loading offline evacuation centers...");let a=yield this.offlineStorage.getEvacuationCenters();console.log("\u{1F50D} DEBUG: Raw offline centers loaded:",a),a&&a.length>0?(console.log(`\u{1F4CD} Loaded ${a.length} evacuation centers from offline storage`),this.evacuationCenters=a.map(c=>({id:c.id,name:c.name,address:c.address,latitude:c.latitude,longitude:c.longitude,capacity:c.capacity,status:c.status,disaster_type:c.disaster_type,contact:c.contact})),console.log("\u{1F50D} DEBUG: Converted evacuation centers for map:",this.evacuationCenters),console.log("\u{1F50D} DEBUG: About to add offline markers..."),this.addOfflineMarkers()):(console.warn("\u26A0\uFE0F No offline evacuation centers available"),console.log("\u{1F50D} DEBUG: offlineCenters is:",a),this.evacuationCenters=[])}catch(a){console.error("\u274C Error loading offline evacuation centers:",a),this.evacuationCenters=[]}})}addOfflineMarkers(){if(console.log("\u{1F50D} DEBUG: addOfflineMarkers called"),console.log("\u{1F50D} DEBUG: Map exists?",!!this.map),console.log("\u{1F50D} DEBUG: Evacuation centers count:",this.evacuationCenters.length),console.log("\u{1F50D} DEBUG: Evacuation centers:",this.evacuationCenters),!this.map){console.error("\u274C Map not initialized");return}if(!this.evacuationCenters.length){console.warn("\u26A0\uFE0F No evacuation centers to display");return}let a=0;this.map.eachLayer(B=>{B instanceof y.Marker&&B!==this.userMarker&&(this.map.removeLayer(B),a++)}),console.log(`\u{1F9F9} Removed ${a} existing markers`);let c=0;this.evacuationCenters.forEach((B,g)=>{console.log(`\u{1F50D} DEBUG: Processing center ${g+1}:`,B);let Q=Number(B.latitude),C=Number(B.longitude);if(console.log(`\u{1F50D} DEBUG: Coordinates: lat=${Q}, lng=${C}`),!isNaN(Q)&&!isNaN(C)){let U=this.getDisasterIcon(B.disaster_type||"");console.log(`\u{1F50D} DEBUG: Icon URL: ${U}`);let d=y.marker([Q,C],{icon:y.icon({iconUrl:U,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),m=`
          <div class="evacuation-popup">
            <h3>${B.name||"Evacuation Center"}</h3>
            <p><strong>Type:</strong> ${B.disaster_type||"General"}</p>
            <p><strong>Address:</strong> ${B.address||"N/A"}</p>
            <p><strong>Capacity:</strong> ${B.capacity||"N/A"}</p>
            <p><strong>Status:</strong> ${B.status||"N/A"}</p>
            <p><em>Offline Mode - Limited functionality</em></p>
          </div>
        `;d.bindPopup(m),d.addTo(this.map),c++,console.log(`\u2705 Added marker ${c} for: ${B.name}`)}else console.error(`\u274C Invalid coordinates for center: ${B.name} (lat=${Q}, lng=${C})`)}),console.log(`\u2705 Added ${c} offline markers to map`)}debugLoadOfflineData(){return J(this,null,function*(){console.log("\u{1F41B} DEBUG: Force loading offline data..."),yield this.loadOfflineEvacuationCenters()})}exportOfflineData(){return J(this,null,function*(){try{console.log("\u{1F4E6} Exporting offline data...");let a=yield this.offlineStorage.getEvacuationCenters(),c=this.offlineStorage.getLastSyncTime(),B=this.offlineStorage.getStorageInfo(),g={evacuation_centers:a,export_timestamp:new Date().toISOString(),last_sync_time:c,total_centers:a.length,storage_info:{used_mb:(B.used/(1024*1024)).toFixed(2),percentage:B.percentage.toFixed(1)},disaster_types:[...new Set(a.map(_=>_.disaster_type))],app_version:"Alerto v1.0"},Q=JSON.stringify(g,null,2),C=new Blob([Q],{type:"application/json"}),U=URL.createObjectURL(C),d=document.createElement("a");d.href=U,d.download=`alerto-offline-data-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(U),yield(yield this.alertCtrl.create({header:"Export Successful",message:`Exported ${a.length} evacuation centers to your downloads folder.`,buttons:["OK"]})).present(),console.log("\u2705 Offline data exported successfully")}catch(a){console.error("\u274C Failed to export offline data:",a),yield(yield this.alertCtrl.create({header:"Export Failed",message:"Failed to export offline data. Please try again.",buttons:["OK"]})).present()}})}shareOfflineData(){return J(this,null,function*(){try{console.log("\u{1F4E4} Sharing offline data...");let a=yield this.offlineStorage.getEvacuationCenters();if(a.length===0){yield(yield this.alertCtrl.create({header:"No Data to Share",message:"No offline evacuation data available to share. Please sync data first.",buttons:["OK"]})).present();return}let c=`Alerto Evacuation Centers Data

Total Centers: ${a.length}
Disaster Types: ${[...new Set(a.map(B=>B.disaster_type))].join(", ")}

Centers:
`+a.map(B=>`\u2022 ${B.name} (${B.disaster_type})
  ${B.address}
  Coordinates: ${B.latitude}, ${B.longitude}`).join(`

`)+`

Exported from Alerto App on ${new Date().toLocaleDateString()}`;navigator.share?yield navigator.share({title:"Alerto Evacuation Centers",text:c}):(yield navigator.clipboard.writeText(c),yield(yield this.toastCtrl.create({message:"Evacuation data copied to clipboard!",duration:3e3,color:"success"})).present()),console.log("\u2705 Offline data shared successfully")}catch(a){console.error("\u274C Failed to share offline data:",a),yield(yield this.toastCtrl.create({message:"Failed to share data. Please try again.",duration:3e3,color:"danger"})).present()}})}getDisasterIcon(a){if(!a)return"assets/forTyphoon.png";switch(a){case"Earthquake":return"assets/forEarthquake.png";case"Flood":return"assets/forFlood.png";case"Typhoon":return"assets/forTyphoon.png";default:return console.warn(`Unknown disaster type: ${a}, using default icon`),"assets/forTyphoon.png"}}getDisasterColor(a){if(!a)return"#3388ff";switch(a){case"Earthquake":return"#ffa500";case"Flood":return"#0000ff";case"Typhoon":return"#008000";default:return console.warn(`Unknown disaster type: ${a}, using default color`),"#3388ff"}}clearPulseCircles(){this.map.eachLayer(a=>{a instanceof y.Circle&&a.options.className==="marker-pulse"&&this.map.removeLayer(a)})}addPulsingAnimationToNearest(a){if(!a)return;this.clearPulseCircles();let c=Number(a.latitude),B=Number(a.longitude);if(isNaN(c)||isNaN(B)){console.error("Invalid coordinates for nearest center:",a);return}let g=this.getDisasterColor(a.disaster_type);y.circle([c,B],{radius:100,fillColor:g,color:g,weight:2,opacity:.8,fillOpacity:.3,className:"marker-pulse"}).addTo(this.map),console.log(`Added pulsing animation to nearest center: ${a.name} with color: ${g}`)}hasRecentErrorToast(){return Date.now()-this.lastErrorToast<this.ERROR_TOAST_DEBOUNCE}setLastErrorToast(){this.lastErrorToast=Date.now()}toggleGps(a){return J(this,null,function*(){if(console.log("GPS toggle:",a.detail.checked),this.gpsEnabled=a.detail.checked,this.gpsEnabled){console.log("Enabling GPS tracking...");try{let c=yield this.getCurrentPositionWithFallback();console.log("Position on toggle:",c);let B=c.coords.latitude,g=c.coords.longitude;this.userMarker?(this.userMarker.setLatLng([B,g]),this.userMarker.addTo(this.map)):this.updateUserMarker(B,g),this.map.setView([B,g],15),this.startWatchingPosition()}catch(c){console.error("Error enabling GPS:",c),this.gpsEnabled=!1,(yield this.toastCtrl.create({message:"Failed to enable GPS. Please check your location settings.",duration:3e3,color:"danger"})).present()}}else if(console.log("Disabling GPS tracking..."),this.userMarker&&this.userMarker.remove(),this.watchId){if(typeof this.watchId=="string")try{let c=this.watchId;pA.clearWatch({id:c})}catch(c){console.log("Error clearing Capacitor watch:",c)}else if(typeof this.watchId=="number")try{navigator.geolocation.clearWatch(this.watchId)}catch(c){console.log("Error clearing browser watch:",c)}this.watchId=null}})}ngOnInit(){return J(this,null,function*(){console.log("\u{1F5FA}\uFE0F MAIN MAP: Initializing clean map (tabs/map)..."),this.setupNetworkMonitoring(),navigator.onLine&&!this.offlineStorage.isOfflineMode()&&(yield this.syncOfflineData()),this.route.queryParams.subscribe(a=>{if(a.centerId){let c=a.centerId;console.log(`\u{1F50D} SEARCH NAVIGATION: Loading specific center ID: ${c}`),this.loadSpecificCenter(c);return}if(a.lat&&a.lng){let c=parseFloat(a.lat),B=parseFloat(a.lng),g=a.name||"Search Result",Q=a.directions==="true",C=a.viewOnly==="true";console.log(`\u{1F50D} SEARCH NAVIGATION: Loading location [${c}, ${B}] - ${g}, directions: ${Q}, viewOnly: ${C}`),Q?this.loadSearchLocationWithRouting(c,B,g):C&&this.loadSearchLocation(c,B,g);return}if(a.searchLat&&a.searchLng){let c=parseFloat(a.searchLat),B=parseFloat(a.searchLng),g=a.searchName||"Search Result";console.log(`\u{1F50D} SEARCH NAVIGATION: Loading location [${c}, ${B}] - ${g}`),this.loadSearchLocation(c,B,g);return}console.log("\u{1F5FA}\uFE0F MAIN MAP: Loading clean map with user location only"),this.loadCleanMap()})})}loadCleanMap(){return J(this,null,function*(){console.log("\u{1F5FA}\uFE0F CLEAN MAP: Loading map with user location only..."),yield this.loadingService.showLoading("Loading map...");try{let a=yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),c=a.coords.latitude,B=a.coords.longitude;console.log(`\u{1F5FA}\uFE0F CLEAN MAP: User location [${c}, ${B}]`),this.initializeMap(c,B),this.evacuationCenters=[],this.isFilterMode=!1,this.currentDisasterType="all",this.map.eachLayer(Q=>{Q instanceof y.Marker&&Q!==this.userMarker&&this.map.removeLayer(Q),Q instanceof y.GeoJSON&&this.map.removeLayer(Q)}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:"\u{1F4CD} Map ready - Search for evacuation centers to view them here",duration:3e3,color:"primary",position:"top"})).present()}catch(a){yield this.loadingService.dismissLoading(),console.error("\u{1F5FA}\uFE0F CLEAN MAP: Error loading map",a),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadCleanMap()},{text:"Use Default Location",handler:()=>this.initializeMap(10.3157,123.8854)}]})).present()}})}loadSpecificCenter(a){return J(this,null,function*(){console.log(`\u{1F50D} SPECIFIC CENTER: Loading center ID ${a}...`),yield this.loadingService.showLoading("Loading evacuation center...");try{let B=(yield ze(this.http.get(`${te.apiUrl}/evacuation-centers`))).find(Y=>Y.id.toString()===a);if(!B){yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"Center Not Found",message:"The requested evacuation center could not be found.",buttons:["OK"]})).present(),this.loadCleanMap();return}let g=yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),Q=g.coords.latitude,C=g.coords.longitude,U=Number(B.latitude),d=Number(B.longitude);this.initializeMap(Q,C);let m=this.getDisasterIcon(B.disaster_type||""),_=y.marker([U,d],{icon:y.icon({iconUrl:m,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})});_.bindPopup(`
        <div class="evacuation-popup">
          <h3>${B.name}</h3>
          <p><strong>Type:</strong> ${B.disaster_type||"General"}</p>
          <p><strong>Address:</strong> ${B.address}</p>
          <p><strong>Capacity:</strong> ${B.capacity||"N/A"}</p>
        </div>
      `).openPopup(),_.addTo(this.map);let D=y.latLngBounds([[Q,C],[U,d]]);this.map.fitBounds(D,{padding:[50,50]}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:`\u{1F4CD} Showing ${B.name}`,duration:3e3,color:"success"})).present()}catch(c){yield this.loadingService.dismissLoading(),console.error("\u{1F50D} SPECIFIC CENTER: Error loading center",c),yield(yield this.toastCtrl.create({message:"Error loading evacuation center. Please try again.",duration:3e3,color:"danger"})).present(),this.loadCleanMap()}})}loadSearchLocation(a,c,B){return J(this,null,function*(){console.log(`\u{1F50D} SEARCH LOCATION: Loading [${a}, ${c}] - ${B}...`),yield this.loadingService.showLoading("Loading location...");try{let g=yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),Q=g.coords.latitude,C=g.coords.longitude;this.initializeMap(Q,C);let U=y.marker([a,c],{icon:y.icon({iconUrl:"assets/icons/search-marker.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})});U.bindPopup(`
        <div class="search-popup">
          <h3>\u{1F4CD} ${B}</h3>
          <p>Search result location</p>
        </div>
      `).openPopup(),U.addTo(this.map);let d=y.latLngBounds([[Q,C],[a,c]]);this.map.fitBounds(d,{padding:[50,50]}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:`\u{1F4CD} Showing ${B}`,duration:3e3,color:"primary"})).present()}catch(g){yield this.loadingService.dismissLoading(),console.error("\u{1F50D} SEARCH LOCATION: Error loading location",g),yield(yield this.toastCtrl.create({message:"Error loading search location. Please try again.",duration:3e3,color:"danger"})).present(),this.loadCleanMap()}})}loadMapWithSearchLocation(a,c,B,g=!1){return J(this,null,function*(){yield this.loadingService.showLoading("Loading selected location...");try{if(console.log(`Initializing map with search location: [${a}, ${c}], name: ${B}`),this.isFilterMode=!1,this.currentDisasterType="all",this.initializeMap(a,c),this.map.eachLayer(C=>{C instanceof y.Marker&&C!==this.userMarker&&this.map.removeLayer(C),C instanceof y.GeoJSON&&this.map.removeLayer(C)}),y.marker([a,c],{icon:y.icon({iconUrl:"assets/Typhoons.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup(`<b>${B}</b><br>Selected evacuation center`).openPopup(),this.gpsEnabled)try{let C=yield this.getCurrentPositionWithFallback(),U=C.coords.latitude,d=C.coords.longitude;if(this.updateUserMarker(U,d),g){this.map.eachLayer(_=>{_ instanceof y.GeoJSON&&this.map.removeLayer(_)}),yield this.getRealRoute(U,d,a,c,this.travelMode),this.toastCtrl.create({message:`Showing directions to ${B}`,duration:3e3,color:"success"}).then(_=>_.present());let m=y.latLngBounds([[U,d],[a,c]]);this.map.fitBounds(m,{padding:[50,50]})}else this.toastCtrl.create({message:`Showing ${B} on map`,duration:2e3,color:"primary"}).then(m=>m.present())}catch(C){console.error("Error getting user location for routing:",C),g&&this.toastCtrl.create({message:"Could not get your location to calculate directions. Please check your GPS settings.",duration:3e3,color:"warning"}).then(U=>U.present())}else g&&this.toastCtrl.create({message:"Please enable GPS to get directions",duration:3e3,color:"warning"}).then(C=>C.present());yield this.loadingService.dismissLoading()}catch(Q){console.error("Error loading search location:",Q),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load selected location. Please try again.",duration:3e3,color:"danger"}).then(C=>C.present()),this.loadMapWithUserLocation()}})}loadSearchLocationWithRouting(a,c,B){return J(this,null,function*(){console.log(`\u{1F50D} SEARCH ROUTING: Loading search location with routing [${a}, ${c}] - ${B}`);try{let g=yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),Q=g.coords.latitude,C=g.coords.longitude;console.log(`\u{1F50D} SEARCH ROUTING: User location [${Q}, ${C}]`),console.log(`\u{1F50D} SEARCH ROUTING: Target location [${a}, ${c}] - ${B}`),this.isFilterMode=!1,this.currentDisasterType="all",this.initializeMap(Q,C),this.map.eachLayer(d=>{d instanceof y.Marker&&d!==this.userMarker&&this.map.removeLayer(d),d instanceof y.GeoJSON&&this.map.removeLayer(d)}),this.userMarker=y.marker([Q,C],{icon:y.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!"),y.marker([a,c],{icon:y.icon({iconUrl:"assets/forEarthquake.png",iconSize:[40,40],iconAnchor:[20,40]})}).addTo(this.map).bindPopup(`<b>${B}</b><br>Selected evacuation center`).openPopup(),yield this.showTransportationOptionsForSearch(a,c,B),console.log(`\u{1F50D} SEARCH ROUTING: Successfully loaded search location with routing: ${B}`)}catch(g){console.error("\u{1F50D} SEARCH ROUTING: Error loading search location with routing:",g),(yield this.toastCtrl.create({message:"Error getting your location for routing. Please enable GPS and try again.",duration:3e3,color:"danger"})).present()}})}showTransportationOptionsForSearch(a,c,B){return J(this,null,function*(){yield(yield this.alertCtrl.create({header:`Route to ${B}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToSearchLocation(a,c,B,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToSearchLocation(a,c,B,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToSearchLocation(a,c,B,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToSearchLocation(a,c,B,g){return J(this,null,function*(){if(this.userMarker)try{let Q=this.userMarker.getLatLng().lat,C=this.userMarker.getLatLng().lng,U=this.mapboxRouting.convertTravelModeToProfile(g),d=yield this.mapboxRouting.getDirections(C,Q,c,a,U,{geometries:"geojson",overview:"full",steps:!1});if(d&&d.routes&&d.routes.length>0){let m=d.routes[0],_="#3880ff";this.map.eachLayer(Y=>{Y instanceof y.GeoJSON&&this.map.removeLayer(Y)});let D=y.polyline(m.geometry.coordinates.map(Y=>[Y[1],Y[0]]),{color:_,weight:5,opacity:.8});D.addTo(this.map),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Route: ${(m.distance/1e3).toFixed(2)}km, ${(m.duration/60).toFixed(0)}min via ${g}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(D.getBounds(),{padding:[50,50]})}}catch(Q){console.error("\u{1F50D} Error routing to search location:",Q),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}ngOnDestroy(){console.log("Map page destroyed, cleaning up resources"),this.stopWatchingPosition(),this.map&&this.map.remove()}getCurrentPositionWithFallback(){return J(this,null,function*(){try{console.log("Trying Capacitor Geolocation...");try{let a=yield pA.checkPermissions();if(console.log("Permission status:",a),a.location!=="granted"){console.log("Requesting permissions explicitly...");let c=yield pA.requestPermissions();if(console.log("Permission request result:",c),c.location!=="granted")throw new Error("Location permission denied")}}catch(a){console.log("Permission check failed, might be in browser:",a)}try{return console.log("Getting current position via Capacitor..."),yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:1e4})}catch(a){throw console.log("Capacitor Geolocation failed, trying browser fallback:",a),a}}catch{if(console.log("Trying browser geolocation fallback..."),navigator.geolocation)return new Promise((c,B)=>{navigator.geolocation.getCurrentPosition(g=>{console.log("Browser geolocation succeeded:",g),c({coords:{latitude:g.coords.latitude,longitude:g.coords.longitude,accuracy:g.coords.accuracy,altitude:g.coords.altitude,altitudeAccuracy:g.coords.altitudeAccuracy,heading:g.coords.heading,speed:g.coords.speed},timestamp:g.timestamp})},g=>{if(console.error("Browser geolocation failed:",g),g.code===1&&g.message.includes("secure origins")){let Q=new Error("Geolocation requires HTTPS. Please use a secure connection, run on a real device, or enable insecure origins in Chrome flags.");Q.code=g.code,B(Q)}else B(g)},{enableHighAccuracy:!0,timeout:1e4})});throw console.error("Geolocation not available in this browser"),new Error("Geolocation not available in this browser")}})}loadMapWithDisasterFilter(a,c=!1,B=!1){return J(this,null,function*(){yield this.loadingService.showLoading(`Loading ${a==="all"?"all evacuation centers":a+" evacuation centers"}...`);try{console.log("Getting user location for disaster map...");try{let g=yield this.getCurrentPositionWithFallback();console.log("Position received:",g);let Q=g.coords.latitude,C=g.coords.longitude;console.log(`Initializing disaster map with real GPS coordinates: [${Q}, ${C}]`),this.initializeMap(Q,C),this.startWatchingPosition(),yield this.loadEvacuationCentersFiltered(Q,C,a),c?this.toastCtrl.create({message:`\u{1F6A8} EMERGENCY: Showing nearest ${a} evacuation centers with routes`,duration:5e3,color:"danger",position:"top"}).then(U=>U.present()):this.toastCtrl.create({message:`Showing ${a==="all"?"all evacuation centers":a+" evacuation centers"} near you`,duration:3e3,color:"primary"}).then(U=>U.present()),yield this.loadingService.dismissLoading();return}catch(g){console.error("Failed to get GPS position for disaster map:",g),yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"GPS Required",message:"We need your location to show nearby evacuation centers. Please enable GPS and try again.",buttons:[{text:"Enable GPS",handler:()=>{this.loadMapWithDisasterFilter(a)}},{text:"Cancel",role:"cancel"}]})).present()}}catch(g){console.error("Error loading disaster map:",g),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load evacuation centers. Please try again.",duration:3e3,color:"danger"}).then(Q=>Q.present())}})}loadMapWithOnlyUserLocation(){return J(this,null,function*(){yield this.loadingService.showLoading("Loading map...");try{console.log("Getting user location for map tab...");try{let a=yield this.getCurrentPositionWithFallback();console.log("Position received:",a);let c=a.coords.latitude,B=a.coords.longitude;console.log(`Initializing map with only user location: [${c}, ${B}]`),this.isFilterMode=!1,this.currentDisasterType="all",this.evacuationCenters=[],this.initializeMap(c,B),this.map.eachLayer(g=>{g instanceof y.Marker&&g!==this.userMarker&&this.map.removeLayer(g),g instanceof y.GeoJSON&&this.map.removeLayer(g)}),this.startWatchingPosition(),this.userMarker&&this.userMarker.bindPopup("You are here!").openPopup(),this.toastCtrl.create({message:"Showing your current location",duration:2e3,color:"success"}).then(g=>g.present()),yield this.loadingService.dismissLoading();return}catch(a){console.error("Failed to get GPS position for map tab:",a),yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"Location Required",message:"We need your location to show the map. Please enable GPS and try again.",buttons:[{text:"Enable GPS",handler:()=>{this.loadMapWithOnlyUserLocation()}},{text:"Cancel",role:"cancel"}]})).present();return}}catch(a){console.error("Error loading map:",a),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load map. Please try again.",duration:3e3,color:"danger"}).then(c=>c.present())}})}loadMapWithUserLocation(){return J(this,null,function*(){yield this.loadingService.showLoading("Loading map...");try{console.log("Getting user location...");try{let a=yield this.getCurrentPositionWithFallback();console.log("Position received:",a);let c=a.coords.latitude,B=a.coords.longitude;console.log(`Initializing map with real GPS coordinates: [${c}, ${B}]`),this.isFilterMode||(this.currentDisasterType="all"),this.initializeMap(c,B),this.startWatchingPosition(),this.toastCtrl.create({message:"Using your real-time location",duration:2e3,color:"success"}).then(g=>g.present()),yield this.loadingService.dismissLoading();return}catch(a){throw console.error("Failed to get GPS position, showing alert:",a),a}}catch(a){console.error("Error getting location",a);let c="Unable to access your location. ";a.code===1?navigator.userAgent.includes("Chrome")?c+='Location permission denied. Please click the lock icon in the address bar, select "Site settings", and change Location permission to "Allow".':navigator.userAgent.includes("Firefox")?c+='Location permission denied. Please click the lock icon in the address bar, select "Site Permissions", and change "Access Your Location" to "Allow".':navigator.userAgent.includes("Safari")?c+='Location permission denied. Please check Safari settings > Websites > Location and ensure this website is set to "Allow".':c+="Location permission denied. Please enable location access for this website in your browser settings.":a.code===2?c+="Position unavailable. Your GPS signal might be weak or unavailable.":a.code===3?c+="Location request timed out. Please try again.":c+="Please enable GPS or try again. "+(a.message||""),yield(yield this.alertCtrl.create({header:"Location Error",message:c,buttons:[{text:"Retry",handler:()=>{this.loadMapWithUserLocation()}},{text:"Load Default Map",role:"cancel",handler:()=>{this.initializeMap(10.3157,123.8854)}}]})).present()}yield this.loadingService.dismissLoading()})}stopWatchingPosition(){if(this.watchId){if(console.log("Stopping position watch..."),typeof this.watchId=="string")try{let a=this.watchId;pA.clearWatch({id:a})}catch(a){console.log("Error clearing Capacitor watch:",a)}else if(typeof this.watchId=="number")try{navigator.geolocation.clearWatch(this.watchId)}catch(a){console.log("Error clearing browser watch:",a)}this.watchId=null}}startWatchingPosition(){this.stopWatchingPosition(),console.log("Starting position watch...");try{this.watchId=pA.watchPosition({enableHighAccuracy:!0,timeout:1e4},(a,c)=>{a&&this.gpsEnabled&&(console.log("Capacitor watch position update:",a),this.updateUserMarker(a.coords.latitude,a.coords.longitude)),c&&(console.error("Error watching position:",c),this.toastCtrl.create({message:"GPS signal lost or weak. Please check your location settings.",duration:3e3,color:"warning"}).then(B=>B.present()))}),console.log("Capacitor watch started with ID:",this.watchId)}catch(a){console.log("Capacitor watch failed, trying browser fallback:",a),navigator.geolocation?(this.watchId=navigator.geolocation.watchPosition(c=>{this.gpsEnabled&&(console.log("Browser watch position update:",c),this.updateUserMarker(c.coords.latitude,c.coords.longitude))},c=>{console.error("Browser watch error:",c),this.toastCtrl.create({message:"GPS signal lost or weak. Please check your location settings.",duration:3e3,color:"warning"}).then(B=>B.present())},{enableHighAccuracy:!0,timeout:1e4}),console.log("Browser watch started with ID:",this.watchId)):console.error("Geolocation watching not available")}}initializeMap(a,c){console.log(`Initializing map with coordinates: [${a}, ${c}]`),(isNaN(a)||isNaN(c)||Math.abs(a)>90||Math.abs(c)>180)&&(console.error("Invalid coordinates for map initialization:",{lat:a,lng:c}),a=12.8797,c=121.774,console.log(`Using fallback coordinates for Philippines: [${a}, ${c}]`)),this.map&&(console.log("Removing existing map"),this.map.remove()),this.map=y.map("map").setView([a,c],15),console.log("Map initialized"),this.offlineStorage.isOfflineMode()||!navigator.onLine?(console.log("\u{1F504} Loading offline map tiles..."),this.offlineMapService.createOfflineTileLayer().addTo(this.map)):(console.log("\u{1F310} Loading online map tiles..."),y.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors",maxZoom:19,minZoom:8}).addTo(this.map)),this.isOnline=!0,this.gpsEnabled?(console.log("GPS is enabled, adding user marker"),this.userMarker?(this.userMarker.setLatLng([a,c]),this.userMarker.addTo(this.map),console.log("Updated existing user marker")):(this.userMarker=y.marker([a,c],{icon:y.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup("You are here!").openPopup(),console.log("Created new user marker")),this.toastCtrl.create({message:"Using your real-time GPS location",duration:2e3,color:"success"}).then(B=>B.present())):console.log("GPS is disabled, not adding user marker"),this.isFilterMode||this.evacuationCenters.length>0?(console.log("Loading evacuation centers"),this.loadEvacuationCenters(a,c)):console.log("Skipping evacuation centers - showing only user location")}updateUserMarker(a,c){if(console.log(`Updating user marker to: [${a}, ${c}]`),isNaN(a)||isNaN(c)||Math.abs(a)>90||Math.abs(c)>180){console.error("Invalid coordinates for user marker update:",{lat:a,lng:c});return}if(this.userMarker){let B=this.userMarker.getLatLng();this.userMarker.setLatLng([a,c]),this.map.setView([a,c]),console.log("Updated existing user marker position");let g=this.calculateDistance(B.lat,B.lng,a,c);console.log(`User moved ${g.toFixed(2)} meters from previous position`),g>20&&(console.log(`Significant movement detected (${g.toFixed(2)}m), recalculating routes`),this.map.eachLayer(Q=>{Q instanceof y.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(Q))}),this.evacuationCenters&&this.evacuationCenters.length>0&&(console.log("Recalculating routes to nearest evacuation centers"),this.routeToTwoNearestCenters()))}else this.userMarker=y.marker([a,c],{icon:y.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup("You are here!").openPopup(),console.log("Created new user marker"),this.evacuationCenters&&this.evacuationCenters.length>0&&(console.log("Calculating initial routes with real GPS data"),this.routeToTwoNearestCenters())}loadEvacuationCentersFiltered(a,c,B){return J(this,null,function*(){if(this.isLoadingCenters){console.log("Already loading evacuation centers, skipping duplicate request");return}this.isLoadingCenters=!0;try{if(console.log(`Loading evacuation centers for disaster type: ${B}`),console.log(`User coordinates: [${a}, ${c}]`),isNaN(a)||isNaN(c)||Math.abs(a)>90||Math.abs(c)>180){console.error("Invalid user coordinates for loading evacuation centers:",{userLat:a,userLng:c}),(yield this.toastCtrl.create({message:"Invalid location coordinates. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}this.currentDisasterType=B;let g=[];if(this.offlineStorage.isOfflineMode()||!this.offlineStorage.isOnline())console.log("\u{1F504} Loading evacuation centers from offline storage"),g=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} OFFLINE DATA:",g),console.log("\u{1F4CA} TOTAL CACHED CENTERS:",(g==null?void 0:g.length)||0),g.length===0&&(console.warn("\u26A0\uFE0F No cached evacuation centers found"),this.toastCtrl.create({message:"No offline evacuation data available. Please sync data when online.",duration:4e3,color:"warning"}).then(d=>d.present()));else{console.log("\u{1F310} Fetching evacuation centers from:",`${te.apiUrl}/evacuation-centers`);try{g=yield ze(this.http.get(`${te.apiUrl}/evacuation-centers`)),console.log("\u{1F4E1} RAW API RESPONSE:",g),console.log("\u{1F4CA} TOTAL CENTERS RECEIVED:",(g==null?void 0:g.length)||0)}catch(d){console.error("\u274C Failed to fetch online data, trying offline cache:",d),g=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} FALLBACK TO OFFLINE DATA:",g),g.length>0&&this.toastCtrl.create({message:"Using cached evacuation data due to network error.",duration:3e3,color:"warning"}).then(m=>m.present())}}if(g&&g.length>0){let d=[...new Set(g.map(m=>m.disaster_type))];console.log("\u{1F3F7}\uFE0F UNIQUE DISASTER TYPES IN DATABASE:",d),d.forEach(m=>{let _=g.filter(D=>D.disaster_type===m).length;console.log(`   \u{1F4C8} ${m}: ${_} centers`)}),console.log("\u{1F50D} SAMPLE CENTERS:"),g.slice(0,5).forEach((m,_)=>{console.log(`   ${_+1}. "${m.name}" - Type: "${m.disaster_type}" - Status: "${m.status}"`)})}this.map.eachLayer(d=>{d instanceof y.Marker&&d!==this.userMarker&&this.map.removeLayer(d)}),this.map.eachLayer(d=>{d instanceof y.GeoJSON&&this.map.removeLayer(d)});let Q=g||[];if(B!=="all"){console.log(`\u{1F50D} FILTERING centers for disaster type: "${B}"`),console.log(`\u{1F4CA} Total centers before filtering: ${Q.length}`),console.log("\u{1F4CB} All centers disaster types:",g.map(m=>`${m.name}: "${m.disaster_type}"`)),Q=Q.filter(m=>{if(!m.disaster_type)return console.log(`\u274C Center "${m.name}" has no disaster_type, excluding`),!1;let _=m.disaster_type.trim(),D=B.trim(),z=_===D;return console.log(`\u{1F3E2} Center "${m.name}"`),console.log(`   \u{1F4CD} Center Type: "${_}" (length: ${_.length})`),console.log(`   \u{1F3AF} Looking for: "${D}" (length: ${D.length})`),console.log(`   \u2705 Match: ${z}`),z}),console.log(`\u{1F3AF} FILTERED RESULT: ${Q.length} centers for disaster type: "${B}"`),console.log("\u2705 INCLUDED CENTERS:"),Q.forEach((m,_)=>{console.log(`   ${_+1}. ${m.name} (${m.disaster_type})`)});let d=g.filter(m=>m.disaster_type&&m.disaster_type.trim()!==B.trim());console.log("\u274C EXCLUDED CENTERS:"),d.forEach((m,_)=>{console.log(`   ${_+1}. ${m.name} (${m.disaster_type})`)}),Q.length===0&&(console.error("\u{1F6A8} NO CENTERS FOUND FOR DISASTER TYPE!"),console.error("\u{1F50D} Debug Info:"),console.error(`   Target disaster type: "${B}"`),console.error("   Available disaster types:",[...new Set(g.map(m=>m.disaster_type))]),console.error(`   Total centers in database: ${g.length}`))}console.log("\u{1F9F9} AGGRESSIVE CLEARING: Removing ALL existing markers");let C=[];if(this.map.eachLayer(d=>{d instanceof y.Marker&&d!==this.userMarker&&(console.log("\u{1F5D1}\uFE0F Marking marker for removal:",d),C.push(d)),d instanceof y.GeoJSON&&(console.log("\u{1F5D1}\uFE0F Marking route for removal:",d),C.push(d))}),C.forEach(d=>{console.log("\u{1F5D1}\uFE0F Removing layer from map"),this.map.removeLayer(d)}),this.evacuationCenters=[],console.log(`\u{1F9F9} CLEARED: Removed ${C.length} layers from map`),this.evacuationCenters=Q,this.evacuationCenters.length===0){console.log(`\u{1F6A8} NO EVACUATION CENTERS FOUND for disaster type: "${B}"`),this.alertCtrl.create({header:"No Evacuation Centers Found",message:`There are no evacuation centers stored for ${B==="all"?"any disaster type":B}. Please contact your administrator to add evacuation centers.`,buttons:["OK"]}).then(d=>d.present()),this.userMarker&&this.userMarker.bindPopup("You are here!").openPopup(),this.map.setView([a,c],15);return}console.log(`\u{1F3AF} ADDING ${this.evacuationCenters.length} FILTERED MARKERS to map`),console.log(`\u{1F4CD} Disaster type filter: "${B}"`),console.log("\u{1F3E2} Centers to display:",this.evacuationCenters.map(d=>`${d.name} (${d.disaster_type})`)),this.evacuationCenters.forEach((d,m)=>{let _=Number(d.latitude),D=Number(d.longitude);if(console.log(`\u{1F3E2} Processing center ${m+1}/${this.evacuationCenters.length}: ${d.name}`),console.log(`   \u{1F4CD} Coordinates: [${_}, ${D}]`),console.log(`   \u{1F3F7}\uFE0F Disaster Type: "${d.disaster_type}"`),console.log(`   \u{1F3AF} Filter Type: "${B}"`),!isNaN(_)&&!isNaN(D)){let z=this.getDisasterIcon(d.disaster_type||"");console.log(`   \u{1F3A8} Icon URL: ${z}`);let Y=y.marker([_,D],{icon:y.icon({iconUrl:z,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),TA=`
            <div class="evacuation-popup">
              <h3>${d.name||"Evacuation Center"}</h3>
              <p><strong>Type:</strong> ${d.disaster_type||"General"}</p>
              <p><strong>Distance:</strong> ${(this.calculateDistance(a,c,_,D)/1e3).toFixed(2)} km</p>
              <p><button class="popup-button">View Details</button></p>
            </div>
          `;Y.bindPopup(TA),Y.on("click",()=>{setTimeout(()=>{Y.closePopup(),this.showEvacuationCenterDetails(d,a,c)},300)}),Y.addTo(this.map),console.log(`   \u2705 MARKER ADDED to map for: ${d.name} (${d.disaster_type})`)}else console.error(`   \u274C Invalid coordinates for center: ${d.name}`)}),console.log(`\u{1F389} COMPLETED: Added ${this.evacuationCenters.length} markers for disaster type "${B}"`);let U=0;if(this.map.eachLayer(d=>{d instanceof y.Marker&&d!==this.userMarker&&U++}),console.log(`\u{1F50D} VERIFICATION: ${U} evacuation center markers currently on map`),this.gpsEnabled&&this.userMarker&&this.evacuationCenters.length>0){console.log("GPS enabled and user marker exists, finding nearest centers");let d=this.findTwoNearestCenters(a,c,this.evacuationCenters);if(d.length>0){this.addPulsingAnimationToNearest(d[0]);for(let D of d){let z=Number(D.latitude),Y=Number(D.longitude);if(console.log(`Calculating route to center: ${D.name}`),console.log(`Center coordinates: [${z}, ${Y}], types: [${typeof z}, ${typeof Y}]`),isNaN(z)||isNaN(Y)){console.error("Invalid center coordinates:",{centerLat:z,centerLng:Y,center:D});continue}yield this.getRealRoute(a,c,z,Y,this.travelMode,D.disaster_type)}let m="You are here!.";d.forEach((D,z)=>{let Y=this.calculateDistance(a,c,Number(D.latitude),Number(D.longitude));m+=`<br> \u2022 <strong>${z+1}: ${D.name} </strong> <br> Distance: ${(Y/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(m).openPopup();let _=y.latLngBounds([]);_.extend([a,c]),d.forEach(D=>{_.extend([Number(D.latitude),Number(D.longitude)])}),this.map.fitBounds(_,{padding:[50,50]})}else console.log("No nearest centers found"),this.map.setView([a,c],15)}else console.log("GPS disabled, no user marker, or no centers found, skipping route calculation"),this.map.setView([a,c],15)}catch(g){console.error("Error loading filtered evacuation centers:",g),console.log("Network error loading evacuation centers - offline mode available")}finally{this.isLoadingCenters=!1}})}loadEvacuationCenters(a,c){return J(this,null,function*(){if(this.isLoadingCenters){console.log("Already loading evacuation centers, skipping duplicate request");return}this.isLoadingCenters=!0;try{if(console.log(`Loading evacuation centers with user coordinates: [${a}, ${c}]`),isNaN(a)||isNaN(c)||Math.abs(a)>90||Math.abs(c)>180){console.error("Invalid user coordinates for loading evacuation centers:",{userLat:a,userLng:c}),(yield this.toastCtrl.create({message:"Invalid location coordinates. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}let B=[];if(this.offlineStorage.isOfflineMode()||!this.offlineStorage.isOnline())console.log("\u{1F504} Loading evacuation centers from offline storage"),B=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} OFFLINE DATA:",B),B.length===0&&(console.warn("\u26A0\uFE0F No cached evacuation centers found"),this.toastCtrl.create({message:"No offline evacuation data available. Please sync data when online.",duration:4e3,color:"warning"}).then(g=>g.present()));else{console.log("\u{1F310} Fetching evacuation centers from:",`${te.apiUrl}/evacuation-centers`);try{B=yield ze(this.http.get(`${te.apiUrl}/evacuation-centers`)),console.log("\u{1F4E1} Received centers from API:",B)}catch(g){console.error("\u274C Failed to fetch online data, trying offline cache:",g),B=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} FALLBACK TO OFFLINE DATA:",B),B.length>0&&this.toastCtrl.create({message:"Using cached evacuation data due to network error.",duration:3e3,color:"warning"}).then(Q=>Q.present())}}if(this.evacuationCenters=B||[],this.map.eachLayer(g=>{g instanceof y.Marker&&g!==this.userMarker&&this.map.removeLayer(g)}),this.evacuationCenters.forEach(g=>{let Q=Number(g.latitude),C=Number(g.longitude);if(console.log(`Processing center: ${g.name}, coordinates: [${Q}, ${C}]`),!isNaN(Q)&&!isNaN(C)){let U=this.getDisasterIcon(g.disaster_type||""),d=y.marker([Q,C],{icon:y.icon({iconUrl:U,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),m=`
            <div class="evacuation-popup">
              <h3>${g.name||"Evacuation Center"}</h3>
              <p><strong>Distance:</strong> ${(this.calculateDistance(a,c,Q,C)/1e3).toFixed(2)} km</p>
              <p><button class="popup-button">View Details</button></p>
            </div>
          `;d.bindPopup(m),d.on("click",()=>{setTimeout(()=>{d.closePopup(),this.showEvacuationCenterDetails(g,a,c)},300)}),d.addTo(this.map),console.log(`Added marker for center: ${g.name}`)}else console.error(`Invalid coordinates for center: ${g.name}`)}),this.gpsEnabled&&this.userMarker){console.log("GPS enabled and user marker exists, finding nearest centers");let g=this.findTwoNearestCenters(a,c,this.evacuationCenters);if(g.length>0){this.addPulsingAnimationToNearest(g[0]),this.map.eachLayer(C=>{C instanceof y.GeoJSON&&this.map.removeLayer(C)});for(let C of g){let U=Number(C.latitude),d=Number(C.longitude);if(console.log(`Calculating route to center: ${C.name}`),console.log(`Center coordinates: [${U}, ${d}], types: [${typeof U}, ${typeof d}]`),isNaN(U)||isNaN(d)){console.error("Invalid center coordinates:",{centerLat:U,centerLng:d,center:C});continue}yield this.getRealRoute(a,c,U,d,this.travelMode,C.disaster_type)}let Q="You are here!.";g.forEach((C,U)=>{let d=this.calculateDistance(a,c,Number(C.latitude),Number(C.longitude));Q+=`<br> \u2022${U+1}: ${C.name} <br> Distance: ${(d/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(Q).openPopup()}else console.log("No nearest centers found"),this.map.setView([a,c],15)}else console.log("GPS disabled or no user marker, skipping route calculation"),this.map.setView([a,c],15)}catch(B){console.error("Failed to load evacuation centers",B),console.log("Network error loading evacuation centers - offline mode available")}finally{this.isLoadingCenters=!1}})}getRealRoute(U,d,m,_){return J(this,arguments,function*(a,c,B,g,Q=this.travelMode,C){if(console.log("Clearing all existing routes before calculating new route"),this.map.eachLayer(D=>{D instanceof y.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(D))}),console.log("Requesting Mapbox route with coordinates:",{startLat:a,startLng:c,endLat:B,endLng:g,travelMode:Q}),[a,c,B,g].some(D=>typeof D!="number"||isNaN(D))){(yield this.toastCtrl.create({message:"Invalid route coordinates. Cannot request directions.",duration:3e3,color:"danger"})).present();return}if(Math.abs(a)>90||Math.abs(B)>90||Math.abs(c)>180||Math.abs(g)>180){(yield this.toastCtrl.create({message:"Route coordinates out of range. Cannot request directions.",duration:3e3,color:"danger"})).present();return}try{console.log("Sending route request to Mapbox");let D=this.mapboxRouting.convertTravelModeToProfile(Q),z=yield this.mapboxRouting.getDirections(c,a,g,B,D,{geometries:"geojson",overview:"full",steps:!0});if(!z.routes||z.routes.length===0)throw new Error("No routes found");let Y=z.routes[0],TA=this.mapboxRouting.convertToGeoJSON(Y),mA="#3388ff";if(C)switch(C){case"Earthquake":mA="#ffa500";break;case"Flood":mA="#0000ff";break;case"Typhoon":mA="#008000";break;default:console.warn(`Unknown disaster type for route color: ${C}`);break}console.log(`Route calculation - Disaster type: "${C}", Normalized type: "${C?C.toLowerCase():"none"}", Selected color: ${mA}`),this.currentDisasterType&&this.currentDisasterType!=="all"&&(this.currentDisasterType==="Earthquake"?mA="#ffa500":this.currentDisasterType==="Typhoon"?mA="#008000":this.currentDisasterType==="Flood"&&(mA="#0000ff"),console.log(`Filter mode active: ${this.currentDisasterType}, forcing route color to: ${mA}`)),console.log(`Using route color: ${mA} for disaster type: ${C||"unknown"}`);let zt=y.geoJSON(TA,{style:{color:mA,weight:5,opacity:.8}}).addTo(this.map);this.routeTime=Y.duration,this.routeDistance=Y.distance;let et=this.mapboxRouting.getRouteSummary(Y);console.log(`Mapbox route summary: ${et.durationText}, ${et.distanceText}`),this.map.fitBounds(zt.getBounds(),{padding:[50,50]})}catch(D){console.error("Failed to fetch route from Mapbox",D);let z="Failed to fetch route. Please check your internet connection or try again later.";D.message?D.message.includes("Invalid Mapbox access token")?z="Invalid Mapbox access token. Please check your token configuration.":D.message.includes("Rate limit exceeded")?z="Too many requests to Mapbox. Please wait a moment and try again.":D.message.includes("Network error")?z="Network error. Please check your internet connection.":D.message.includes("No routes found")?z="No route could be calculated between these points.":z=`Mapbox routing error: ${D.message}`:D.status===401?z="Invalid Mapbox access token. Please check your token.":D.status===422?z="Invalid coordinates or routing parameters.":D.status===429?z="Rate limit exceeded. Please try again later.":D.status===0&&(z="Network error. Please check your internet connection.");let Y=Q==="foot-walking"?"walking":Q==="cycling-regular"?"cycling":Q==="driving-car"?"driving":Q;this.hasRecentErrorToast()||((yield this.toastCtrl.create({message:`Failed to fetch ${Y} route: ${z}`,duration:5e3,color:"danger"})).present(),this.setLastErrorToast())}})}findNearestCenter(a,c,B){if(!B.length)return null;let g=B[0],Q=this.calculateDistance(a,c,Number(g.latitude),Number(g.longitude));for(let C of B){let U=this.calculateDistance(a,c,Number(C.latitude),Number(C.longitude));U<Q&&(Q=U,g=C)}return g}calculateDistance(a,c,B,g){let C=a*Math.PI/180,U=B*Math.PI/180,d=(B-a)*Math.PI/180,m=(g-c)*Math.PI/180,_=Math.sin(d/2)*Math.sin(d/2)+Math.cos(C)*Math.cos(U)*Math.sin(m/2)*Math.sin(m/2);return 6371e3*(2*Math.atan2(Math.sqrt(_),Math.sqrt(1-_)))}downloadMap(){return J(this,null,function*(){try{yield this.loadingService.showLoading("Capturing map...");let a=document.getElementById("map");if(!a)throw new Error("Map element not found");console.log("Capturing map as image...");let c=yield(0,xi.default)(a,{useCORS:!0,allowTaint:!0,scrollX:0,scrollY:0,windowWidth:document.documentElement.offsetWidth,windowHeight:document.documentElement.offsetHeight,scale:1});yield this.loadingService.dismissLoading();let B=c.toDataURL("image/png"),C=`evacuation-map-${new Date().toISOString().replace(/[:.]/g,"-").substring(0,19)}.png`;yield(yield this.alertCtrl.create({header:"Map Captured",message:"Your map has been captured. What would you like to do with it?",buttons:[{text:"Download",handler:()=>{this.downloadImage(B,C)}},{text:"Share",handler:()=>{this.shareImage(B,C)}},{text:"Cancel",role:"cancel"}]})).present()}catch(a){console.error("Error capturing map:",a),yield this.loadingService.dismissLoading(),(yield this.toastCtrl.create({message:"Failed to capture map. Please try again.",duration:3e3,color:"danger"})).present()}})}downloadImage(a,c){let B=document.createElement("a");B.href=a,B.download=c,document.body.appendChild(B),B.click(),document.body.removeChild(B),this.toastCtrl.create({message:"Map downloaded successfully",duration:2e3,color:"success"}).then(g=>g.present())}shareImage(a,c){return J(this,null,function*(){try{if(navigator.share){let B=yield(yield fetch(a)).blob(),g=new File([B],c,{type:"image/png"});yield navigator.share({title:"Evacuation Map",text:"Here is my evacuation map with routes to the nearest evacuation centers",files:[g]}),console.log("Map shared successfully")}else console.log("Web Share API not supported"),(yield this.toastCtrl.create({message:"Sharing not supported on this device. The map has been downloaded instead.",duration:3e3,color:"warning"})).present(),this.downloadImage(a,c)}catch(B){console.error("Error sharing map:",B),(yield this.toastCtrl.create({message:"Failed to share map. The map has been downloaded instead.",duration:3e3,color:"warning"})).present(),this.downloadImage(a,c)}})}showEvacuationCenterDetails(a,c,B){return J(this,null,function*(){console.log("Showing evacuation center details for:",a.name);let g=yield this.modalCtrl.create({component:yi,componentProps:{center:a,userLat:c,userLng:B},cssClass:"evacuation-details-modal",breakpoints:[0,.5,.75,1],initialBreakpoint:.75});yield g.present();let{data:Q}=yield g.onDidDismiss();if(Q&&Q.selectedMode&&(console.log("Selected travel mode:",Q.selectedMode),this.travelMode=Q.selectedMode,console.log("Clearing all existing routes before calculating new route"),this.map.eachLayer(C=>{C instanceof y.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(C))}),this.userMarker)){let C=this.userMarker.getLatLng(),U=Number(a.latitude),d=Number(a.longitude);if(console.log("Recalculating route with new travel mode:",{userLat:C.lat,userLng:C.lng,centerLat:U,centerLng:d,travelMode:this.travelMode}),isNaN(U)||isNaN(d)){console.error("Invalid center coordinates:",{centerLat:U,centerLng:d}),(yield this.toastCtrl.create({message:"Invalid evacuation center coordinates. Cannot calculate route.",duration:3e3,color:"danger"})).present();return}this.toastCtrl.create({message:`Showing ${this.getTravelModeName().toLowerCase()} route to ${a.name}`,duration:2e3,color:"primary"}).then(m=>m.present()),yield this.getRealRoute(C.lat,C.lng,U,d,this.travelMode,a.disaster_type)}})}};S.\u0275fac=function(c){return new(c||S)},S.\u0275cmp=ve({type:S,selectors:[["app-map"]],decls:38,vars:15,consts:[["class","offline-status-banner",4,"ngIf"],["id","map"],["class","location-request-container",4,"ngIf"],["class","map-default-message",4,"ngIf"],["class","route-summary-card",3,"click",4,"ngIf"],[3,"directions","travelMode","totalDistance","totalDuration","close",4,"ngIf"],["vertical","top","horizontal","end","slot","fixed"],["size","small",3,"click","color"],[3,"name"],["vertical","top","horizontal","end","slot","fixed",2,"top","60px"],["name","cloud-offline"],["vertical","top","horizontal","end","slot","fixed",2,"top","120px"],["size","small","color","danger",3,"click"],["name","bug"],["vertical","top","horizontal","start","slot","fixed"],["size","small","color","success",3,"click"],["name","download-outline"],[1,"fab-label"],[1,"gps-status",3,"click"],["class","disaster-type-indicator",4,"ngIf"],["vertical","bottom","horizontal","end","slot","fixed",4,"ngIf"],["vertical","bottom","horizontal","start","slot","fixed",4,"ngIf"],["vertical","bottom","horizontal","center","slot","fixed"],["color","secondary"],["name","cloud-offline-outline"],["side","top"],["color","warning",3,"click"],["color","success",3,"click"],["name","share-outline"],[1,"offline-status-banner"],[1,"location-request-container"],["expand","block","color","primary",3,"click"],["name","locate","slot","start"],[1,"location-help-text"],[1,"map-default-message"],["name","information-circle-outline"],[1,"route-summary-card",3,"click"],[3,"name","color"],[1,"summary-text"],[1,"travel-mode"],["name","chevron-up",1,"expand-icon"],[3,"close","directions","travelMode","totalDistance","totalDuration"],[1,"disaster-type-indicator"],["vertical","bottom","horizontal","end","slot","fixed"],["color","primary",3,"click"],["name","navigate-outline"],["vertical","bottom","horizontal","start","slot","fixed"],["color","tertiary",3,"click"],["name","list-outline"]],template:function(c,B){c&1&&(H(0,"ion-content"),wA(1,ug,4,0,"div",0),V(2,"div",1),wA(3,fg,6,0,"div",2)(4,wg,6,0,"div",3)(5,dg,9,5,"div",4)(6,hg,1,4,"app-directions-panel",5),H(7,"ion-fab",6)(8,"ion-fab-button",7),sA("click",function(){return B.toggleGps({detail:{checked:!B.gpsEnabled}})}),V(9,"ion-icon",8),T()(),H(10,"ion-fab",9)(11,"ion-fab-button",7),sA("click",function(){return B.toggleOfflineMode()}),V(12,"ion-icon",10),T()(),H(13,"ion-fab",11)(14,"ion-fab-button",12),sA("click",function(){return B.debugLoadOfflineData()}),V(15,"ion-icon",13),T()(),H(16,"ion-fab",14)(17,"ion-fab-button",15),sA("click",function(){return B.downloadMap()}),V(18,"ion-icon",16),T(),H(19,"ion-label",17),X(20,"Save Map"),T()(),H(21,"div",18),sA("click",function(){return B.showLocationHelp()}),V(22,"ion-icon",8),H(23,"span"),X(24),T()(),wA(25,Cg,4,2,"div",19)(26,Qg,5,0,"ion-fab",20)(27,pg,5,0,"ion-fab",21),H(28,"ion-fab",22)(29,"ion-fab-button",23),V(30,"ion-icon",24),T(),H(31,"ion-fab-list",25)(32,"ion-fab-button",26),sA("click",function(){return B.exportOfflineData()}),V(33,"ion-icon",16),T(),H(34,"ion-fab-button",27),sA("click",function(){return B.shareOfflineData()}),V(35,"ion-icon",28),T()(),H(36,"ion-label",17),X(37,"Offline Data"),T()()()),c&2&&(O(),k("ngIf",B.offlineStorage.isOfflineMode()||!B.offlineStorage.isOnline()),O(2),k("ngIf",B.showLocationRequestButton),O(),k("ngIf",!B.isFilterMode&&B.evacuationCenters.length===0),O(),k("ngIf",B.routeTime&&B.routeDistance),O(),k("ngIf",B.showDirectionsPanel&&B.currentDirections.length>0),O(2),k("color",B.gpsEnabled?"primary":"medium"),O(),k("name",B.gpsEnabled?"locate":"locate-outline"),O(2),k("color",B.offlineStorage.isOfflineMode()?"warning":"medium"),O(10),ei("active",B.gpsEnabled),O(),k("name",B.gpsEnabled?"location":"location-outline"),O(2),bA("GPS ",B.gpsEnabled?"Active":"Inactive",""),O(),k("ngIf",B.isFilterMode&&B.currentDisasterType!=="all"),O(),k("ngIf",B.isFilterMode||B.evacuationCenters.length>0),O(),k("ngIf",B.currentDirections.length>0&&!B.showDirectionsPanel))},dependencies:[Le,Ie,Wt,Bi,gi,ui,ye,be,He,Ee,ni,bi],styles:['.offline-status-banner[_ngcontent-%COMP%]{position:absolute;top:10px;left:50%;transform:translate(-50%);background:linear-gradient(135deg,#ff6b35,#f7931e);color:#fff;border-radius:20px;padding:8px 16px;display:flex;align-items:center;gap:8px;z-index:1001;box-shadow:0 2px 8px #0003;font-size:14px;font-weight:500;animation:_ngcontent-%COMP%_slideDown .3s ease-out}.offline-status-banner[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}@keyframes _ngcontent-%COMP%_slideDown{0%{opacity:0;transform:translate(-50%) translateY(-20px)}to{opacity:1;transform:translate(-50%) translateY(0)}}#map[_ngcontent-%COMP%]{width:100%;height:100%}.mode-segment[_ngcontent-%COMP%]{position:absolute;left:50%;transform:translate(-50%);top:10px;z-index:1000;background:#ffffffe6;border-radius:20px;padding:4px;width:90%;max-width:400px;box-shadow:0 2px 8px #0000001a}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--background: transparent;--background-checked: var(--ion-color-light);--indicator-color: transparent;--border-radius: 16px;min-height:40px}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   .segment-icon[_ngcontent-%COMP%]{width:24px;height:24px;display:block;margin:0 auto 4px}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   .segment-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-summary-card[_ngcontent-%COMP%]{position:absolute;left:50%;transform:translate(-50%);top:70px;background:#fffffff2;border-radius:16px;box-shadow:0 2px 8px #0000001a;padding:12px 16px;display:flex;align-items:center;gap:12px;z-index:1000;cursor:pointer;transition:all .2s ease}.route-summary-card[_ngcontent-%COMP%]:hover{background:#fff;box-shadow:0 4px 12px #00000026;transform:translate(-50%) translateY(-2px)}.route-summary-card[_ngcontent-%COMP%]:active{transform:translate(-50%) translateY(0)}.route-summary-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]{line-height:1.3}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-size:16px}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   .travel-mode[_ngcontent-%COMP%]{font-size:12px;opacity:.8;margin-top:2px}.route-summary-card[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%]{font-size:18px;margin-left:8px;color:var(--ion-color-medium)}.fab-label[_ngcontent-%COMP%]{position:absolute;right:80px;bottom:30px;background:#fffffff2;padding:8px 16px;border-radius:20px;font-size:14px;color:var(--ion-color-primary);z-index:1000;box-shadow:0 2px 8px #0000001a;font-weight:500}ion-fab-button[activated][_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}.gps-status[_ngcontent-%COMP%]{position:absolute;top:10px;left:70px;background:#ffffffe6;border-radius:20px;padding:8px 12px;display:flex;align-items:center;gap:6px;z-index:1000;box-shadow:0 2px 8px #0000001a;font-size:14px;color:var(--ion-color-medium);cursor:pointer;transition:all .2s ease}.gps-status[_ngcontent-%COMP%]:hover{background:#fff;box-shadow:0 4px 12px #00000026;transform:translateY(-2px)}.gps-status[_ngcontent-%COMP%]:active{transform:translateY(0)}.gps-status.active[_ngcontent-%COMP%]{color:var(--ion-color-primary);background:rgba(var(--ion-color-primary-rgb),.1)}.gps-status.active[_ngcontent-%COMP%]:hover{background:rgba(var(--ion-color-primary-rgb),.2)}.gps-status.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s infinite}.gps-status[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.gps-status[_ngcontent-%COMP%]:after{content:"?";display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background:var(--ion-color-medium);color:#fff;border-radius:50%;font-size:12px;margin-left:6px;opacity:.7}.disaster-type-indicator[_ngcontent-%COMP%]{position:absolute;top:10px;right:80px;background:#ffffffe6;border-radius:20px;padding:8px 12px;display:flex;align-items:center;gap:6px;z-index:1000;box-shadow:0 2px 8px #0000001a;font-size:14px;font-weight:500;color:var(--ion-color-dark)}.disaster-type-indicator[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-primary)}.location-request-container[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#fffffff2;border-radius:16px;box-shadow:0 4px 16px #00000026;padding:20px;text-align:center;max-width:300px;width:90%;z-index:1001;animation:_ngcontent-%COMP%_fadeIn .5s ease-out}.location-request-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin:10px 0;--border-radius: 10px;--box-shadow: 0 4px 8px rgba(var(--ion-color-primary-rgb), .3);font-weight:600;height:48px}.location-request-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:active{--box-shadow: 0 2px 4px rgba(var(--ion-color-primary-rgb), .2);transform:translateY(2px)}.location-request-container[_ngcontent-%COMP%]   .location-help-text[_ngcontent-%COMP%]{margin:10px 0 0;font-size:14px;color:var(--ion-color-medium);line-height:1.4}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translate(-50%,-40%)}to{opacity:1;transform:translate(-50%,-50%)}}.map-default-message[_ngcontent-%COMP%]{position:absolute;bottom:30px;left:50%;transform:translate(-50%);background:#fffffff2;border-radius:16px;box-shadow:0 2px 8px #0000001a;padding:12px 16px;text-align:center;max-width:300px;z-index:1000}.map-default-message[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:var(--ion-color-primary);margin-bottom:8px}.map-default-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 5px;font-weight:500;font-size:16px;color:var(--ion-color-dark)}.map-default-message[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:13px;display:block;line-height:1.4}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:.6}50%{opacity:1}to{opacity:.6}}@keyframes _ngcontent-%COMP%_pulsate{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.5);opacity:.4}to{transform:scale(.8);opacity:.8}}.marker-pulse-container[_ngcontent-%COMP%]{position:relative}.marker-pulse[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;width:50px;height:50px;margin-top:-25px;margin-left:-25px;border-radius:50%;z-index:100;pointer-events:none;animation:_ngcontent-%COMP%_pulsate 1.5s ease-out infinite;box-shadow:0 0 10px #00000080}[_nghost-%COMP%]     .popup-button{background-color:var(--ion-color-primary);color:#fff;border:none;border-radius:4px;padding:6px 12px;font-size:14px;cursor:pointer;margin-top:8px;transition:background-color .2s}[_nghost-%COMP%]     .popup-button:hover{background-color:var(--ion-color-primary-shade)}[_nghost-%COMP%]     .evacuation-popup h3{margin:0 0 8px;font-size:16px;font-weight:600}[_nghost-%COMP%]     .evacuation-popup p{margin:4px 0;font-size:14px}.evacuation-details-modal[_ngcontent-%COMP%]{--border-radius: 16px 16px 0 0;--backdrop-opacity: .4}']});let v=S;return v})();export{$g as MapPage};
