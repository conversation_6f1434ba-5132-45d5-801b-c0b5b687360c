import{a as B}from"./chunk-6Z4VSBPG.js";import"./chunk-L5U4Z35Y.js";import{b as N}from"./chunk-KAKQC7EG.js";import{a as _}from"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{Ca as F,Cb as j,E as n,F as r,G as h,I as w,Ia as x,L as l,Ma as I,P as d,Q as p,R as f,Va as O,_ as C,ab as E,bb as W,da as k,eb as z,ga as y,ha as P,ia as S,ja as T,ka as b,la as v,na as R,sb as A,x as m,y as g,yb as L,z as M}from"./chunk-6UWMO7JM.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as a}from"./chunk-LNJ3S2LQ.js";var Z=(()=>{let c=class c{constructor(i,o,e,s,t,q){this.authService=i,this.router=o,this.http=e,this.platform=s,this.fcmService=t,this.alertController=q,this.user={full_name:"",email:"",password:"",confirmPassword:""},this.fcmToken="",this.fcmTokenReady=!1}ngOnInit(){return a(this,null,function*(){console.log("\u{1F525} Register page initializing..."),yield this.initializeFCM()})}initializeFCM(){return a(this,null,function*(){try{console.log("\u{1F525} Initializing FCM for registration..."),yield this.fcmService.initPush(),yield this.getFCMToken(),console.log("\u2705 FCM initialization complete, token ready:",!!this.fcmToken),this.fcmTokenReady=!0}catch(i){console.error("\u274C FCM initialization failed:",i),this.fcmTokenReady=!1}})}getFCMToken(){return a(this,null,function*(){try{if(!this.platform.is("cordova")&&!this.platform.is("capacitor")){console.log("Running in browser, using mock FCM token"),this.fcmToken="browser-mock-token-"+Math.random().toString(36).substring(2,15),console.log("Mock FCM Token:",this.fcmToken);return}console.log("Getting FCM token from service..."),this.fcmToken=yield this.fcmService.getToken(),console.log("\u2705 FCM Token obtained:",this.fcmToken.substring(0,20)+"...")}catch(i){console.error("\u274C Error getting FCM token from service:",i),this.fcmToken=""}})}onRegister(){return a(this,null,function*(){if(this.user.password!==this.user.confirmPassword){yield this.presentAlert("Registration Failed","Passwords do not match!");return}this.authService.register({full_name:this.user.full_name,email:this.user.email,password:this.user.password,password_confirmation:this.user.confirmPassword}).subscribe({next:i=>a(this,null,function*(){if(console.log("Registration successful:",i),this.fcmToken){console.log("Registering FCM token after registration:",this.fcmToken);let o={token:this.fcmToken,device_type:this.platform.is("ios")?"ios":"android",project_id:_.firebase.projectId};console.log("Token registration payload:",o),this.fcmService.registerTokenWithBackend(this.fcmToken)}else console.warn("No FCM token available to register after registration");yield this.presentAlert("Registration Successful","Your account has been created successfully. Please log in."),this.router.navigate(["/login"])}),error:i=>a(this,null,function*(){var o;console.error("Registration error:",i),yield this.presentAlert("Registration Failed","Registration failed: "+(((o=i.error)==null?void 0:o.message)||"Unknown error"))})})})}registerTokenWithEndpoints(i){return a(this,null,function*(){let o=[`${_.apiUrl}/device-token`,"http://localhost:8000/api/device-token","https://7af9-43-226-6-217.ngrok-free.app/api/device-token"];for(let e of o)try{let s=yield this.http.post(e,i).toPromise();console.log(`FCM token registered with ${e}:`,s),localStorage.setItem("fcm_token",this.fcmToken);break}catch(s){console.error(`Error registering token with ${e}:`,s)}})}presentAlert(i,o){return a(this,null,function*(){yield(yield this.alertController.create({header:i,message:o,buttons:["OK"]})).present()})}goToLogin(){this.router.navigate(["/login"])}};c.\u0275fac=function(o){return new(o||c)(g(B),g(k),g(C),g(F),g(N),g(L))},c.\u0275cmp=M({type:c,selectors:[["app-register"]],decls:33,vars:4,consts:[[1,"ion-padding","register-bg"],[1,"register-wrapper"],["src","assets/ALERTO.png","alt","App Logo",1,"register-logo"],[1,"register-title"],[1,"register-form",3,"ngSubmit"],["position","floating"],["type","text","name","full_name","required","",3,"ngModelChange","ngModel"],["type","email","name","email","required","",3,"ngModelChange","ngModel"],["type","password","name","password","required","",3,"ngModelChange","ngModel"],["type","password","name","confirmPassword","required","",3,"ngModelChange","ngModel"],["expand","block","type","submit",1,"register-btn"],[1,"ion-text-center","ion-margin-top"],[3,"click"]],template:function(o,e){o&1&&(n(0,"ion-content",0)(1,"div",1),h(2,"img",2),n(3,"h1",3),l(4,"Sign Up Here!"),r(),n(5,"form",4),w("ngSubmit",function(){return e.onRegister()}),n(6,"ion-item")(7,"ion-label",5),l(8,"Full Name:"),r(),n(9,"ion-input",6),f("ngModelChange",function(t){return p(e.user.full_name,t)||(e.user.full_name=t),t}),r()(),n(10,"ion-item")(11,"ion-label",5),l(12,"Email:"),r(),n(13,"ion-input",7),f("ngModelChange",function(t){return p(e.user.email,t)||(e.user.email=t),t}),r()(),n(14,"ion-item")(15,"ion-label",5),l(16,"Password:"),r(),n(17,"ion-input",8),f("ngModelChange",function(t){return p(e.user.password,t)||(e.user.password=t),t}),r()(),n(18,"ion-item")(19,"ion-label",5),l(20,"Confirm Password:"),r(),n(21,"ion-input",9),f("ngModelChange",function(t){return p(e.user.confirmPassword,t)||(e.user.confirmPassword=t),t}),r()(),h(22,"br")(23,"br"),n(24,"ion-button",10),l(25,"Register"),r()(),n(26,"div",11)(27,"ion-text"),l(28,"Already have an account? "),r(),n(29,"a",12),w("click",function(){return e.goToLogin()}),n(30,"strong")(31,"u"),l(32,"Log In"),r()()()()()()),o&2&&(m(9),d("ngModel",e.user.full_name),m(4),d("ngModel",e.user.email),m(4),d("ngModel",e.user.password),m(4),d("ngModel",e.user.confirmPassword))},dependencies:[j,I,O,E,W,z,A,x,R,b,y,P,v,T,S],styles:[".register-container[_ngcontent-%COMP%]{height:80vh;display:flex;align-items:center;justify-content:center}.register-wrapper[_ngcontent-%COMP%]{width:100%;max-width:420px;padding:32px 28px;margin:0 auto;display:flex;flex-direction:column;align-items:center;text-align:center}.register-logo[_ngcontent-%COMP%]{width:280px;height:280px}.register-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700}.register-desc[_ngcontent-%COMP%]{color:#888;font-size:1.1rem}.register-form[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:18px;color:#888}.register-form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:100%;--highlight-color-focused: xz#000000;--min-height: 44px;--padding-start: 0;--padding-end: 0;--inner-padding-end: 0;--inner-padding-start: 0}.forgot-link[_ngcontent-%COMP%]{text-align:right;font-size:.95rem}.forgot-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#1565c0;text-decoration:none}.register-link[_ngcontent-%COMP%]{font-size:1rem;color:#444}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#1565c0;text-decoration:none;font-weight:600}.register-btn[_ngcontent-%COMP%]{--border-radius: 25px;font-size:1.1rem;height:48px;width:100%}"]});let u=c;return u})();export{Z as RegisterPage};
