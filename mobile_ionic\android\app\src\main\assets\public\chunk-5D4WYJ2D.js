import{a as G}from"./chunk-UMBNC2EW.js";import{a as V,b as j}from"./chunk-BNNKLWXU.js";import{a as B}from"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{$a as H,Bb as S,C as k,Cb as F,E as p,F as f,G as C,I as A,L as w,Ma as O,N as x,Na as _,Oa as q,Pa as I,Va as $,W as L,_ as T,_a as U,ba as R,da as N,g as y,o as h,tb as D,ub as K,x as v,yb as z,z as b,zb as Q}from"./chunk-6UWMO7JM.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{a as P,b as E,f as W,h as u}from"./chunk-LNJ3S2LQ.js";var a=W(j());var ct=(()=>{let g=class g{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.loadingCtrl=h(Q),this.toastCtrl=h(S),this.alertCtrl=h(z),this.http=h(T),this.router=h(N),this.route=h(R),this.mapboxRouting=h(G)}ngOnInit(){console.log("\u{1F7E0} EARTHQUAKE MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F7E0} EARTHQUAKE MAP: New center to highlight:",this.newCenterId))})}ngAfterViewInit(){return u(this,null,function*(){console.log("\u{1F7E0} EARTHQUAKE MAP: View initialized, loading map..."),setTimeout(()=>u(this,null,function*(){yield this.loadEarthquakeMap()}),100)})}loadEarthquakeMap(){return u(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading earthquake evacuation centers...",spinner:"crescent"});yield t.present();try{let o=yield V.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),n=o.coords.latitude,e=o.coords.longitude;this.userLocation={lat:n,lng:e},console.log(`\u{1F7E0} EARTHQUAKE MAP: User location [${n}, ${e}]`),this.initializeMap(n,e),yield this.loadEarthquakeCenters(n,e),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F7E0} Showing ${this.evacuationCenters.length} earthquake evacuation centers`,duration:3e3,color:"warning",position:"top"})).present()}catch(o){yield t.dismiss(),console.error("\u{1F7E0} EARTHQUAKE MAP: Error loading map",o),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadEarthquakeMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,o){if(console.log(`\u{1F7E0} EARTHQUAKE MAP: Initializing map at [${t}, ${o}]`),!document.getElementById("earthquake-map"))throw console.error("\u{1F7E0} EARTHQUAKE MAP: Container #earthquake-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=a.map("earthquake-map").setView([t,o],13),a.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=a.marker([t,o],{icon:a.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadEarthquakeCenters(t,o){return u(this,null,function*(){try{console.log("\u{1F7E0} EARTHQUAKE MAP: Fetching earthquake centers...");let n=yield y(this.http.get(`${B.apiUrl}/evacuation-centers`));if(console.log("\u{1F7E0} EARTHQUAKE MAP: Total centers received:",(n==null?void 0:n.length)||0),this.evacuationCenters=n.filter(e=>e.disaster_type==="Earthquake"),console.log(`\u{1F7E0} EARTHQUAKE MAP: Filtered to ${this.evacuationCenters.length} earthquake centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Earthquake Centers",message:"No earthquake evacuation centers found in the database.",buttons:["OK"]})).present();return}if(this.evacuationCenters.forEach(e=>{let i=Number(e.latitude),r=Number(e.longitude);if(!isNaN(i)&&!isNaN(r)){let s=a.marker([i,r],{icon:a.icon({iconUrl:"assets/forEarthquake.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),c=this.calculateDistance(t,o,i,r);s.on("click",()=>{this.showTransportationOptions(e)});let l=this.newCenterId&&e.id.toString()===this.newCenterId;s.bindPopup(`
            <div class="evacuation-popup">
              <h3>\u{1F7E0} ${e.name} ${l?"\u2B50 NEW!":""}</h3>
              <p><strong>Type:</strong> Earthquake Center</p>
              <p><strong>Distance:</strong> ${(c/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
              <p><em>Click marker for route options</em></p>
              ${l?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
            </div>
          `),l&&(s.openPopup(),this.map.setView([i,r],15),this.toastCtrl.create({message:`\u{1F195} New earthquake evacuation center: ${e.name}`,duration:5e3,color:"warning",position:"top"}).then(d=>d.present())),s.addTo(this.map),console.log(`\u{1F7E0} Added earthquake marker: ${e.name}`)}}),yield this.routeToTwoNearestCenters(),this.evacuationCenters.length>0){let e=a.latLngBounds([]);e.extend([t,o]),this.evacuationCenters.forEach(i=>{e.extend([Number(i.latitude),Number(i.longitude)])}),this.map.fitBounds(e,{padding:[50,50]})}}catch(n){console.error("\u{1F7E0} EARTHQUAKE MAP: Error loading centers",n),yield(yield this.toastCtrl.create({message:"Error loading earthquake centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,o,n,e){let r=t*Math.PI/180,s=n*Math.PI/180,c=(n-t)*Math.PI/180,l=(e-o)*Math.PI/180,d=Math.sin(c/2)*Math.sin(c/2)+Math.cos(r)*Math.cos(s)*Math.sin(l/2)*Math.sin(l/2);return 6371e3*(2*Math.atan2(Math.sqrt(d),Math.sqrt(1-d)))}routeToTwoNearestCenters(){return u(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F7E0} EARTHQUAKE MAP: No user location or evacuation centers available");return}try{console.log("\u{1F7E0} EARTHQUAKE MAP: Finding 2 nearest earthquake centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0)return;this.clearRoutes(),yield this.calculateRoutes(t)}catch(t){console.error("\u{1F7E0} EARTHQUAKE MAP: Error calculating routes",t)}})}getTwoNearestCenters(t,o){return this.evacuationCenters.map(e=>E(P({},e),{distance:this.calculateDistance(t,o,Number(e.latitude),Number(e.longitude))})).sort((e,i)=>e.distance-i.distance).slice(0,2)}calculateRoutes(t){return u(this,null,function*(){if(this.userLocation){this.routeLayer=a.layerGroup().addTo(this.map);for(let o=0;o<t.length;o++){let n=t[o],e=Number(n.latitude),i=Number(n.longitude);if(!isNaN(e)&&!isNaN(i))try{let r=this.mapboxRouting.convertTravelModeToProfile("walking"),s=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,i,e,r,{geometries:"geojson",overview:"simplified",steps:!1});if(s&&s.routes&&s.routes.length>0){let c=s.routes[0];a.polyline(c.geometry.coordinates.map(m=>[m[1],m[0]]),{color:"#ff9500",weight:4,opacity:.8,dashArray:o===0?void 0:"10, 10"}).addTo(this.routeLayer),console.log(`\u{1F7E0} Route ${o+1}: ${(c.distance/1e3).toFixed(2)}km, ${(c.duration/60).toFixed(0)}min`)}}catch(r){console.error(`\u{1F7E0} Error calculating route to center ${o+1}:`,r)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[]}showTransportationOptions(t){return u(this,null,function*(){yield(yield this.alertCtrl.create({header:`Route to ${t.name}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToCenter(t,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToCenter(t,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToCenter(t,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToCenter(t,o){return u(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let n=Number(t.latitude),e=Number(t.longitude);if(!isNaN(n)&&!isNaN(e)){let i=this.mapboxRouting.convertTravelModeToProfile(o),r=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,e,n,i,{geometries:"geojson",overview:"full",steps:!1});if(r&&r.routes&&r.routes.length>0){let s=r.routes[0],c="#ff9500";this.routeLayer=a.layerGroup().addTo(this.map);let l=a.polyline(s.geometry.coordinates.map(m=>[m[1],m[0]]),{color:c,weight:5,opacity:.8});l.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`\u{1F7E0} Route: ${(s.distance/1e3).toFixed(2)}km, ${(s.duration/60).toFixed(0)}min via ${o}`,duration:4e3,color:"warning"})).present(),this.map.fitBounds(l.getBounds(),{padding:[50,50]})}}}catch(n){console.error("\u{1F7E0} Error routing to center:",n),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}goBack(){this.router.navigate(["/tabs/home"])}ionViewWillLeave(){this.clearRoutes(),this.map&&this.map.remove()}};g.\u0275fac=function(o){return new(o||g)},g.\u0275cmp=b({type:g,selectors:[["app-earthquake-map"]],decls:18,vars:3,consts:[[3,"translucent"],["color","warning"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[3,"fullscreen"],["id","earthquake-map",2,"height","100%","width","100%"],[1,"floating-info"],[1,"info-row"],["name","warning","color","warning"],[1,"info-text"]],template:function(o,n){o&1&&(p(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),A("click",function(){return n.goBack()}),C(4,"ion-icon",4),f()(),p(5,"ion-title"),w(6,"\u{1F7E0} Earthquake Evacuation Centers"),f()()(),p(7,"ion-content",5),C(8,"div",6),p(9,"div",7)(10,"ion-card")(11,"ion-card-content")(12,"div",8),C(13,"ion-icon",9),p(14,"span"),w(15),f()(),p(16,"div",10),w(17," Showing evacuation centers specifically for earthquake disasters "),f()()()()()),o&2&&(k("translucent",!0),v(7),k("fullscreen",!0),v(8),x("Earthquake Centers: ",n.evacuationCenters.length,""))},dependencies:[F,O,_,q,I,$,U,H,D,K,L],styles:["#earthquake-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:250px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-warning);margin-bottom:4px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);line-height:1.3}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-warning);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-warning);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}"]});let M=g;return M})();export{ct as EarthquakeMapPage};
