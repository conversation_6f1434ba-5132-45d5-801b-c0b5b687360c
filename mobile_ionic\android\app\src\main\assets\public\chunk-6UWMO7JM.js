import{b as Yc}from"./chunk-IP3IRUG3.js";import{c as zc,e as Gc,f as Wc,g as qc,h as Zc}from"./chunk-UKIOCGZG.js";import{b as jc,c as Lc,d as Vc,e as Bc,f as Uc}from"./chunk-RXV5ZBOB.js";import{f as dh}from"./chunk-ODNE7IRY.js";import{a as mn,b as fh,c as hh,d as ph,f as Fc}from"./chunk-F4H6ZFEG.js";import{a as Hc}from"./chunk-JWIEPCRG.js";import{a as gh,d as mh}from"./chunk-WTKF7BA6.js";import{b as qt}from"./chunk-SV7S5NYR.js";import{a as Kn,b as Pc,c as Ni}from"./chunk-WTCPO44B.js";import{c as $c}from"./chunk-L5T6STQ3.js";import{m as vh}from"./chunk-3EJRMEWO.js";import{a as b,b as L,d as kc,g as u,h as ve}from"./chunk-LNJ3S2LQ.js";function Xn(t){let r=t(n=>{Error.call(n),n.stack=new Error().stack});return r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r}var rt=Xn(t=>function(){t(this),this.name="EmptyError",this.message="no elements in sequence"});function F(t){return typeof t=="function"}var Oi=Xn(t=>function(r){t(this),this.message=r?`${r.length} errors occurred during unsubscription:
${r.map((n,o)=>`${o+1}) ${n.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=r});function vn(t,e){if(t){let r=t.indexOf(e);0<=r&&t.splice(r,1)}}var ue=class t{constructor(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let e;if(!this.closed){this.closed=!0;let{_parentage:r}=this;if(r)if(this._parentage=null,Array.isArray(r))for(let i of r)i.remove(this);else r.remove(this);let{initialTeardown:n}=this;if(F(n))try{n()}catch(i){e=i instanceof Oi?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{yh(i)}catch(s){e=e!=null?e:[],s instanceof Oi?e=[...e,...s.errors]:e.push(s)}}if(e)throw new Oi(e)}}add(e){var r;if(e&&e!==this)if(this.closed)yh(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=(r=this._finalizers)!==null&&r!==void 0?r:[]).push(e)}}_hasParent(e){let{_parentage:r}=this;return r===e||Array.isArray(r)&&r.includes(e)}_addParent(e){let{_parentage:r}=this;this._parentage=Array.isArray(r)?(r.push(e),r):r?[r,e]:e}_removeParent(e){let{_parentage:r}=this;r===e?this._parentage=null:Array.isArray(r)&&vn(r,e)}remove(e){let{_finalizers:r}=this;r&&vn(r,e),e instanceof t&&e._removeParent(this)}};ue.EMPTY=(()=>{let t=new ue;return t.closed=!0,t})();var Qc=ue.EMPTY;function ki(t){return t instanceof ue||t&&"closed"in t&&F(t.remove)&&F(t.add)&&F(t.unsubscribe)}function yh(t){F(t)?t():t.unsubscribe()}var ot={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Jn={setTimeout(t,e,...r){let{delegate:n}=Jn;return n!=null&&n.setTimeout?n.setTimeout(t,e,...r):setTimeout(t,e,...r)},clearTimeout(t){let{delegate:e}=Jn;return((e==null?void 0:e.clearTimeout)||clearTimeout)(t)},delegate:void 0};function Pi(t){Jn.setTimeout(()=>{let{onUnhandledError:e}=ot;if(e)e(t);else throw t})}function io(){}var Dh=Kc("C",void 0,void 0);function Ih(t){return Kc("E",void 0,t)}function Ch(t){return Kc("N",t,void 0)}function Kc(t,e,r){return{kind:t,value:e,error:r}}var yn=null;function er(t){if(ot.useDeprecatedSynchronousErrorHandling){let e=!yn;if(e&&(yn={errorThrown:!1,error:null}),t(),e){let{errorThrown:r,error:n}=yn;if(yn=null,r)throw n}}else t()}function bh(t){ot.useDeprecatedSynchronousErrorHandling&&yn&&(yn.errorThrown=!0,yn.error=t)}var Dn=class extends ue{constructor(e){super(),this.isStopped=!1,e?(this.destination=e,ki(e)&&e.add(this)):this.destination=MI}static create(e,r,n){return new Zt(e,r,n)}next(e){this.isStopped?Jc(Ch(e),this):this._next(e)}error(e){this.isStopped?Jc(Ih(e),this):(this.isStopped=!0,this._error(e))}complete(){this.isStopped?Jc(Dh,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(e){this.destination.next(e)}_error(e){try{this.destination.error(e)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},EI=Function.prototype.bind;function Xc(t,e){return EI.call(t,e)}var el=class{constructor(e){this.partialObserver=e}next(e){let{partialObserver:r}=this;if(r.next)try{r.next(e)}catch(n){Fi(n)}}error(e){let{partialObserver:r}=this;if(r.error)try{r.error(e)}catch(n){Fi(n)}else Fi(e)}complete(){let{partialObserver:e}=this;if(e.complete)try{e.complete()}catch(r){Fi(r)}}},Zt=class extends Dn{constructor(e,r,n){super();let o;if(F(e)||!e)o={next:e!=null?e:void 0,error:r!=null?r:void 0,complete:n!=null?n:void 0};else{let i;this&&ot.useDeprecatedNextContext?(i=Object.create(e),i.unsubscribe=()=>this.unsubscribe(),o={next:e.next&&Xc(e.next,i),error:e.error&&Xc(e.error,i),complete:e.complete&&Xc(e.complete,i)}):o=e}this.destination=new el(o)}};function Fi(t){ot.useDeprecatedSynchronousErrorHandling?bh(t):Pi(t)}function wI(t){throw t}function Jc(t,e){let{onStoppedNotification:r}=ot;r&&Jn.setTimeout(()=>r(t,e))}var MI={closed:!0,next:io,error:wI,complete:io};function SI(t,e){let r=typeof e=="object";return new Promise((n,o)=>{let i=new Zt({next:s=>{n(s),i.unsubscribe()},error:o,complete:()=>{r?n(e.defaultValue):o(new rt)}});t.subscribe(i)})}var ji=class extends ue{constructor(e,r){super()}schedule(e,r=0){return this}};var so={setInterval(t,e,...r){let{delegate:n}=so;return n!=null&&n.setInterval?n.setInterval(t,e,...r):setInterval(t,e,...r)},clearInterval(t){let{delegate:e}=so;return((e==null?void 0:e.clearInterval)||clearInterval)(t)},delegate:void 0};var Li=class extends ji{constructor(e,r){super(e,r),this.scheduler=e,this.work=r,this.pending=!1}schedule(e,r=0){var n;if(this.closed)return this;this.state=e;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,r)),this.pending=!0,this.delay=r,this.id=(n=this.id)!==null&&n!==void 0?n:this.requestAsyncId(i,this.id,r),this}requestAsyncId(e,r,n=0){return so.setInterval(e.flush.bind(e,this),n)}recycleAsyncId(e,r,n=0){if(n!=null&&this.delay===n&&this.pending===!1)return r;r!=null&&so.clearInterval(r)}execute(e,r){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let n=this._execute(e,r);if(n)return n;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(e,r){let n=!1,o;try{this.work(e)}catch(i){n=!0,o=i||new Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:e,scheduler:r}=this,{actions:n}=r;this.work=this.state=this.scheduler=null,this.pending=!1,vn(n,this),e!=null&&(this.id=this.recycleAsyncId(r,e,null)),this.delay=null,super.unsubscribe()}}};var tl={now(){return(tl.delegate||Date).now()},delegate:void 0};var tr=class t{constructor(e,r=t.now){this.schedulerActionCtor=e,this.now=r}schedule(e,r=0,n){return new this.schedulerActionCtor(this,e).schedule(n,r)}};tr.now=tl.now;var Vi=class extends tr{constructor(e,r=tr.now){super(e,r),this.actions=[],this._active=!1}flush(e){let{actions:r}=this;if(this._active){r.push(e);return}let n;this._active=!0;do if(n=e.execute(e.state,e.delay))break;while(e=r.shift());if(this._active=!1,n){for(;e=r.shift();)e.unsubscribe();throw n}}};var nl=new Vi(Li),Eh=nl;var nr=typeof Symbol=="function"&&Symbol.observable||"@@observable";function be(t){return t}function rl(...t){return ol(t)}function ol(t){return t.length===0?be:t.length===1?t[0]:function(r){return t.reduce((n,o)=>o(n),r)}}var q=(()=>{class t{constructor(r){r&&(this._subscribe=r)}lift(r){let n=new t;return n.source=this,n.operator=r,n}subscribe(r,n,o){let i=_I(r)?r:new Zt(r,n,o);return er(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(r){try{return this._subscribe(r)}catch(n){r.error(n)}}forEach(r,n){return n=wh(n),new n((o,i)=>{let s=new Zt({next:a=>{try{r(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(r){var n;return(n=this.source)===null||n===void 0?void 0:n.subscribe(r)}[nr](){return this}pipe(...r){return ol(r)(this)}toPromise(r){return r=wh(r),new r((n,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>n(i))})}}return t.create=e=>new t(e),t})();function wh(t){var e;return(e=t!=null?t:ot.Promise)!==null&&e!==void 0?e:Promise}function TI(t){return t&&F(t.next)&&F(t.error)&&F(t.complete)}function _I(t){return t&&t instanceof Dn||TI(t)&&ki(t)}function Bi(t){return t&&F(t.schedule)}function Mh(t){return t instanceof Date&&!isNaN(t)}function Ui(t=0,e,r=Eh){let n=-1;return e!=null&&(Bi(e)?r=e:n=e),new q(o=>{let i=Mh(t)?+t-r.now():t;i<0&&(i=0);let s=0;return r.schedule(function(){o.closed||(o.next(s++),0<=n?this.schedule(void 0,n):o.complete())},i)})}function xI(t=0,e=nl){return t<0&&(t=0),Ui(t,t,e)}function il(t){return F(t==null?void 0:t.lift)}function G(t){return e=>{if(il(e))return e.lift(function(r){try{return t(r,this)}catch(n){this.error(n)}});throw new TypeError("Unable to lift unknown Observable type")}}function U(t,e,r,n,o){return new sl(t,e,r,n,o)}var sl=class extends Dn{constructor(e,r,n,o,i,s){super(e),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=r?function(a){try{r(a)}catch(c){e.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){e.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=n?function(){try{n()}catch(a){e.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var e;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:r}=this;super.unsubscribe(),!r&&((e=this.onFinalize)===null||e===void 0||e.call(this))}}};function rr(){return G((t,e)=>{let r=null;t._refCount++;let n=U(e,void 0,void 0,void 0,()=>{if(!t||t._refCount<=0||0<--t._refCount){r=null;return}let o=t._connection,i=r;r=null,o&&(!i||o===i)&&o.unsubscribe(),e.unsubscribe()});t.subscribe(n),n.closed||(r=t.connect())})}var or=class extends q{constructor(e,r){super(),this.source=e,this.subjectFactory=r,this._subject=null,this._refCount=0,this._connection=null,il(e)&&(this.lift=e.lift)}_subscribe(e){return this.getSubject().subscribe(e)}getSubject(){let e=this._subject;return(!e||e.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:e}=this;this._subject=this._connection=null,e==null||e.unsubscribe()}connect(){let e=this._connection;if(!e){e=this._connection=new ue;let r=this.getSubject();e.add(this.source.subscribe(U(r,void 0,()=>{this._teardown(),r.complete()},n=>{this._teardown(),r.error(n)},()=>this._teardown()))),e.closed&&(this._connection=null,e=ue.EMPTY)}return e}refCount(){return rr()(this)}};var Sh=Xn(t=>function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ee=(()=>{class t extends q{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(r){let n=new Hi(this,this);return n.operator=r,n}_throwIfClosed(){if(this.closed)throw new Sh}next(r){er(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let n of this.currentObservers)n.next(r)}})}error(r){er(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=r;let{observers:n}=this;for(;n.length;)n.shift().error(r)}})}complete(){er(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:r}=this;for(;r.length;)r.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var r;return((r=this.observers)===null||r===void 0?void 0:r.length)>0}_trySubscribe(r){return this._throwIfClosed(),super._trySubscribe(r)}_subscribe(r){return this._throwIfClosed(),this._checkFinalizedStatuses(r),this._innerSubscribe(r)}_innerSubscribe(r){let{hasError:n,isStopped:o,observers:i}=this;return n||o?Qc:(this.currentObservers=null,i.push(r),new ue(()=>{this.currentObservers=null,vn(i,r)}))}_checkFinalizedStatuses(r){let{hasError:n,thrownError:o,isStopped:i}=this;n?r.error(o):i&&r.complete()}asObservable(){let r=new q;return r.source=this,r}}return t.create=(e,r)=>new Hi(e,r),t})(),Hi=class extends ee{constructor(e,r){super(),this.destination=e,this.source=r}next(e){var r,n;(n=(r=this.destination)===null||r===void 0?void 0:r.next)===null||n===void 0||n.call(r,e)}error(e){var r,n;(n=(r=this.destination)===null||r===void 0?void 0:r.error)===null||n===void 0||n.call(r,e)}complete(){var e,r;(r=(e=this.destination)===null||e===void 0?void 0:e.complete)===null||r===void 0||r.call(e)}_subscribe(e){var r,n;return(n=(r=this.source)===null||r===void 0?void 0:r.subscribe(e))!==null&&n!==void 0?n:Qc}};var pe=class extends ee{constructor(e){super(),this._value=e}get value(){return this.getValue()}_subscribe(e){let r=super._subscribe(e);return!r.closed&&e.next(this._value),r}getValue(){let{hasError:e,thrownError:r,_value:n}=this;if(e)throw r;return this._throwIfClosed(),n}next(e){super.next(this._value=e)}};var Ne=new q(t=>t.complete());function Th(t){return t[t.length-1]}function $i(t){return F(Th(t))?t.pop():void 0}function Yt(t){return Bi(Th(t))?t.pop():void 0}var al=function(t,e){return al=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(r[o]=n[o])},al(t,e)};function r1(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");al(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}function M(t,e,r,n){var o=arguments.length,i=o<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,r):n,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(i=(o<3?s(i):o>3?s(e,r,i):s(e,r))||i);return o>3&&i&&Object.defineProperty(e,r,i),i}function xh(t,e,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function a(d){try{l(n.next(d))}catch(h){s(h)}}function c(d){try{l(n.throw(d))}catch(h){s(h)}}function l(d){d.done?i(d.value):o(d.value).then(a,c)}l((n=n.apply(t,e||[])).next())})}function _h(t){var e=typeof Symbol=="function"&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function In(t){return this instanceof In?(this.v=t,this):new In(t)}function Ah(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(t,e||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(p){return function(v){return Promise.resolve(v).then(p,h)}}function a(p,v){n[p]&&(o[p]=function(D){return new Promise(function(x,O){i.push([p,D,x,O])>1||c(p,D)})},v&&(o[p]=v(o[p])))}function c(p,v){try{l(n[p](v))}catch(D){g(i[0][3],D)}}function l(p){p.value instanceof In?Promise.resolve(p.value.v).then(d,h):g(i[0][2],p)}function d(p){c("next",p)}function h(p){c("throw",p)}function g(p,v){p(v),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Rh(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],r;return e?e.call(t):(t=typeof _h=="function"?_h(t):t[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(i){r[i]=t[i]&&function(s){return new Promise(function(a,c){s=t[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var ir=t=>t&&typeof t.length=="number"&&typeof t!="function";function zi(t){return F(t==null?void 0:t.then)}function Gi(t){return F(t[nr])}function Wi(t){return Symbol.asyncIterator&&F(t==null?void 0:t[Symbol.asyncIterator])}function qi(t){return new TypeError(`You provided ${t!==null&&typeof t=="object"?"an invalid object":`'${t}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function AI(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Zi=AI();function Yi(t){return F(t==null?void 0:t[Zi])}function Qi(t){return Ah(this,arguments,function*(){let r=t.getReader();try{for(;;){let{value:n,done:o}=yield In(r.read());if(o)return yield In(void 0);yield yield In(n)}}finally{r.releaseLock()}})}function Ki(t){return F(t==null?void 0:t.getReader)}function ae(t){if(t instanceof q)return t;if(t!=null){if(Gi(t))return RI(t);if(ir(t))return NI(t);if(zi(t))return OI(t);if(Wi(t))return Nh(t);if(Yi(t))return kI(t);if(Ki(t))return PI(t)}throw qi(t)}function RI(t){return new q(e=>{let r=t[nr]();if(F(r.subscribe))return r.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function NI(t){return new q(e=>{for(let r=0;r<t.length&&!e.closed;r++)e.next(t[r]);e.complete()})}function OI(t){return new q(e=>{t.then(r=>{e.closed||(e.next(r),e.complete())},r=>e.error(r)).then(null,Pi)})}function kI(t){return new q(e=>{for(let r of t)if(e.next(r),e.closed)return;e.complete()})}function Nh(t){return new q(e=>{FI(t,e).catch(r=>e.error(r))})}function PI(t){return Nh(Qi(t))}function FI(t,e){var r,n,o,i;return xh(this,void 0,void 0,function*(){try{for(r=Rh(t);n=yield r.next(),!n.done;){let s=n.value;if(e.next(s),e.closed)return}}catch(s){o={error:s}}finally{try{n&&!n.done&&(i=r.return)&&(yield i.call(r))}finally{if(o)throw o.error}}e.complete()})}function Oe(t,e,r,n=0,o=!1){let i=e.schedule(function(){r(),o?t.add(this.schedule(null,n)):this.unsubscribe()},n);if(t.add(i),!o)return i}function Xi(t,e=0){return G((r,n)=>{r.subscribe(U(n,o=>Oe(n,t,()=>n.next(o),e),()=>Oe(n,t,()=>n.complete(),e),o=>Oe(n,t,()=>n.error(o),e)))})}function Ji(t,e=0){return G((r,n)=>{n.add(t.schedule(()=>r.subscribe(n),e))})}function Oh(t,e){return ae(t).pipe(Ji(e),Xi(e))}function kh(t,e){return ae(t).pipe(Ji(e),Xi(e))}function Ph(t,e){return new q(r=>{let n=0;return e.schedule(function(){n===t.length?r.complete():(r.next(t[n++]),r.closed||this.schedule())})})}function Fh(t,e){return new q(r=>{let n;return Oe(r,e,()=>{n=t[Zi](),Oe(r,e,()=>{let o,i;try{({value:o,done:i}=n.next())}catch(s){r.error(s);return}i?r.complete():r.next(o)},0,!0)}),()=>F(n==null?void 0:n.return)&&n.return()})}function es(t,e){if(!t)throw new Error("Iterable cannot be null");return new q(r=>{Oe(r,e,()=>{let n=t[Symbol.asyncIterator]();Oe(r,e,()=>{n.next().then(o=>{o.done?r.complete():r.next(o.value)})},0,!0)})})}function jh(t,e){return es(Qi(t),e)}function Lh(t,e){if(t!=null){if(Gi(t))return Oh(t,e);if(ir(t))return Ph(t,e);if(zi(t))return kh(t,e);if(Wi(t))return es(t,e);if(Yi(t))return Fh(t,e);if(Ki(t))return jh(t,e)}throw qi(t)}function re(t,e){return e?Lh(t,e):ae(t)}function P(...t){let e=Yt(t);return re(t,e)}function sr(t,e){let r=F(t)?t:()=>t,n=o=>o.error(r());return new q(e?o=>e.schedule(n,0,o):n)}function cl(t){return!!t&&(t instanceof q||F(t.lift)&&F(t.subscribe))}function H(t,e){return G((r,n)=>{let o=0;r.subscribe(U(n,i=>{n.next(t.call(e,i,o++))}))})}var{isArray:jI}=Array;function LI(t,e){return jI(e)?t(...e):t(e)}function ar(t){return H(e=>LI(t,e))}var{isArray:VI}=Array,{getPrototypeOf:BI,prototype:UI,keys:HI}=Object;function ts(t){if(t.length===1){let e=t[0];if(VI(e))return{args:e,keys:null};if($I(e)){let r=HI(e);return{args:r.map(n=>e[n]),keys:r}}}return{args:t,keys:null}}function $I(t){return t&&typeof t=="object"&&BI(t)===UI}function ns(t,e){return t.reduce((r,n,o)=>(r[n]=e[o],r),{})}function Cn(...t){let e=Yt(t),r=$i(t),{args:n,keys:o}=ts(t);if(n.length===0)return re([],e);let i=new q(zI(n,e,o?s=>ns(o,s):be));return r?i.pipe(ar(r)):i}function zI(t,e,r=be){return n=>{Vh(e,()=>{let{length:o}=t,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Vh(e,()=>{let l=re(t[c],e),d=!1;l.subscribe(U(n,h=>{i[c]=h,d||(d=!0,a--),a||n.next(r(i.slice()))},()=>{--s||n.complete()}))},n)},n)}}function Vh(t,e,r){t?Oe(r,t,e):e()}function Bh(t,e,r,n,o,i,s,a){let c=[],l=0,d=0,h=!1,g=()=>{h&&!c.length&&!l&&e.complete()},p=D=>l<n?v(D):c.push(D),v=D=>{i&&e.next(D),l++;let x=!1;ae(r(D,d++)).subscribe(U(e,O=>{o==null||o(O),i?p(O):e.next(O)},()=>{x=!0},void 0,()=>{if(x)try{for(l--;c.length&&l<n;){let O=c.shift();s?Oe(e,s,()=>v(O)):v(O)}g()}catch(O){e.error(O)}}))};return t.subscribe(U(e,p,()=>{h=!0,g()})),()=>{a==null||a()}}function le(t,e,r=1/0){return F(e)?le((n,o)=>H((i,s)=>e(n,i,o,s))(ae(t(n,o))),r):(typeof e=="number"&&(r=e),G((n,o)=>Bh(n,o,t,r)))}function cr(t=1/0){return le(be,t)}function Uh(){return cr(1)}function lr(...t){return Uh()(re(t,Yt(t)))}function rs(t){return new q(e=>{ae(t()).subscribe(e)})}function ll(...t){let e=$i(t),{args:r,keys:n}=ts(t),o=new q(i=>{let{length:s}=r;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let d=0;d<s;d++){let h=!1;ae(r[d]).subscribe(U(i,g=>{h||(h=!0,l--),a[d]=g},()=>c--,void 0,()=>{(!c||!h)&&(l||i.next(n?ns(n,a):a),i.complete())}))}});return e?o.pipe(ar(e)):o}var GI=["addListener","removeListener"],WI=["addEventListener","removeEventListener"],qI=["on","off"];function bn(t,e,r,n){if(F(r)&&(n=r,r=void 0),n)return bn(t,e,r).pipe(ar(n));let[o,i]=QI(t)?WI.map(s=>a=>t[s](e,a,r)):ZI(t)?GI.map(Hh(t,e)):YI(t)?qI.map(Hh(t,e)):[];if(!o&&ir(t))return le(s=>bn(s,e,r))(ae(t));if(!o)throw new TypeError("Invalid event target");return new q(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function Hh(t,e){return r=>n=>t[r](e,n)}function ZI(t){return F(t.addListener)&&F(t.removeListener)}function YI(t){return F(t.on)&&F(t.off)}function QI(t){return F(t.addEventListener)&&F(t.removeEventListener)}function ye(t,e){return G((r,n)=>{let o=0;r.subscribe(U(n,i=>t.call(e,i,o++)&&n.next(i)))})}function pt(t){return G((e,r)=>{let n=null,o=!1,i;n=e.subscribe(U(r,void 0,void 0,s=>{i=ae(t(s,pt(t)(e))),n?(n.unsubscribe(),n=null,i.subscribe(r)):o=!0})),o&&(n.unsubscribe(),n=null,i.subscribe(r))})}function $h(t,e,r,n,o){return(i,s)=>{let a=r,c=e,l=0;i.subscribe(U(s,d=>{let h=l++;c=a?t(c,d,h):(a=!0,d),n&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function gt(t,e){return F(e)?le(t,e,1):le(t,1)}function Qt(t){return G((e,r)=>{let n=!1;e.subscribe(U(r,o=>{n=!0,r.next(o)},()=>{n||r.next(t),r.complete()}))})}function kt(t){return t<=0?()=>Ne:G((e,r)=>{let n=0;e.subscribe(U(r,o=>{++n<=t&&(r.next(o),t<=n&&r.complete())}))})}function ul(t,e=be){return t=t!=null?t:KI,G((r,n)=>{let o,i=!0;r.subscribe(U(n,s=>{let a=e(s);(i||!t(o,a))&&(i=!1,o=a,n.next(s))}))})}function KI(t,e){return t===e}function os(t=XI){return G((e,r)=>{let n=!1;e.subscribe(U(r,o=>{n=!0,r.next(o)},()=>n?r.complete():r.error(t())))})}function XI(){return new rt}function Kt(t){return G((e,r)=>{try{e.subscribe(r)}finally{r.add(t)}})}function Pt(t,e){let r=arguments.length>=2;return n=>n.pipe(t?ye((o,i)=>t(o,i,n)):be,kt(1),r?Qt(e):os(()=>new rt))}function ur(t){return t<=0?()=>Ne:G((e,r)=>{let n=[];e.subscribe(U(r,o=>{n.push(o),t<n.length&&n.shift()},()=>{for(let o of n)r.next(o);r.complete()},void 0,()=>{n=null}))})}function dl(t,e){let r=arguments.length>=2;return n=>n.pipe(t?ye((o,i)=>t(o,i,n)):be,ur(1),r?Qt(e):os(()=>new rt))}function JI(t=1/0){let e;t&&typeof t=="object"?e=t:e={count:t};let{count:r=1/0,delay:n,resetOnSuccess:o=!1}=e;return r<=0?be:G((i,s)=>{let a=0,c,l=()=>{let d=!1;c=i.subscribe(U(s,h=>{o&&(a=0),s.next(h)},void 0,h=>{if(a++<r){let g=()=>{c?(c.unsubscribe(),c=null,l()):d=!0};if(n!=null){let p=typeof n=="number"?Ui(n):ae(n(h,a)),v=U(s,()=>{v.unsubscribe(),g()},()=>{s.complete()});p.subscribe(v)}else g()}else s.error(h)})),d&&(c.unsubscribe(),c=null,l())};l()})}function fl(t,e){return G($h(t,e,arguments.length>=2,!0))}function hl(...t){let e=Yt(t);return G((r,n)=>{(e?lr(t,r,e):lr(t,r)).subscribe(n)})}function De(t,e){return G((r,n)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&n.complete();r.subscribe(U(n,c=>{o==null||o.unsubscribe();let l=0,d=i++;ae(t(c,d)).subscribe(o=U(n,h=>n.next(e?e(c,h,d,l++):h),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function pl(t){return G((e,r)=>{ae(t).subscribe(U(r,()=>r.complete(),io)),!r.closed&&e.subscribe(r)})}function Ee(t,e,r){let n=F(t)||e||r?{next:t,error:e,complete:r}:t;return n?G((o,i)=>{var s;(s=n.subscribe)===null||s===void 0||s.call(n);let a=!0;o.subscribe(U(i,c=>{var l;(l=n.next)===null||l===void 0||l.call(n,c),i.next(c)},()=>{var c;a=!1,(c=n.complete)===null||c===void 0||c.call(n),i.complete()},c=>{var l;a=!1,(l=n.error)===null||l===void 0||l.call(n,c),i.error(c)},()=>{var c,l;a&&((c=n.unsubscribe)===null||c===void 0||c.call(n)),(l=n.finalize)===null||l===void 0||l.call(n)}))}):be}function Dl(t,e){return Object.is(t,e)}var fe=null,is=!1,Il=1,Ye=Symbol("SIGNAL");function Z(t){let e=fe;return fe=t,e}function Cl(){return fe}var co={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function lo(t){if(is)throw new Error("");if(fe===null)return;fe.consumerOnSignalRead(t);let e=fe.nextProducerIndex++;if(ls(fe),e<fe.producerNode.length&&fe.producerNode[e]!==t&&ao(fe)){let r=fe.producerNode[e];cs(r,fe.producerIndexOfThis[e])}fe.producerNode[e]!==t&&(fe.producerNode[e]=t,fe.producerIndexOfThis[e]=ao(fe)?Gh(t,fe,e):0),fe.producerLastReadVersion[e]=t.version}function zh(){Il++}function bl(t){if(!(ao(t)&&!t.dirty)&&!(!t.dirty&&t.lastCleanEpoch===Il)){if(!t.producerMustRecompute(t)&&!Sl(t)){yl(t);return}t.producerRecomputeValue(t),yl(t)}}function El(t){if(t.liveConsumerNode===void 0)return;let e=is;is=!0;try{for(let r of t.liveConsumerNode)r.dirty||eC(r)}finally{is=e}}function wl(){return(fe==null?void 0:fe.consumerAllowSignalWrites)!==!1}function eC(t){var e;t.dirty=!0,El(t),(e=t.consumerMarkedDirty)==null||e.call(t,t)}function yl(t){t.dirty=!1,t.lastCleanEpoch=Il}function as(t){return t&&(t.nextProducerIndex=0),Z(t)}function Ml(t,e){if(Z(e),!(!t||t.producerNode===void 0||t.producerIndexOfThis===void 0||t.producerLastReadVersion===void 0)){if(ao(t))for(let r=t.nextProducerIndex;r<t.producerNode.length;r++)cs(t.producerNode[r],t.producerIndexOfThis[r]);for(;t.producerNode.length>t.nextProducerIndex;)t.producerNode.pop(),t.producerLastReadVersion.pop(),t.producerIndexOfThis.pop()}}function Sl(t){ls(t);for(let e=0;e<t.producerNode.length;e++){let r=t.producerNode[e],n=t.producerLastReadVersion[e];if(n!==r.version||(bl(r),n!==r.version))return!0}return!1}function Tl(t){if(ls(t),ao(t))for(let e=0;e<t.producerNode.length;e++)cs(t.producerNode[e],t.producerIndexOfThis[e]);t.producerNode.length=t.producerLastReadVersion.length=t.producerIndexOfThis.length=0,t.liveConsumerNode&&(t.liveConsumerNode.length=t.liveConsumerIndexOfThis.length=0)}function Gh(t,e,r){if(Wh(t),t.liveConsumerNode.length===0&&qh(t))for(let n=0;n<t.producerNode.length;n++)t.producerIndexOfThis[n]=Gh(t.producerNode[n],t,n);return t.liveConsumerIndexOfThis.push(r),t.liveConsumerNode.push(e)-1}function cs(t,e){if(Wh(t),t.liveConsumerNode.length===1&&qh(t))for(let n=0;n<t.producerNode.length;n++)cs(t.producerNode[n],t.producerIndexOfThis[n]);let r=t.liveConsumerNode.length-1;if(t.liveConsumerNode[e]=t.liveConsumerNode[r],t.liveConsumerIndexOfThis[e]=t.liveConsumerIndexOfThis[r],t.liveConsumerNode.length--,t.liveConsumerIndexOfThis.length--,e<t.liveConsumerNode.length){let n=t.liveConsumerIndexOfThis[e],o=t.liveConsumerNode[e];ls(o),o.producerIndexOfThis[n]=e}}function ao(t){var e,r;return t.consumerIsAlwaysLive||((r=(e=t==null?void 0:t.liveConsumerNode)==null?void 0:e.length)!=null?r:0)>0}function ls(t){var e,r,n;(e=t.producerNode)!=null||(t.producerNode=[]),(r=t.producerIndexOfThis)!=null||(t.producerIndexOfThis=[]),(n=t.producerLastReadVersion)!=null||(t.producerLastReadVersion=[])}function Wh(t){var e,r;(e=t.liveConsumerNode)!=null||(t.liveConsumerNode=[]),(r=t.liveConsumerIndexOfThis)!=null||(t.liveConsumerIndexOfThis=[])}function qh(t){return t.producerNode!==void 0}function _l(t,e){let r=Object.create(tC);r.computation=t,e!==void 0&&(r.equal=e);let n=()=>{if(bl(r),lo(r),r.value===ss)throw r.error;return r.value};return n[Ye]=r,n}var gl=Symbol("UNSET"),ml=Symbol("COMPUTING"),ss=Symbol("ERRORED"),tC=L(b({},co),{value:gl,dirty:!0,error:null,equal:Dl,kind:"computed",producerMustRecompute(t){return t.value===gl||t.value===ml},producerRecomputeValue(t){if(t.value===ml)throw new Error("Detected cycle in computations.");let e=t.value;t.value=ml;let r=as(t),n,o=!1;try{n=t.computation(),Z(null),o=e!==gl&&e!==ss&&n!==ss&&t.equal(e,n)}catch(i){n=ss,t.error=i}finally{Ml(t,r)}if(o){t.value=e;return}t.value=n,t.version++}});function nC(){throw new Error}var Zh=nC;function Yh(t){Zh(t)}function xl(t){Zh=t}var vl=null;function Al(t,e){let r=Object.create(us);r.value=t,e!==void 0&&(r.equal=e);let n=()=>(lo(r),r.value);return n[Ye]=r,n}function uo(t,e){wl()||Yh(t),t.equal(t.value,e)||(t.value=e,rC(t))}function Rl(t,e){wl()||Yh(t),uo(t,e(t.value))}var us=L(b({},co),{equal:Dl,value:void 0,kind:"signal"});function rC(t){t.version++,zh(),El(t),vl==null||vl()}function Nl(t){let e=Z(null);try{return t()}finally{Z(e)}}var Ol;function fo(){return Ol}function Ft(t){let e=Ol;return Ol=t,e}var ds=Symbol("NotFound");var qp="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",R=class extends Error{code;constructor(e,r){super(Zp(e,r)),this.code=e}};function aC(t){return`NG0${Math.abs(t)}`}function Zp(t,e){return`${aC(t)}${e?": "+e:""}`}var Yp=Symbol("InputSignalNode#UNSET"),cC=L(b({},us),{transformFn:void 0,applyValueToInputSignal(t,e){uo(t,e)}});function Qp(t,e){let r=Object.create(cC);r.value=t,r.transformFn=e==null?void 0:e.transform;function n(){if(lo(r),r.value===Yp){let i=null;throw new R(-950,i)}return r.value}return n[Ye]=r,n}function Ar(t){return{toString:t}.toString()}var dr="__annotations__",fr="__parameters__",Qh="__prop__metadata__";function lC(t,e,r,n,o){return Ar(()=>{let i=Kp(e);function s(...a){if(this instanceof s)return i.call(this,...a),this;let c=new s(...a);return function(d){return o&&o(d,...a),(d.hasOwnProperty(dr)?d[dr]:Object.defineProperty(d,dr,{value:[]})[dr]).push(c),d}}return r&&(s.prototype=Object.create(r.prototype)),s.prototype.ngMetadataName=t,s.annotationCls=s,s})}function Kp(t){return function(...r){if(t){let n=t(...r);for(let o in n)this[o]=n[o]}}}function Rr(t,e,r){return Ar(()=>{let n=Kp(e);function o(...i){if(this instanceof o)return n.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,d){let h=c.hasOwnProperty(fr)?c[fr]:Object.defineProperty(c,fr,{value:[]})[fr];for(;h.length<=d;)h.push(null);return(h[d]=h[d]||[]).push(s),c}}return o.prototype.ngMetadataName=t,o.annotationCls=o,o})}var Pe=globalThis;function J(t){for(let e in t)if(t[e]===J)return e;throw Error("Could not find renamed property on target object.")}function uC(t,e){for(let r in e)e.hasOwnProperty(r)&&!t.hasOwnProperty(r)&&(t[r]=e[r])}function ke(t){if(typeof t=="string")return t;if(Array.isArray(t))return`[${t.map(ke).join(", ")}]`;if(t==null)return""+t;let e=t.overriddenName||t.name;if(e)return`${e}`;let r=t.toString();if(r==null)return""+r;let n=r.indexOf(`
`);return n>=0?r.slice(0,n):r}function Kh(t,e){return t?e?`${t} ${e}`:t:e||""}var dC=J({__forward_ref__:J});function $e(t){return t.__forward_ref__=$e,t.toString=function(){return ke(this())},t}function Me(t){return Xp(t)?t():t}function Xp(t){return typeof t=="function"&&t.hasOwnProperty(dC)&&t.__forward_ref__===$e}function A(t){return{token:t.token,providedIn:t.providedIn||null,factory:t.factory,value:void 0}}function Te(t){return{providers:t.providers||[],imports:t.imports||[]}}function Qs(t){return Xh(t,ws)||Xh(t,eg)}function Jp(t){return Qs(t)!==null}function Xh(t,e){return t.hasOwnProperty(e)?t[e]:null}function fC(t){let e=t&&(t[ws]||t[eg]);return e||null}function Jh(t){return t&&(t.hasOwnProperty(ep)||t.hasOwnProperty(hC))?t[ep]:null}var ws=J({\u0275prov:J}),ep=J({\u0275inj:J}),eg=J({ngInjectableDef:J}),hC=J({ngInjectorDef:J}),N=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(e,r){this._desc=e,this.\u0275prov=void 0,typeof r=="number"?this.__NG_ELEMENT_ID__=r:r!==void 0&&(this.\u0275prov=A({token:this,providedIn:r.providedIn||"root",factory:r.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function tg(t){return t&&!!t.\u0275providers}var pC=J({\u0275cmp:J}),gC=J({\u0275dir:J}),mC=J({\u0275pipe:J}),vC=J({\u0275mod:J}),Dr=J({\u0275fac:J}),vo=J({__NG_ELEMENT_ID__:J}),tp=J({__NG_ENV_ID__:J});function Tn(t){return typeof t=="string"?t:t==null?"":String(t)}function yC(t){return typeof t=="function"?t.name||t.toString():typeof t=="object"&&t!=null&&typeof t.type=="function"?t.type.name||t.type.toString():Tn(t)}function ng(t,e){throw new R(-200,t)}function qu(t,e){throw new R(-201,!1)}var $=function(t){return t[t.Default=0]="Default",t[t.Host=1]="Host",t[t.Self=2]="Self",t[t.SkipSelf=4]="SkipSelf",t[t.Optional=8]="Optional",t}($||{}),Yl;function rg(){return Yl}function Qe(t){let e=Yl;return Yl=t,e}function og(t,e,r){let n=Qs(t);if(n&&n.providedIn=="root")return n.value===void 0?n.value=n.factory():n.value;if(r&$.Optional)return null;if(e!==void 0)return e;qu(t,"Injector")}var DC={},wn=DC,Ql="__NG_DI_FLAG__",Ms=class{injector;constructor(e){this.injector=e}retrieve(e,r){let n=r;return this.injector.get(e,n.optional?ds:wn,n)}},Ss="ngTempTokenPath",IC="ngTokenPath",CC=/\n/gm,bC="\u0275",np="__source";function EC(t,e=$.Default){if(fo()===void 0)throw new R(-203,!1);if(fo()===null)return og(t,void 0,e);{let r=fo(),n;return r instanceof Ms?n=r.injector:n=r,n.get(t,e&$.Optional?null:void 0,e)}}function k(t,e=$.Default){return(rg()||EC)(Me(t),e)}function wC(t){throw new R(202,!1)}function I(t,e=$.Default){return k(t,Ks(e))}function Ks(t){return typeof t>"u"||typeof t=="number"?t:0|(t.optional&&8)|(t.host&&1)|(t.self&&2)|(t.skipSelf&&4)}function Kl(t){let e=[];for(let r=0;r<t.length;r++){let n=Me(t[r]);if(Array.isArray(n)){if(n.length===0)throw new R(900,!1);let o,i=$.Default;for(let s=0;s<n.length;s++){let a=n[s],c=MC(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}e.push(k(o,i))}else e.push(k(n))}return e}function So(t,e){return t[Ql]=e,t.prototype[Ql]=e,t}function MC(t){return t[Ql]}function SC(t,e,r,n){let o=t[Ss];throw e[np]&&o.unshift(e[np]),t.message=TC(`
`+t.message,o,r,n),t[IC]=o,t[Ss]=null,t}function TC(t,e,r,n=null){t=t&&t.charAt(0)===`
`&&t.charAt(1)==bC?t.slice(2):t;let o=ke(e);if(Array.isArray(e))o=e.map(ke).join(" -> ");else if(typeof e=="object"){let i=[];for(let s in e)if(e.hasOwnProperty(s)){let a=e[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):ke(a)))}o=`{${i.join(", ")}}`}return`${r}${n?"("+n+")":""}[${o}]: ${t.replace(CC,`
  `)}`}var _C=So(Rr("Inject",t=>({token:t})),-1),ig=So(Rr("Optional"),8),xC=So(Rr("Self"),2),sg=So(Rr("SkipSelf"),4),AC=So(Rr("Host"),1);function Ir(t,e){let r=t.hasOwnProperty(Dr);return r?t[Dr]:null}function RC(t,e,r){if(t.length!==e.length)return!1;for(let n=0;n<t.length;n++){let o=t[n],i=e[n];if(r&&(o=r(o),i=r(i)),i!==o)return!1}return!0}function NC(t){return t.flat(Number.POSITIVE_INFINITY)}function Zu(t,e){t.forEach(r=>Array.isArray(r)?Zu(r,e):e(r))}function ag(t,e,r){e>=t.length?t.push(r):t.splice(e,0,r)}function Ts(t,e){return e>=t.length-1?t.pop():t.splice(e,1)[0]}function ys(t,e){let r=[];for(let n=0;n<t;n++)r.push(e);return r}function OC(t,e,r,n){let o=t.length;if(o==e)t.push(r,n);else if(o===1)t.push(n,t[0]),t[0]=r;else{for(o--,t.push(t[o-1],t[o]);o>e;){let i=o-2;t[o]=t[i],o--}t[e]=r,t[e+1]=n}}function kC(t,e,r){let n=To(t,e);return n>=0?t[n|1]=r:(n=~n,OC(t,n,e,r)),n}function kl(t,e){let r=To(t,e);if(r>=0)return t[r|1]}function To(t,e){return PC(t,e,1)}function PC(t,e,r){let n=0,o=t.length>>r;for(;o!==n;){let i=n+(o-n>>1),s=t[i<<r];if(e===s)return i<<r;s>e?o=i:n=i+1}return~(o<<r)}var _n={},Ke=[],Do=new N(""),cg=new N("",-1),lg=new N(""),_s=class{get(e,r=wn){if(r===wn){let n=new Error(`NullInjectorError: No provider for ${ke(e)}!`);throw n.name="NullInjectorError",n}return r}};function ug(t,e){let r=t[vC]||null;if(!r&&e===!0)throw new Error(`Type ${ke(t)} does not have '\u0275mod' property.`);return r}function tn(t){return t[pC]||null}function FC(t){return t[gC]||null}function jC(t){return t[mC]||null}function Xs(t){return{\u0275providers:t}}function LC(...t){return{\u0275providers:dg(!0,t),\u0275fromNgModule:!0}}function dg(t,...e){let r=[],n=new Set,o,i=s=>{r.push(s)};return Zu(e,s=>{let a=s;Xl(a,i,[],n)&&(o||(o=[]),o.push(a))}),o!==void 0&&fg(o,i),r}function fg(t,e){for(let r=0;r<t.length;r++){let{ngModule:n,providers:o}=t[r];Yu(o,i=>{e(i,n)})}}function Xl(t,e,r,n){if(t=Me(t),!t)return!1;let o=null,i=Jh(t),s=!i&&tn(t);if(!i&&!s){let c=t.ngModule;if(i=Jh(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=t}let a=n.has(o);if(s){if(a)return!1;if(n.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Xl(l,e,r,n)}}else if(i){if(i.imports!=null&&!a){n.add(o);let l;try{Zu(i.imports,d=>{Xl(d,e,r,n)&&(l||(l=[]),l.push(d))})}finally{}l!==void 0&&fg(l,e)}if(!a){let l=Ir(o)||(()=>new o);e({provide:o,useFactory:l,deps:Ke},o),e({provide:lg,useValue:o,multi:!0},o),e({provide:Do,useValue:()=>k(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=t;Yu(c,d=>{e(d,l)})}}else return!1;return o!==t&&t.providers!==void 0}function Yu(t,e){for(let r of t)tg(r)&&(r=r.\u0275providers),Array.isArray(r)?Yu(r,e):e(r)}var VC=J({provide:String,useValue:J});function hg(t){return t!==null&&typeof t=="object"&&VC in t}function BC(t){return!!(t&&t.useExisting)}function UC(t){return!!(t&&t.useFactory)}function Cr(t){return typeof t=="function"}function HC(t){return!!t.useClass}var Js=new N(""),Ds={},rp={},Pl;function ea(){return Pl===void 0&&(Pl=new _s),Pl}var se=class{},Io=class extends se{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(e,r,n,o){super(),this.parent=r,this.source=n,this.scopes=o,eu(e,s=>this.processProvider(s)),this.records.set(cg,hr(void 0,this)),o.has("environment")&&this.records.set(se,hr(void 0,this));let i=this.records.get(Js);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(lg,Ke,$.Self))}retrieve(e,r){let n=r;return this.get(e,n.optional?ds:wn,n)}destroy(){po(this),this._destroyed=!0;let e=Z(null);try{for(let n of this._ngOnDestroyHooks)n.ngOnDestroy();let r=this._onDestroyHooks;this._onDestroyHooks=[];for(let n of r)n()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),Z(e)}}onDestroy(e){return po(this),this._onDestroyHooks.push(e),()=>this.removeOnDestroy(e)}runInContext(e){po(this);let r=Ft(this),n=Qe(void 0),o;try{return e()}finally{Ft(r),Qe(n)}}get(e,r=wn,n=$.Default){if(po(this),e.hasOwnProperty(tp))return e[tp](this);n=Ks(n);let o,i=Ft(this),s=Qe(void 0);try{if(!(n&$.SkipSelf)){let c=this.records.get(e);if(c===void 0){let l=qC(e)&&Qs(e);l&&this.injectableDefInScope(l)?c=hr(Jl(e),Ds):c=null,this.records.set(e,c)}if(c!=null)return this.hydrate(e,c)}let a=n&$.Self?ea():this.parent;return r=n&$.Optional&&r===wn?null:r,a.get(e,r)}catch(a){if(a.name==="NullInjectorError"){if((a[Ss]=a[Ss]||[]).unshift(ke(e)),i)throw a;return SC(a,e,"R3InjectorError",this.source)}else throw a}finally{Qe(s),Ft(i)}}resolveInjectorInitializers(){let e=Z(null),r=Ft(this),n=Qe(void 0),o;try{let i=this.get(Do,Ke,$.Self);for(let s of i)s()}finally{Ft(r),Qe(n),Z(e)}}toString(){let e=[],r=this.records;for(let n of r.keys())e.push(ke(n));return`R3Injector[${e.join(", ")}]`}processProvider(e){e=Me(e);let r=Cr(e)?e:Me(e&&e.provide),n=zC(e);if(!Cr(e)&&e.multi===!0){let o=this.records.get(r);o||(o=hr(void 0,Ds,!0),o.factory=()=>Kl(o.multi),this.records.set(r,o)),r=e,o.multi.push(e)}this.records.set(r,n)}hydrate(e,r){let n=Z(null);try{return r.value===rp?ng(ke(e)):r.value===Ds&&(r.value=rp,r.value=r.factory()),typeof r.value=="object"&&r.value&&WC(r.value)&&this._ngOnDestroyHooks.add(r.value),r.value}finally{Z(n)}}injectableDefInScope(e){if(!e.providedIn)return!1;let r=Me(e.providedIn);return typeof r=="string"?r==="any"||this.scopes.has(r):this.injectorDefTypes.has(r)}removeOnDestroy(e){let r=this._onDestroyHooks.indexOf(e);r!==-1&&this._onDestroyHooks.splice(r,1)}};function Jl(t){let e=Qs(t),r=e!==null?e.factory:Ir(t);if(r!==null)return r;if(t instanceof N)throw new R(204,!1);if(t instanceof Function)return $C(t);throw new R(204,!1)}function $C(t){if(t.length>0)throw new R(204,!1);let r=fC(t);return r!==null?()=>r.factory(t):()=>new t}function zC(t){if(hg(t))return hr(void 0,t.useValue);{let e=pg(t);return hr(e,Ds)}}function pg(t,e,r){let n;if(Cr(t)){let o=Me(t);return Ir(o)||Jl(o)}else if(hg(t))n=()=>Me(t.useValue);else if(UC(t))n=()=>t.useFactory(...Kl(t.deps||[]));else if(BC(t))n=()=>k(Me(t.useExisting));else{let o=Me(t&&(t.useClass||t.provide));if(GC(t))n=()=>new o(...Kl(t.deps));else return Ir(o)||Jl(o)}return n}function po(t){if(t.destroyed)throw new R(205,!1)}function hr(t,e,r=!1){return{factory:t,value:e,multi:r?[]:void 0}}function GC(t){return!!t.deps}function WC(t){return t!==null&&typeof t=="object"&&typeof t.ngOnDestroy=="function"}function qC(t){return typeof t=="function"||typeof t=="object"&&t instanceof N}function eu(t,e){for(let r of t)Array.isArray(r)?eu(r,e):r&&tg(r)?eu(r.\u0275providers,e):e(r)}function je(t,e){let r;t instanceof Io?(po(t),r=t):r=new Ms(t);let n,o=Ft(r),i=Qe(void 0);try{return e()}finally{Ft(o),Qe(i)}}function gg(){return rg()!==void 0||fo()!=null}function ZC(t){if(!gg())throw new R(-203,!1)}function tu(t){let e=Pe.ng;if(e&&e.\u0275compilerFacade)return e.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}var op={\u0275\u0275defineInjectable:A,\u0275\u0275defineInjector:Te,\u0275\u0275inject:k,\u0275\u0275invalidFactoryDep:wC,resolveForwardRef:Me},YC=Function;function go(t){return typeof t=="function"}var QC=/^function\s+\S+\(\)\s*{[\s\S]+\.apply\(this,\s*(arguments|(?:[^()]+\(\[\],)?[^()]+\(arguments\).*)\)/,KC=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{/,XC=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(/,JC=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(\)\s*{[^}]*super\(\.\.\.arguments\)/;function e0(t){return QC.test(t)||JC.test(t)||KC.test(t)&&!XC.test(t)}var nu=class{_reflect;constructor(e){this._reflect=e||Pe.Reflect}factory(e){return(...r)=>new e(...r)}_zipTypesAndAnnotations(e,r){let n;typeof e>"u"?n=ys(r.length):n=ys(e.length);for(let o=0;o<n.length;o++)typeof e>"u"?n[o]=[]:e[o]&&e[o]!=Object?n[o]=[e[o]]:n[o]=[],r&&r[o]!=null&&(n[o]=n[o].concat(r[o]));return n}_ownParameters(e,r){let n=e.toString();if(e0(n))return null;if(e.parameters&&e.parameters!==r.parameters)return e.parameters;let o=e.ctorParameters;if(o&&o!==r.ctorParameters){let a=typeof o=="function"?o():o,c=a.map(d=>d&&d.type),l=a.map(d=>d&&Fl(d.decorators));return this._zipTypesAndAnnotations(c,l)}let i=e.hasOwnProperty(fr)&&e[fr],s=this._reflect&&this._reflect.getOwnMetadata&&this._reflect.getOwnMetadata("design:paramtypes",e);return s||i?this._zipTypesAndAnnotations(s,i):ys(e.length)}parameters(e){if(!go(e))return[];let r=fs(e),n=this._ownParameters(e,r);return!n&&r!==Object&&(n=this.parameters(r)),n||[]}_ownAnnotations(e,r){if(e.annotations&&e.annotations!==r.annotations){let n=e.annotations;return typeof n=="function"&&n.annotations&&(n=n.annotations),n}return e.decorators&&e.decorators!==r.decorators?Fl(e.decorators):e.hasOwnProperty(dr)?e[dr]:null}annotations(e){if(!go(e))return[];let r=fs(e),n=this._ownAnnotations(e,r)||[];return(r!==Object?this.annotations(r):[]).concat(n)}_ownPropMetadata(e,r){if(e.propMetadata&&e.propMetadata!==r.propMetadata){let n=e.propMetadata;return typeof n=="function"&&n.propMetadata&&(n=n.propMetadata),n}if(e.propDecorators&&e.propDecorators!==r.propDecorators){let n=e.propDecorators,o={};return Object.keys(n).forEach(i=>{o[i]=Fl(n[i])}),o}return e.hasOwnProperty(Qh)?e[Qh]:null}propMetadata(e){if(!go(e))return{};let r=fs(e),n={};if(r!==Object){let i=this.propMetadata(r);Object.keys(i).forEach(s=>{n[s]=i[s]})}let o=this._ownPropMetadata(e,r);return o&&Object.keys(o).forEach(i=>{let s=[];n.hasOwnProperty(i)&&s.push(...n[i]),s.push(...o[i]),n[i]=s}),n}ownPropMetadata(e){return go(e)?this._ownPropMetadata(e,fs(e))||{}:{}}hasLifecycleHook(e,r){return e instanceof YC&&r in e.prototype}};function Fl(t){return t?t.map(e=>{let n=e.type.annotationCls,o=e.args?e.args:[];return new n(...o)}):[]}function fs(t){let e=t.prototype?Object.getPrototypeOf(t.prototype):null;return(e?e.constructor:null)||Object}var Vt=0,W=1,V=2,Se=3,st=4,Le=5,Co=6,vr=7,Fe=8,br=9,jt=10,de=11,bo=12,ip=13,Nr=14,Je=15,xn=16,pr=17,at=18,ta=19,mg=20,Xt=21,jl=22,xs=23,Xe=24,Jt=25,et=26,vg=1;var An=7,As=8,Er=9,He=10;function en(t){return Array.isArray(t)&&typeof t[vg]=="object"}function Bt(t){return Array.isArray(t)&&t[vg]===!0}function Qu(t){return(t.flags&4)!==0}function Or(t){return t.componentOffset>-1}function na(t){return(t.flags&1)===1}function vt(t){return!!t.template}function Rs(t){return(t[V]&512)!==0}function _o(t){return(t[V]&256)===256}var ru=class{previousValue;currentValue;firstChange;constructor(e,r,n){this.previousValue=e,this.currentValue=r,this.firstChange=n}isFirstChange(){return this.firstChange}};function yg(t,e,r,n){e!==null?e.applyValueToInputSignal(e,n):t[r]=n}var tt=(()=>{let t=()=>Dg;return t.ngInherit=!0,t})();function Dg(t){return t.type.prototype.ngOnChanges&&(t.setInput=n0),t0}function t0(){let t=Cg(this),e=t==null?void 0:t.current;if(e){let r=t.previous;if(r===_n)t.previous=e;else for(let n in e)r[n]=e[n];t.current=null,this.ngOnChanges(e)}}function n0(t,e,r,n,o){let i=this.declaredInputs[n],s=Cg(t)||r0(t,{previous:_n,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new ru(l&&l.currentValue,r,c===_n),yg(t,e,o,r)}var Ig="__ngSimpleChanges__";function Cg(t){return t[Ig]||null}function r0(t,e){return t[Ig]=e}var sp=null;var oe=function(t,e=null,r){sp!=null&&sp(t,e,r)},o0="svg",i0="math";function yt(t){for(;Array.isArray(t);)t=t[Vt];return t}function bg(t,e){return yt(e[t])}function wt(t,e){return yt(e[t.index])}function Eg(t,e){return t.data[e]}function Dt(t,e){let r=e[t];return en(r)?r:r[Vt]}function s0(t){return(t[V]&4)===4}function Ku(t){return(t[V]&128)===128}function a0(t){return Bt(t[Se])}function wr(t,e){return e==null?null:t[e]}function wg(t){t[pr]=0}function Mg(t){t[V]&1024||(t[V]|=1024,Ku(t)&&xo(t))}function c0(t,e){for(;t>0;)e=e[Nr],t--;return e}function ra(t){var e;return!!(t[V]&9216||(e=t[Xe])!=null&&e.dirty)}function ou(t){var e;(e=t[jt].changeDetectionScheduler)==null||e.notify(8),t[V]&64&&(t[V]|=1024),ra(t)&&xo(t)}function xo(t){var r;(r=t[jt].changeDetectionScheduler)==null||r.notify(0);let e=Rn(t);for(;e!==null&&!(e[V]&8192||(e[V]|=8192,!Ku(e)));)e=Rn(e)}function Sg(t,e){if(_o(t))throw new R(911,!1);t[Xt]===null&&(t[Xt]=[]),t[Xt].push(e)}function l0(t,e){if(t[Xt]===null)return;let r=t[Xt].indexOf(e);r!==-1&&t[Xt].splice(r,1)}function Rn(t){let e=t[Se];return Bt(e)?e[Se]:e}function Xu(t){var e;return(e=t[vr])!=null?e:t[vr]=[]}function Ju(t){var e;return(e=t.cleanup)!=null?e:t.cleanup=[]}function u0(t,e,r,n){let o=Xu(e);o.push(r),t.firstCreatePass&&Ju(t).push(n,o.length-1)}var z={lFrame:kg(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var iu=!1;function d0(){return z.lFrame.elementDepthCount}function f0(){z.lFrame.elementDepthCount++}function h0(){z.lFrame.elementDepthCount--}function ed(){return z.bindingsEnabled}function Tg(){return z.skipHydrationRootTNode!==null}function p0(t){return z.skipHydrationRootTNode===t}function g0(){z.skipHydrationRootTNode=null}function Y(){return z.lFrame.lView}function ge(){return z.lFrame.tView}function td(t){return z.lFrame.contextLView=t,t[Fe]}function nd(t){return z.lFrame.contextLView=null,t}function _e(){let t=_g();for(;t!==null&&t.type===64;)t=t.parent;return t}function _g(){return z.lFrame.currentTNode}function m0(){let t=z.lFrame,e=t.currentTNode;return t.isParent?e:e.parent}function jn(t,e){let r=z.lFrame;r.currentTNode=t,r.isParent=e}function rd(){return z.lFrame.isParent}function od(){z.lFrame.isParent=!1}function xg(){return iu}function ap(t){let e=iu;return iu=t,e}function v0(){let t=z.lFrame,e=t.bindingRootIndex;return e===-1&&(e=t.bindingRootIndex=t.tView.bindingStartIndex),e}function y0(){return z.lFrame.bindingIndex}function D0(t){return z.lFrame.bindingIndex=t}function oa(){return z.lFrame.bindingIndex++}function Ag(t){let e=z.lFrame,r=e.bindingIndex;return e.bindingIndex=e.bindingIndex+t,r}function I0(){return z.lFrame.inI18n}function C0(t,e){let r=z.lFrame;r.bindingIndex=r.bindingRootIndex=t,su(e)}function b0(){return z.lFrame.currentDirectiveIndex}function su(t){z.lFrame.currentDirectiveIndex=t}function E0(t){let e=z.lFrame.currentDirectiveIndex;return e===-1?null:t[e]}function Rg(){return z.lFrame.currentQueryIndex}function id(t){z.lFrame.currentQueryIndex=t}function w0(t){let e=t[W];return e.type===2?e.declTNode:e.type===1?t[Le]:null}function Ng(t,e,r){if(r&$.SkipSelf){let o=e,i=t;for(;o=o.parent,o===null&&!(r&$.Host);)if(o=w0(i),o===null||(i=i[Nr],o.type&10))break;if(o===null)return!1;e=o,t=i}let n=z.lFrame=Og();return n.currentTNode=e,n.lView=t,!0}function sd(t){let e=Og(),r=t[W];z.lFrame=e,e.currentTNode=r.firstChild,e.lView=t,e.tView=r,e.contextLView=t,e.bindingIndex=r.bindingStartIndex,e.inI18n=!1}function Og(){let t=z.lFrame,e=t===null?null:t.child;return e===null?kg(t):e}function kg(t){let e={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:t,child:null,inI18n:!1};return t!==null&&(t.child=e),e}function Pg(){let t=z.lFrame;return z.lFrame=t.parent,t.currentTNode=null,t.lView=null,t}var Fg=Pg;function ad(){let t=Pg();t.isParent=!0,t.tView=null,t.selectedIndex=-1,t.contextLView=null,t.elementDepthCount=0,t.currentDirectiveIndex=-1,t.currentNamespace=null,t.bindingRootIndex=-1,t.bindingIndex=-1,t.currentQueryIndex=0}function M0(t){return(z.lFrame.contextLView=c0(t,z.lFrame.contextLView))[Fe]}function Ln(){return z.lFrame.selectedIndex}function Nn(t){z.lFrame.selectedIndex=t}function ia(){let t=z.lFrame;return Eg(t.tView,t.selectedIndex)}function S0(){return z.lFrame.currentNamespace}var jg=!0;function sa(){return jg}function aa(t){jg=t}function T0(t,e,r){var s,a,c,l,d;let{ngOnChanges:n,ngOnInit:o,ngDoCheck:i}=e.type.prototype;if(n){let h=Dg(e);((s=r.preOrderHooks)!=null?s:r.preOrderHooks=[]).push(t,h),((a=r.preOrderCheckHooks)!=null?a:r.preOrderCheckHooks=[]).push(t,h)}o&&((c=r.preOrderHooks)!=null?c:r.preOrderHooks=[]).push(0-t,o),i&&(((l=r.preOrderHooks)!=null?l:r.preOrderHooks=[]).push(t,i),((d=r.preOrderCheckHooks)!=null?d:r.preOrderCheckHooks=[]).push(t,i))}function cd(t,e){var r,n,o,i,s,a,c;for(let l=e.directiveStart,d=e.directiveEnd;l<d;l++){let g=t.data[l].type.prototype,{ngAfterContentInit:p,ngAfterContentChecked:v,ngAfterViewInit:D,ngAfterViewChecked:x,ngOnDestroy:O}=g;p&&((r=t.contentHooks)!=null?r:t.contentHooks=[]).push(-l,p),v&&(((n=t.contentHooks)!=null?n:t.contentHooks=[]).push(l,v),((o=t.contentCheckHooks)!=null?o:t.contentCheckHooks=[]).push(l,v)),D&&((i=t.viewHooks)!=null?i:t.viewHooks=[]).push(-l,D),x&&(((s=t.viewHooks)!=null?s:t.viewHooks=[]).push(l,x),((a=t.viewCheckHooks)!=null?a:t.viewCheckHooks=[]).push(l,x)),O!=null&&((c=t.destroyHooks)!=null?c:t.destroyHooks=[]).push(l,O)}}function Is(t,e,r){Lg(t,e,3,r)}function Cs(t,e,r,n){(t[V]&3)===r&&Lg(t,e,r,n)}function Ll(t,e){let r=t[V];(r&3)===e&&(r&=16383,r+=1,t[V]=r)}function Lg(t,e,r,n){let o=n!==void 0?t[pr]&65535:0,i=n!=null?n:-1,s=e.length-1,a=0;for(let c=o;c<s;c++)if(typeof e[c+1]=="number"){if(a=e[c],n!=null&&a>=n)break}else e[c]<0&&(t[pr]+=65536),(a<i||i==-1)&&(_0(t,r,e,c),t[pr]=(t[pr]&**********)+c+2),c++}function cp(t,e){oe(4,t,e);let r=Z(null);try{e.call(t)}finally{Z(r),oe(5,t,e)}}function _0(t,e,r,n){let o=r[n]<0,i=r[n+1],s=o?-r[n]:r[n],a=t[s];o?t[V]>>14<t[pr]>>16&&(t[V]&3)===e&&(t[V]+=16384,cp(a,i)):cp(a,i)}var yr=-1,On=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(e,r,n){this.factory=e,this.canSeeViewProviders=r,this.injectImpl=n}};function x0(t){return(t.flags&8)!==0}function A0(t){return(t.flags&16)!==0}function R0(t,e,r){let n=0;for(;n<r.length;){let o=r[n];if(typeof o=="number"){if(o!==0)break;n++;let i=r[n++],s=r[n++],a=r[n++];t.setAttribute(e,s,a,i)}else{let i=o,s=r[++n];N0(i)?t.setProperty(e,i,s):t.setAttribute(e,i,s),n++}}return n}function Vg(t){return t===3||t===4||t===6}function N0(t){return t.charCodeAt(0)===64}function Mr(t,e){if(!(e===null||e.length===0))if(t===null||t.length===0)t=e.slice();else{let r=-1;for(let n=0;n<e.length;n++){let o=e[n];typeof o=="number"?r=o:r===0||(r===-1||r===2?lp(t,r,o,null,e[++n]):lp(t,r,o,null,null))}}return t}function lp(t,e,r,n,o){let i=0,s=t.length;if(e===-1)s=-1;else for(;i<t.length;){let a=t[i++];if(typeof a=="number"){if(a===e){s=-1;break}else if(a>e){s=i-1;break}}}for(;i<t.length;){let a=t[i];if(typeof a=="number")break;if(a===r){o!==null&&(t[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(t.splice(s,0,e),i=s+1),t.splice(i++,0,r),o!==null&&t.splice(i++,0,o)}function Bg(t){return t!==yr}function Ns(t){return t&32767}function O0(t){return t>>16}function Os(t,e){let r=O0(t),n=e;for(;r>0;)n=n[Nr],r--;return n}var au=!0;function up(t){let e=au;return au=t,e}var k0=256,Ug=k0-1,Hg=5,P0=0,mt={};function F0(t,e,r){let n;typeof r=="string"?n=r.charCodeAt(0)||0:r.hasOwnProperty(vo)&&(n=r[vo]),n==null&&(n=r[vo]=P0++);let o=n&Ug,i=1<<o;e.data[t+(o>>Hg)]|=i}function ks(t,e){let r=$g(t,e);if(r!==-1)return r;let n=e[W];n.firstCreatePass&&(t.injectorIndex=e.length,Vl(n.data,t),Vl(e,null),Vl(n.blueprint,null));let o=ld(t,e),i=t.injectorIndex;if(Bg(o)){let s=Ns(o),a=Os(o,e),c=a[W].data;for(let l=0;l<8;l++)e[i+l]=a[s+l]|c[s+l]}return e[i+8]=o,i}function Vl(t,e){t.push(0,0,0,0,0,0,0,0,e)}function $g(t,e){return t.injectorIndex===-1||t.parent&&t.parent.injectorIndex===t.injectorIndex||e[t.injectorIndex+8]===null?-1:t.injectorIndex}function ld(t,e){if(t.parent&&t.parent.injectorIndex!==-1)return t.parent.injectorIndex;let r=0,n=null,o=e;for(;o!==null;){if(n=Zg(o),n===null)return yr;if(r++,o=o[Nr],n.injectorIndex!==-1)return n.injectorIndex|r<<16}return yr}function cu(t,e,r){F0(t,e,r)}function j0(t,e){if(e==="class")return t.classes;if(e==="style")return t.styles;let r=t.attrs;if(r){let n=r.length,o=0;for(;o<n;){let i=r[o];if(Vg(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<n&&typeof r[o]=="string";)o++;else{if(i===e)return r[o+1];o=o+2}}}return null}function zg(t,e,r){if(r&$.Optional||t!==void 0)return t;qu(e,"NodeInjector")}function Gg(t,e,r,n){if(r&$.Optional&&n===void 0&&(n=null),(r&($.Self|$.Host))===0){let o=t[br],i=Qe(void 0);try{return o?o.get(e,n,r&$.Optional):og(e,n,r&$.Optional)}finally{Qe(i)}}return zg(n,e,r)}function Wg(t,e,r,n=$.Default,o){if(t!==null){if(e[V]&2048&&!(n&$.Self)){let s=U0(t,e,r,n,mt);if(s!==mt)return s}let i=qg(t,e,r,n,mt);if(i!==mt)return i}return Gg(e,r,n,o)}function qg(t,e,r,n,o){let i=V0(r);if(typeof i=="function"){if(!Ng(e,t,n))return n&$.Host?zg(o,r,n):Gg(e,r,n,o);try{let s;if(s=i(n),s==null&&!(n&$.Optional))qu(r);else return s}finally{Fg()}}else if(typeof i=="number"){let s=null,a=$g(t,e),c=yr,l=n&$.Host?e[Je][Le]:null;for((a===-1||n&$.SkipSelf)&&(c=a===-1?ld(t,e):e[a+8],c===yr||!fp(n,!1)?a=-1:(s=e[W],a=Ns(c),e=Os(c,e)));a!==-1;){let d=e[W];if(dp(i,a,d.data)){let h=L0(a,e,r,s,n,l);if(h!==mt)return h}c=e[a+8],c!==yr&&fp(n,e[W].data[a+8]===l)&&dp(i,a,e)?(s=d,a=Ns(c),e=Os(c,e)):a=-1}}return o}function L0(t,e,r,n,o,i){let s=e[W],a=s.data[t+8],c=n==null?Or(a)&&au:n!=s&&(a.type&3)!==0,l=o&$.Host&&i===a,d=bs(a,s,r,c,l);return d!==null?Eo(e,s,d,a):mt}function bs(t,e,r,n,o){let i=t.providerIndexes,s=e.data,a=i&1048575,c=t.directiveStart,l=t.directiveEnd,d=i>>20,h=n?a:a+d,g=o?a+d:l;for(let p=h;p<g;p++){let v=s[p];if(p<c&&r===v||p>=c&&v.type===r)return p}if(o){let p=s[c];if(p&&vt(p)&&p.type===r)return c}return null}function Eo(t,e,r,n){let o=t[r],i=e.data;if(o instanceof On){let s=o;s.resolving&&ng(yC(i[r]));let a=up(s.canSeeViewProviders);s.resolving=!0;let c,l=s.injectImpl?Qe(s.injectImpl):null,d=Ng(t,n,$.Default);try{o=t[r]=s.factory(void 0,i,t,n),e.firstCreatePass&&r>=n.directiveStart&&T0(r,i[r],e)}finally{l!==null&&Qe(l),up(a),s.resolving=!1,Fg()}}return o}function V0(t){if(typeof t=="string")return t.charCodeAt(0)||0;let e=t.hasOwnProperty(vo)?t[vo]:void 0;return typeof e=="number"?e>=0?e&Ug:B0:e}function dp(t,e,r){let n=1<<t;return!!(r[e+(t>>Hg)]&n)}function fp(t,e){return!(t&$.Self)&&!(t&$.Host&&e)}var Mn=class{_tNode;_lView;constructor(e,r){this._tNode=e,this._lView=r}get(e,r,n){return Wg(this._tNode,this._lView,e,Ks(n),r)}};function B0(){return new Mn(_e(),Y())}function we(t){return Ar(()=>{let e=t.prototype.constructor,r=e[Dr]||lu(e),n=Object.prototype,o=Object.getPrototypeOf(t.prototype).constructor;for(;o&&o!==n;){let i=o[Dr]||lu(o);if(i&&i!==r)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function lu(t){return Xp(t)?()=>{let e=lu(Me(t));return e&&e()}:Ir(t)}function U0(t,e,r,n,o){let i=t,s=e;for(;i!==null&&s!==null&&s[V]&2048&&!Rs(s);){let a=qg(i,s,r,n|$.Self,mt);if(a!==mt)return a;let c=i.parent;if(!c){let l=s[mg];if(l){let d=l.get(r,mt,n);if(d!==mt)return d}c=Zg(s),s=s[Nr]}i=c}return o}function Zg(t){let e=t[W],r=e.type;return r===2?e.declTNode:r===1?t[Le]:null}function Mt(t){return j0(_e(),t)}var H0=Rr("Attribute",t=>({attributeName:t,__NG_ELEMENT_ID__:()=>Mt(t)})),hp=null;function $0(){return hp=hp||new nu}function z0(t){return Yg($0().parameters(t))}function Yg(t){return t.map(e=>G0(e))}function G0(t){let e={token:null,attribute:null,host:!1,optional:!1,self:!1,skipSelf:!1};if(Array.isArray(t)&&t.length>0)for(let r=0;r<t.length;r++){let n=t[r];if(n===void 0)continue;let o=Object.getPrototypeOf(n);if(n instanceof ig||o.ngMetadataName==="Optional")e.optional=!0;else if(n instanceof sg||o.ngMetadataName==="SkipSelf")e.skipSelf=!0;else if(n instanceof xC||o.ngMetadataName==="Self")e.self=!0;else if(n instanceof AC||o.ngMetadataName==="Host")e.host=!0;else if(n instanceof _C)e.token=n.token;else if(n instanceof H0){if(n.attributeName===void 0)throw new R(204,!1);e.attribute=n.attributeName}else e.token=n}else t===void 0||Array.isArray(t)&&t.length===0?e.token=null:e.token=t;return e}function W0(t,e){let r=null,n=null;t.hasOwnProperty(ws)||Object.defineProperty(t,ws,{get:()=>(r===null&&(r=tu({usage:0,kind:"injectable",type:t}).compileInjectable(op,`ng:///${t.name}/\u0275prov.js`,Q0(t,e))),r)}),t.hasOwnProperty(Dr)||Object.defineProperty(t,Dr,{get:()=>{if(n===null){let o=tu({usage:0,kind:"injectable",type:t});n=o.compileFactory(op,`ng:///${t.name}/\u0275fac.js`,{name:t.name,type:t,typeArgumentCount:0,deps:z0(t),target:o.FactoryTarget.Injectable})}return n},configurable:!0})}var q0=J({provide:String,useValue:J});function pp(t){return t.useClass!==void 0}function Z0(t){return q0 in t}function gp(t){return t.useFactory!==void 0}function Y0(t){return t.useExisting!==void 0}function Q0(t,e){let r=e||{providedIn:null},n={name:t.name,type:t,typeArgumentCount:0,providedIn:r.providedIn};return(pp(r)||gp(r))&&r.deps!==void 0&&(n.deps=Yg(r.deps)),pp(r)?n.useClass=r.useClass:Z0(r)?n.useValue=r.useValue:gp(r)?n.useFactory=r.useFactory:Y0(r)&&(n.useExisting=r.useExisting),n}var sV=lC("Injectable",void 0,void 0,void 0,(t,e)=>W0(t,e));function mp(t,e=null,r=null,n){let o=Qg(t,e,r,n);return o.resolveInjectorInitializers(),o}function Qg(t,e=null,r=null,n,o=new Set){let i=[r||Ke,LC(t)];return n=n||(typeof t=="object"?void 0:ke(t)),new Io(i,e||ea(),n||null,o)}var En=class En{static create(e,r){var n;if(Array.isArray(e))return mp({name:""},r,e,"");{let o=(n=e.name)!=null?n:"";return mp({name:o},e.parent,e.providers,o)}}};u(En,"THROW_IF_NOT_FOUND",wn),u(En,"NULL",new _s),u(En,"\u0275prov",A({token:En,providedIn:"any",factory:()=>k(cg)})),u(En,"__NG_ELEMENT_ID__",-1);var te=En;var K0=new N("");K0.__NG_ELEMENT_ID__=t=>{let e=_e();if(e===null)throw new R(204,!1);if(e.type&2)return e.value;if(t&$.Optional)return null;throw new R(204,!1)};var Kg=!1,Ao=(()=>{class t{}return u(t,"__NG_ELEMENT_ID__",X0),u(t,"__NG_ENV_ID__",r=>r),t})(),uu=class extends Ao{_lView;constructor(e){super(),this._lView=e}onDestroy(e){return Sg(this._lView,e),()=>l0(this._lView,e)}};function X0(){return new uu(Y())}var Sr=class{},Xg=new N("",{providedIn:"root",factory:()=>!1});var Jg=new N(""),em=new N(""),Ut=(()=>{let e=class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new pe(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>new e}));let t=e;return t})();var du=class extends ee{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(e=!1){var r,n;super(),this.__isAsync=e,gg()&&(this.destroyRef=(r=I(Ao,{optional:!0}))!=null?r:void 0,this.pendingTasks=(n=I(Ut,{optional:!0}))!=null?n:void 0)}emit(e){let r=Z(null);try{super.next(e)}finally{Z(r)}}subscribe(e,r,n){var c,l,d;let o=e,i=r||(()=>null),s=n;if(e&&typeof e=="object"){let h=e;o=(c=h.next)==null?void 0:c.bind(h),i=(l=h.error)==null?void 0:l.bind(h),s=(d=h.complete)==null?void 0:d.bind(h)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return e instanceof ue&&e.add(a),a}wrapInTimeout(e){return r=>{var o;let n=(o=this.pendingTasks)==null?void 0:o.add();setTimeout(()=>{var i;e(r),n!==void 0&&((i=this.pendingTasks)==null||i.remove(n))})}}},ie=du;function Ps(...t){}function tm(t){let e,r;function n(){t=Ps;try{r!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(r),e!==void 0&&clearTimeout(e)}catch{}}return e=setTimeout(()=>{t(),n()}),typeof requestAnimationFrame=="function"&&(r=requestAnimationFrame(()=>{t(),n()})),()=>n()}function vp(t){return queueMicrotask(()=>t()),()=>{t=Ps}}var ud="isAngularZone",Fs=ud+"_ID",J0=0,m=class t{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ie(!1);onMicrotaskEmpty=new ie(!1);onStable=new ie(!1);onError=new ie(!1);constructor(e){let{enableLongStackTrace:r=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Kg}=e;if(typeof Zone>"u")throw new R(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),r&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&n,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,nb(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(ud)===!0}static assertInAngularZone(){if(!t.isInAngularZone())throw new R(909,!1)}static assertNotInAngularZone(){if(t.isInAngularZone())throw new R(909,!1)}run(e,r,n){return this._inner.run(e,r,n)}runTask(e,r,n,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,e,eb,Ps,Ps);try{return i.runTask(s,r,n)}finally{i.cancelTask(s)}}runGuarded(e,r,n){return this._inner.runGuarded(e,r,n)}runOutsideAngular(e){return this._outer.run(e)}},eb={};function dd(t){if(t._nesting==0&&!t.hasPendingMicrotasks&&!t.isStable)try{t._nesting++,t.onMicrotaskEmpty.emit(null)}finally{if(t._nesting--,!t.hasPendingMicrotasks)try{t.runOutsideAngular(()=>t.onStable.emit(null))}finally{t.isStable=!0}}}function tb(t){if(t.isCheckStableRunning||t.callbackScheduled)return;t.callbackScheduled=!0;function e(){tm(()=>{t.callbackScheduled=!1,fu(t),t.isCheckStableRunning=!0,dd(t),t.isCheckStableRunning=!1})}t.scheduleInRootZone?Zone.root.run(()=>{e()}):t._outer.run(()=>{e()}),fu(t)}function nb(t){let e=()=>{tb(t)},r=J0++;t._inner=t._inner.fork({name:"angular",properties:{[ud]:!0,[Fs]:r,[Fs+r]:!0},onInvokeTask:(n,o,i,s,a,c)=>{if(rb(c))return n.invokeTask(i,s,a,c);try{return yp(t),n.invokeTask(i,s,a,c)}finally{(t.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||t.shouldCoalesceRunChangeDetection)&&e(),Dp(t)}},onInvoke:(n,o,i,s,a,c,l)=>{try{return yp(t),n.invoke(i,s,a,c,l)}finally{t.shouldCoalesceRunChangeDetection&&!t.callbackScheduled&&!ob(c)&&e(),Dp(t)}},onHasTask:(n,o,i,s)=>{n.hasTask(i,s),o===i&&(s.change=="microTask"?(t._hasPendingMicrotasks=s.microTask,fu(t),dd(t)):s.change=="macroTask"&&(t.hasPendingMacrotasks=s.macroTask))},onHandleError:(n,o,i,s)=>(n.handleError(i,s),t.runOutsideAngular(()=>t.onError.emit(s)),!1)})}function fu(t){t._hasPendingMicrotasks||(t.shouldCoalesceEventChangeDetection||t.shouldCoalesceRunChangeDetection)&&t.callbackScheduled===!0?t.hasPendingMicrotasks=!0:t.hasPendingMicrotasks=!1}function yp(t){t._nesting++,t.isStable&&(t.isStable=!1,t.onUnstable.emit(null))}function Dp(t){t._nesting--,dd(t)}var js=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ie;onMicrotaskEmpty=new ie;onStable=new ie;onError=new ie;run(e,r,n){return e.apply(r,n)}runGuarded(e,r,n){return e.apply(r,n)}runOutsideAngular(e){return e()}runTask(e,r,n,o){return e.apply(r,n)}};function rb(t){return nm(t,"__ignore_ng_zone__")}function ob(t){return nm(t,"__scheduler_tick__")}function nm(t,e){var r,n;return!Array.isArray(t)||t.length!==1?!1:((n=(r=t[0])==null?void 0:r.data)==null?void 0:n[e])===!0}function ib(t="zone.js",e){return t==="noop"?new js:t==="zone.js"?new m(e):t}var It=class{_console=console;handleError(e){this._console.error("ERROR",e)}},sb=new N("",{providedIn:"root",factory:()=>{let t=I(m),e=I(It);return r=>t.runOutsideAngular(()=>e.handleError(r))}});function Ip(t,e){return Qp(t,e)}function ab(t){return Qp(Yp,t)}var rm=(Ip.required=ab,Ip);function cb(){return kr(_e(),Y())}function kr(t,e){return new y(wt(t,e))}var y=(()=>{class t{nativeElement;constructor(r){this.nativeElement=r}}return u(t,"__NG_ELEMENT_ID__",cb),t})();function lb(t){return t instanceof y?t.nativeElement:t}function ub(t){return typeof t=="function"&&t[Ye]!==void 0}function Ro(t,e){let r=Al(t,e==null?void 0:e.equal),n=r[Ye];return r.set=o=>uo(n,o),r.update=o=>Rl(n,o),r.asReadonly=db.bind(r),r}function db(){let t=this[Ye];if(t.readonlyFn===void 0){let e=()=>this();e[Ye]=t,t.readonlyFn=e}return t.readonlyFn}function om(t){return ub(t)&&typeof t.set=="function"}function fb(){return this._results[Symbol.iterator]()}var hu=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){var e;return(e=this._changes)!=null?e:this._changes=new ee}constructor(e=!1){this._emitDistinctChangesOnly=e}get(e){return this._results[e]}map(e){return this._results.map(e)}filter(e){return this._results.filter(e)}find(e){return this._results.find(e)}reduce(e,r){return this._results.reduce(e,r)}forEach(e){this._results.forEach(e)}some(e){return this._results.some(e)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(e,r){this.dirty=!1;let n=NC(e);(this._changesDetected=!RC(this._results,n,r))&&(this._results=n,this.length=n.length,this.last=n[this.length-1],this.first=n[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(e){this._onDirty=e}setDirty(){var e;this.dirty=!0,(e=this._onDirty)==null||e.call(this)}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=fb};function im(t){return(t.flags&128)===128}var sm=function(t){return t[t.OnPush=0]="OnPush",t[t.Default=1]="Default",t}(sm||{}),am=new Map,hb=0;function pb(){return hb++}function gb(t){am.set(t[ta],t)}function pu(t){am.delete(t[ta])}var Cp="__ngContext__";function Pr(t,e){en(e)?(t[Cp]=e[ta],gb(e)):t[Cp]=e}function cm(t){return um(t[bo])}function lm(t){return um(t[st])}function um(t){for(;t!==null&&!Bt(t);)t=t[st];return t}var gu;function dm(t){gu=t}function fm(){if(gu!==void 0)return gu;if(typeof document<"u")return document;throw new R(210,!1)}var fd=new N("",{providedIn:"root",factory:()=>mb}),mb="ng",hd=new N(""),nn=new N("",{providedIn:"platform",factory:()=>"unknown"});var pd=new N("",{providedIn:"root",factory:()=>{var t,e;return((e=(t=fm().body)==null?void 0:t.querySelector("[ngCspNonce]"))==null?void 0:e.getAttribute("ngCspNonce"))||null}});var vb="h",yb="b";var hm=!1,Db=new N("",{providedIn:"root",factory:()=>hm});var gd=function(t){return t[t.CHANGE_DETECTION=0]="CHANGE_DETECTION",t[t.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",t}(gd||{}),Fr=new N(""),bp=new Set;function No(t){var e;bp.has(t)||(bp.add(t),(e=performance==null?void 0:performance.mark)==null||e.call(performance,"mark_feature_usage",{detail:{feature:t}}))}var pm=(()=>{class t{view;node;constructor(r,n){this.view=r,this.node=n}}return u(t,"__NG_ELEMENT_ID__",Ib),t})();function Ib(){return new pm(Y(),_e())}var gr=function(t){return t[t.EarlyRead=0]="EarlyRead",t[t.Write=1]="Write",t[t.MixedReadWrite=2]="MixedReadWrite",t[t.Read=3]="Read",t}(gr||{}),gm=(()=>{let e=class e{impl=null;execute(){var n;(n=this.impl)==null||n.execute()}};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>new e}));let t=e;return t})(),Cb=[gr.EarlyRead,gr.Write,gr.MixedReadWrite,gr.Read],bb=(()=>{let e=class e{ngZone=I(m);scheduler=I(Sr);errorHandler=I(It,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){I(Fr,{optional:!0})}execute(){var o;let n=this.sequences.size>0;n&&oe(16),this.executing=!0;for(let i of Cb)for(let s of this.sequences)if(!(s.erroredOrDestroyed||!s.hooks[i]))try{s.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let a=s.hooks[i];return a(s.pipelinedValue)},s.snapshot))}catch(a){s.erroredOrDestroyed=!0,(o=this.errorHandler)==null||o.handleError(a)}this.executing=!1;for(let i of this.sequences)i.afterRun(),i.once&&(this.sequences.delete(i),i.destroy());for(let i of this.deferredRegistrations)this.sequences.add(i);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&oe(17)}register(n){var i;let{view:o}=n;o!==void 0?(((i=o[Jt])!=null?i:o[Jt]=[]).push(n),xo(o),o[V]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,o){return o?o.run(gd.AFTER_NEXT_RENDER,n):n()}};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>new e}));let t=e;return t})(),mu=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(e,r,n,o,i,s=null){this.impl=e,this.hooks=r,this.view=n,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i==null?void 0:i.onDestroy(()=>this.destroy())}afterRun(){var e;this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,(e=this.snapshot)==null||e.dispose(),this.snapshot=null}destroy(){var r,n;this.impl.unregister(this),(r=this.unregisterOnDestroy)==null||r.call(this);let e=(n=this.view)==null?void 0:n[Jt];e&&(this.view[Jt]=e.filter(o=>o!==this))}};function md(t,e){var n;!(e!=null&&e.injector)&&ZC(md);let r=(n=e==null?void 0:e.injector)!=null?n:I(te);return No("NgAfterNextRender"),wb(t,r,e,!0)}function Eb(t,e){if(t instanceof Function){let r=[void 0,void 0,void 0,void 0];return r[e]=t,r}else return[t.earlyRead,t.write,t.mixedReadWrite,t.read]}function wb(t,e,r,n){var d,h;let o=e.get(gm);(d=o.impl)!=null||(o.impl=e.get(bb));let i=e.get(Fr,null,{optional:!0}),s=(h=r==null?void 0:r.phase)!=null?h:gr.MixedReadWrite,a=(r==null?void 0:r.manualCleanup)!==!0?e.get(Ao):null,c=e.get(pm,null,{optional:!0}),l=new mu(o.impl,Eb(t,s),c==null?void 0:c.view,n,a,i==null?void 0:i.snapshot(null));return o.impl.register(l),l}var Mb=()=>null;function mm(t,e,r=!1){return Mb(t,e,r)}function vm(t,e){let r=t.contentQueries;if(r!==null){let n=Z(null);try{for(let o=0;o<r.length;o+=2){let i=r[o],s=r[o+1];if(s!==-1){let a=t.data[s];id(i),a.contentQueries(2,e[s],s)}}}finally{Z(n)}}}function vu(t,e,r){id(0);let n=Z(null);try{e(t,r)}finally{Z(n)}}function vd(t,e,r){if(Qu(e)){let n=Z(null);try{let o=e.directiveStart,i=e.directiveEnd;for(let s=o;s<i;s++){let a=t.data[s];if(a.contentQueries){let c=r[s];a.contentQueries(1,c,s)}}}finally{Z(n)}}}var Ct=function(t){return t[t.Emulated=0]="Emulated",t[t.None=2]="None",t[t.ShadowDom=3]="ShadowDom",t}(Ct||{}),hs;function Sb(){if(hs===void 0&&(hs=null,Pe.trustedTypes))try{hs=Pe.trustedTypes.createPolicy("angular",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch{}return hs}function ca(t){var e;return((e=Sb())==null?void 0:e.createHTML(t))||t}var ps;function ym(){if(ps===void 0&&(ps=null,Pe.trustedTypes))try{ps=Pe.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch{}return ps}function Ep(t){var e;return((e=ym())==null?void 0:e.createHTML(t))||t}function wp(t){var e;return((e=ym())==null?void 0:e.createScriptURL(t))||t}var Ls=class{changingThisBreaksApplicationSecurity;constructor(e){this.changingThisBreaksApplicationSecurity=e}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${qp})`}};function Oo(t){return t instanceof Ls?t.changingThisBreaksApplicationSecurity:t}function yd(t,e){let r=Tb(t);if(r!=null&&r!==e){if(r==="ResourceURL"&&e==="URL")return!0;throw new Error(`Required a safe ${e}, got a ${r} (see ${qp})`)}return r===e}function Tb(t){return t instanceof Ls&&t.getTypeName()||null}function _b(t){let e=new Du(t);return xb()?new yu(e):e}var yu=class{inertDocumentHelper;constructor(e){this.inertDocumentHelper=e}getInertBodyElement(e){var r;e="<body><remove></remove>"+e;try{let n=new window.DOMParser().parseFromString(ca(e),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(e):((r=n.firstChild)==null||r.remove(),n)}catch{return null}}},Du=class{defaultDoc;inertDocument;constructor(e){this.defaultDoc=e,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(e){let r=this.inertDocument.createElement("template");return r.innerHTML=ca(e),r}};function xb(){try{return!!new window.DOMParser().parseFromString(ca(""),"text/html")}catch{return!1}}var Ab=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Dm(t){return t=String(t),t.match(Ab)?t:"unsafe:"+t}function Ht(t){let e={};for(let r of t.split(","))e[r]=!0;return e}function ko(...t){let e={};for(let r of t)for(let n in r)r.hasOwnProperty(n)&&(e[n]=!0);return e}var Im=Ht("area,br,col,hr,img,wbr"),Cm=Ht("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),bm=Ht("rp,rt"),Rb=ko(bm,Cm),Nb=ko(Cm,Ht("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Ob=ko(bm,Ht("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Mp=ko(Im,Nb,Ob,Rb),Em=Ht("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),kb=Ht("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Pb=Ht("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Fb=ko(Em,kb,Pb),jb=Ht("script,style,template"),Iu=class{sanitizedSomething=!1;buf=[];sanitizeChildren(e){let r=e.firstChild,n=!0,o=[];for(;r;){if(r.nodeType===Node.ELEMENT_NODE?n=this.startElement(r):r.nodeType===Node.TEXT_NODE?this.chars(r.nodeValue):this.sanitizedSomething=!0,n&&r.firstChild){o.push(r),r=Bb(r);continue}for(;r;){r.nodeType===Node.ELEMENT_NODE&&this.endElement(r);let i=Vb(r);if(i){r=i;break}r=o.pop()}}return this.buf.join("")}startElement(e){let r=Sp(e).toLowerCase();if(!Mp.hasOwnProperty(r))return this.sanitizedSomething=!0,!jb.hasOwnProperty(r);this.buf.push("<"),this.buf.push(r);let n=e.attributes;for(let o=0;o<n.length;o++){let i=n.item(o),s=i.name,a=s.toLowerCase();if(!Fb.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Em[a]&&(c=Dm(c)),this.buf.push(" ",s,'="',Tp(c),'"')}return this.buf.push(">"),!0}endElement(e){let r=Sp(e).toLowerCase();Mp.hasOwnProperty(r)&&!Im.hasOwnProperty(r)&&(this.buf.push("</"),this.buf.push(r),this.buf.push(">"))}chars(e){this.buf.push(Tp(e))}};function Lb(t,e){return(t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Vb(t){let e=t.nextSibling;if(e&&t!==e.previousSibling)throw wm(e);return e}function Bb(t){let e=t.firstChild;if(e&&Lb(t,e))throw wm(e);return e}function Sp(t){let e=t.nodeName;return typeof e=="string"?e:"FORM"}function wm(t){return new Error(`Failed to sanitize html because the element is clobbered: ${t.outerHTML}`)}var Ub=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Hb=/([^\#-~ |!])/g;function Tp(t){return t.replace(/&/g,"&amp;").replace(Ub,function(e){let r=e.charCodeAt(0),n=e.charCodeAt(1);return"&#"+((r-55296)*1024+(n-56320)+65536)+";"}).replace(Hb,function(e){return"&#"+e.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var gs;function $b(t,e){let r=null;try{gs=gs||_b(t);let n=e?String(e):"";r=gs.getInertBodyElement(n);let o=5,i=n;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,n=i,i=r.innerHTML,r=gs.getInertBodyElement(n)}while(n!==i);let a=new Iu().sanitizeChildren(_p(r)||r);return ca(a)}finally{if(r){let n=_p(r)||r;for(;n.firstChild;)n.firstChild.remove()}}}function _p(t){return"content"in t&&zb(t)?t.content:null}function zb(t){return t.nodeType===Node.ELEMENT_NODE&&t.nodeName==="TEMPLATE"}var la=function(t){return t[t.NONE=0]="NONE",t[t.HTML=1]="HTML",t[t.STYLE=2]="STYLE",t[t.SCRIPT=3]="SCRIPT",t[t.URL=4]="URL",t[t.RESOURCE_URL=5]="RESOURCE_URL",t}(la||{});function aV(t){let e=Dd();return e?Ep(e.sanitize(la.HTML,t)||""):yd(t,"HTML")?Ep(Oo(t)):$b(fm(),Tn(t))}function Gb(t){let e=Dd();return e?e.sanitize(la.URL,t)||"":yd(t,"URL")?Oo(t):Dm(Tn(t))}function Wb(t){let e=Dd();if(e)return wp(e.sanitize(la.RESOURCE_URL,t)||"");if(yd(t,"ResourceURL"))return wp(Oo(t));throw new R(904,!1)}function qb(t,e){return e==="src"&&(t==="embed"||t==="frame"||t==="iframe"||t==="media"||t==="script")||e==="href"&&(t==="base"||t==="link")?Wb:Gb}function Mm(t,e,r){return qb(e,r)(t)}function Dd(){let t=Y();return t&&t[jt].sanitizer}var Zb=/^>|^->|<!--|-->|--!>|<!-$/g,Yb=/(<|>)/g,Qb="\u200B$1\u200B";function Kb(t){return t.replace(Zb,e=>e.replace(Yb,Qb))}function Sm(t){return t instanceof Function?t():t}function Xb(t,e,r){let n=t.length;for(;;){let o=t.indexOf(e,r);if(o===-1)return o;if(o===0||t.charCodeAt(o-1)<=32){let i=e.length;if(o+i===n||t.charCodeAt(o+i)<=32)return o}r=o+1}}var Tm="ng-template";function Jb(t,e,r,n){let o=0;if(n){for(;o<e.length&&typeof e[o]=="string";o+=2)if(e[o]==="class"&&Xb(e[o+1].toLowerCase(),r,0)!==-1)return!0}else if(Id(t))return!1;if(o=e.indexOf(1,o),o>-1){let i;for(;++o<e.length&&typeof(i=e[o])=="string";)if(i.toLowerCase()===r)return!0}return!1}function Id(t){return t.type===4&&t.value!==Tm}function eE(t,e,r){let n=t.type===4&&!r?Tm:t.value;return e===n}function tE(t,e,r){let n=4,o=t.attrs,i=o!==null?oE(o):0,s=!1;for(let a=0;a<e.length;a++){let c=e[a];if(typeof c=="number"){if(!s&&!it(n)&&!it(c))return!1;if(s&&it(c))continue;s=!1,n=c|n&1;continue}if(!s)if(n&4){if(n=2|n&1,c!==""&&!eE(t,c,r)||c===""&&e.length===1){if(it(n))return!1;s=!0}}else if(n&8){if(o===null||!Jb(t,o,c,r)){if(it(n))return!1;s=!0}}else{let l=e[++a],d=nE(c,o,Id(t),r);if(d===-1){if(it(n))return!1;s=!0;continue}if(l!==""){let h;if(d>i?h="":h=o[d+1].toLowerCase(),n&2&&l!==h){if(it(n))return!1;s=!0}}}}return it(n)||s}function it(t){return(t&1)===0}function nE(t,e,r,n){if(e===null)return-1;let o=0;if(n||!r){let i=!1;for(;o<e.length;){let s=e[o];if(s===t)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=e[++o];for(;typeof a=="string";)a=e[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return iE(e,t)}function _m(t,e,r=!1){for(let n=0;n<e.length;n++)if(tE(t,e[n],r))return!0;return!1}function rE(t){let e=t.attrs;if(e!=null){let r=e.indexOf(5);if((r&1)===0)return e[r+1]}return null}function oE(t){for(let e=0;e<t.length;e++){let r=t[e];if(Vg(r))return e}return t.length}function iE(t,e){let r=t.indexOf(4);if(r>-1)for(r++;r<t.length;){let n=t[r];if(typeof n=="number")return-1;if(n===e)return r;r++}return-1}function sE(t,e){e:for(let r=0;r<e.length;r++){let n=e[r];if(t.length===n.length){for(let o=0;o<t.length;o++)if(t[o]!==n[o])continue e;return!0}}return!1}function xp(t,e){return t?":not("+e.trim()+")":e}function aE(t){let e=t[0],r=1,n=2,o="",i=!1;for(;r<t.length;){let s=t[r];if(typeof s=="string")if(n&2){let a=t[++r];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else n&8?o+="."+s:n&4&&(o+=" "+s);else o!==""&&!it(s)&&(e+=xp(i,o),o=""),n=s,i=i||!it(n);r++}return o!==""&&(e+=xp(i,o)),e}function cE(t){return t.map(aE).join(",")}function lE(t){let e=[],r=[],n=1,o=2;for(;n<t.length;){let i=t[n];if(typeof i=="string")o===2?i!==""&&e.push(i,t[++n]):o===8&&r.push(i);else{if(!it(o))break;o=i}n++}return r.length&&e.push(1,...r),e}var St={};function uE(t,e){return t.createText(e)}function dE(t,e,r){t.setValue(e,r)}function fE(t,e){return t.createComment(Kb(e))}function xm(t,e,r){return t.createElement(e,r)}function Vs(t,e,r,n,o){t.insertBefore(e,r,n,o)}function Am(t,e,r){t.appendChild(e,r)}function Ap(t,e,r,n,o){n!==null?Vs(t,e,r,n,o):Am(t,e,r)}function hE(t,e,r){t.removeChild(null,e,r)}function pE(t,e,r){t.setAttribute(e,"style",r)}function gE(t,e,r){r===""?t.removeAttribute(e,"class"):t.setAttribute(e,"class",r)}function Rm(t,e,r){let{mergedAttrs:n,classes:o,styles:i}=r;n!==null&&R0(t,e,n),o!==null&&gE(t,e,o),i!==null&&pE(t,e,i)}function Cd(t,e,r,n,o,i,s,a,c,l,d){let h=et+n,g=h+o,p=mE(h,g),v=typeof l=="function"?l():l;return p[W]={type:t,blueprint:p,template:r,queries:null,viewQuery:a,declTNode:e,data:p.slice().fill(null,h),bindingStartIndex:h,expandoStartIndex:g,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:v,incompleteFirstPass:!1,ssrId:d}}function mE(t,e){let r=[];for(let n=0;n<e;n++)r.push(n<t?null:St);return r}function vE(t){let e=t.tView;return e===null||e.incompleteFirstPass?t.tView=Cd(1,null,t.template,t.decls,t.vars,t.directiveDefs,t.pipeDefs,t.viewQuery,t.schemas,t.consts,t.id):e}function bd(t,e,r,n,o,i,s,a,c,l,d){let h=e.blueprint.slice();return h[Vt]=o,h[V]=n|4|128|8|64|1024,(l!==null||t&&t[V]&2048)&&(h[V]|=2048),wg(h),h[Se]=h[Nr]=t,h[Fe]=r,h[jt]=s||t&&t[jt],h[de]=a||t&&t[de],h[br]=c||t&&t[br]||null,h[Le]=i,h[ta]=pb(),h[Co]=d,h[mg]=l,h[Je]=e.type==2?t[Je]:h,h}function yE(t,e,r){let n=wt(e,t),o=vE(r),i=t[jt].rendererFactory,s=Ed(t,bd(t,o,null,Nm(r),n,e,null,i.createRenderer(n,r),null,null,null));return t[e.index]=s}function Nm(t){let e=16;return t.signals?e=4096:t.onPush&&(e=64),e}function Om(t,e,r,n){if(r===0)return-1;let o=e.length;for(let i=0;i<r;i++)e.push(n),t.blueprint.push(n),t.data.push(null);return o}function Ed(t,e){return t[bo]?t[ip][st]=e:t[bo]=e,t[ip]=e,e}function ua(t=1){km(ge(),Y(),Ln()+t,!1)}function km(t,e,r,n){if(!n)if((e[V]&3)===3){let i=t.preOrderCheckHooks;i!==null&&Is(e,i,r)}else{let i=t.preOrderHooks;i!==null&&Cs(e,i,0,r)}Nn(r)}var da=function(t){return t[t.None=0]="None",t[t.SignalBased=1]="SignalBased",t[t.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",t}(da||{});function Cu(t,e,r,n){let o=Z(null);try{let[i,s,a]=t.inputs[r],c=null;(s&da.SignalBased)!==0&&(c=e[i][Ye]),c!==null&&c.transformFn!==void 0?n=c.transformFn(n):a!==null&&(n=a.call(e,n)),t.setInput!==null?t.setInput(e,c,n,r,i):yg(e,c,i,n)}finally{Z(o)}}function Pm(t,e,r,n,o){let i=Ln(),s=n&2;try{Nn(-1),s&&e.length>et&&km(t,e,et,!1),oe(s?2:0,o),r(n,o)}finally{Nn(i),oe(s?3:1,o)}}function fa(t,e,r){wE(t,e,r),(r.flags&64)===64&&ME(t,e,r)}function wd(t,e,r=wt){let n=e.localNames;if(n!==null){let o=e.index+1;for(let i=0;i<n.length;i+=2){let s=n[i+1],a=s===-1?r(e,t):t[s];t[o++]=a}}}function DE(t,e,r,n){let i=n.get(Db,hm)||r===Ct.ShadowDom,s=t.selectRootElement(e,i);return IE(s),s}function IE(t){CE(t)}var CE=()=>null;function bE(t){return t==="class"?"className":t==="for"?"htmlFor":t==="formaction"?"formAction":t==="innerHtml"?"innerHTML":t==="readonly"?"readOnly":t==="tabindex"?"tabIndex":t}function Md(t,e,r,n,o,i,s,a){if(!a&&Td(e,t,r,n,o)){Or(e)&&EE(r,e.index);return}if(e.type&3){let c=wt(e,r);n=bE(n),o=s!=null?s(o,e.value||"",n):o,i.setProperty(c,n,o)}else e.type&12}function EE(t,e){let r=Dt(e,t);r[V]&16||(r[V]|=64)}function wE(t,e,r){let n=r.directiveStart,o=r.directiveEnd;Or(r)&&yE(e,r,t.data[n+r.componentOffset]),t.firstCreatePass||ks(r,e);let i=r.initialInputs;for(let s=n;s<o;s++){let a=t.data[s],c=Eo(e,t,s,r);if(Pr(c,e),i!==null&&xE(e,s-n,c,a,r,i),vt(a)){let l=Dt(r.index,e);l[Fe]=Eo(e,t,s,r)}}}function ME(t,e,r){let n=r.directiveStart,o=r.directiveEnd,i=r.index,s=b0();try{Nn(i);for(let a=n;a<o;a++){let c=t.data[a],l=e[a];su(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&SE(c,l)}}finally{Nn(-1),su(s)}}function SE(t,e){t.hostBindings!==null&&t.hostBindings(1,e)}function Sd(t,e){let r=t.directiveRegistry,n=null;if(r)for(let o=0;o<r.length;o++){let i=r[o];_m(e,i.selectors,!1)&&(n!=null||(n=[]),vt(i)?n.unshift(i):n.push(i))}return n}function TE(t,e,r,n,o,i){let s=wt(t,e);_E(e[de],s,i,t.value,r,n,o)}function _E(t,e,r,n,o,i,s){if(i==null)t.removeAttribute(e,o,r);else{let a=s==null?Tn(i):s(i,n||"",o);t.setAttribute(e,o,a,r)}}function xE(t,e,r,n,o,i){let s=i[e];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];Cu(n,r,c,l)}}function AE(t,e){let r=t[br],n=r?r.get(It,null):null;n&&n.handleError(e)}function Td(t,e,r,n,o){var c,l;let i=(c=t.inputs)==null?void 0:c[n],s=(l=t.hostDirectiveInputs)==null?void 0:l[n],a=!1;if(s)for(let d=0;d<s.length;d+=2){let h=s[d],g=s[d+1],p=e.data[h];Cu(p,r[h],g,o),a=!0}if(i)for(let d of i){let h=r[d],g=e.data[d];Cu(g,h,n,o),a=!0}return a}function RE(t,e){let r=Dt(e,t),n=r[W];NE(n,r);let o=r[Vt];o!==null&&r[Co]===null&&(r[Co]=mm(o,r[br])),oe(18),_d(n,r,r[Fe]),oe(19,r[Fe])}function NE(t,e){for(let r=e.length;r<t.blueprint.length;r++)e.push(t.blueprint[r])}function _d(t,e,r){var n;sd(e);try{let o=t.viewQuery;o!==null&&vu(1,o,r);let i=t.template;i!==null&&Pm(t,e,i,1,r),t.firstCreatePass&&(t.firstCreatePass=!1),(n=e[at])==null||n.finishViewCreation(t),t.staticContentQueries&&vm(t,e),t.staticViewQueries&&vu(2,t.viewQuery,r);let s=t.components;s!==null&&OE(e,s)}catch(o){throw t.firstCreatePass&&(t.incompleteFirstPass=!0,t.firstCreatePass=!1),o}finally{e[V]&=-5,ad()}}function OE(t,e){for(let r=0;r<e.length;r++)RE(t,e[r])}function Fm(t,e,r,n){var i,s,a;let o=Z(null);try{let c=e.tView,d=t[V]&4096?4096:16,h=bd(t,c,r,d,null,e,null,null,(i=n==null?void 0:n.injector)!=null?i:null,(s=n==null?void 0:n.embeddedViewInjector)!=null?s:null,(a=n==null?void 0:n.dehydratedView)!=null?a:null),g=t[e.index];h[xn]=g;let p=t[at];return p!==null&&(h[at]=p.createEmbeddedView(c)),_d(c,h,r),h}finally{Z(o)}}function bu(t,e){return!e||e.firstChild===null||im(t)}var kE;function xd(t,e){return kE(t,e)}var Lt=function(t){return t[t.Important=1]="Important",t[t.DashCase=2]="DashCase",t}(Lt||{});function Ad(t){return(t.flags&32)===32}function mr(t,e,r,n,o){if(n!=null){let i,s=!1;Bt(n)?i=n:en(n)&&(s=!0,n=n[Vt]);let a=yt(n);t===0&&r!==null?o==null?Am(e,r,a):Vs(e,r,a,o||null,!0):t===1&&r!==null?Vs(e,r,a,o||null,!0):t===2?hE(e,a,s):t===3&&e.destroyNode(a),i!=null&&zE(e,t,i,r,o)}}function PE(t,e){jm(t,e),e[Vt]=null,e[Le]=null}function FE(t,e,r,n,o,i){n[Vt]=o,n[Le]=e,pa(t,n,r,1,o,i)}function jm(t,e){var r;(r=e[jt].changeDetectionScheduler)==null||r.notify(9),pa(t,e,e[de],2,null,null)}function jE(t){let e=t[bo];if(!e)return Bl(t[W],t);for(;e;){let r=null;if(en(e))r=e[bo];else{let n=e[He];n&&(r=n)}if(!r){for(;e&&!e[st]&&e!==t;)en(e)&&Bl(e[W],e),e=e[Se];e===null&&(e=t),en(e)&&Bl(e[W],e),r=e&&e[st]}e=r}}function Rd(t,e){let r=t[Er],n=r.indexOf(e);r.splice(n,1)}function Lm(t,e){if(_o(e))return;let r=e[de];r.destroyNode&&pa(t,e,r,3,null,null),jE(e)}function Bl(t,e){if(_o(e))return;let r=Z(null);try{e[V]&=-129,e[V]|=256,e[Xe]&&Tl(e[Xe]),VE(t,e),LE(t,e),e[W].type===1&&e[de].destroy();let n=e[xn];if(n!==null&&Bt(e[Se])){n!==e[Se]&&Rd(n,e);let o=e[at];o!==null&&o.detachView(t)}pu(e)}finally{Z(r)}}function LE(t,e){let r=t.cleanup,n=e[vr];if(r!==null)for(let s=0;s<r.length-1;s+=2)if(typeof r[s]=="string"){let a=r[s+3];a>=0?n[a]():n[-a].unsubscribe(),s+=2}else{let a=n[r[s+1]];r[s].call(a)}n!==null&&(e[vr]=null);let o=e[Xt];if(o!==null){e[Xt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=e[xs];if(i!==null){e[xs]=null;for(let s of i)s.destroy()}}function VE(t,e){let r;if(t!=null&&(r=t.destroyHooks)!=null)for(let n=0;n<r.length;n+=2){let o=e[r[n]];if(!(o instanceof On)){let i=r[n+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];oe(4,a,c);try{c.call(a)}finally{oe(5,a,c)}}else{oe(4,o,i);try{i.call(o)}finally{oe(5,o,i)}}}}}function Vm(t,e,r){return BE(t,e.parent,r)}function BE(t,e,r){let n=e;for(;n!==null&&n.type&168;)e=n,n=e.parent;if(n===null)return r[Vt];if(Or(n)){let{encapsulation:o}=t.data[n.directiveStart+n.componentOffset];if(o===Ct.None||o===Ct.Emulated)return null}return wt(n,r)}function Bm(t,e,r){return HE(t,e,r)}function UE(t,e,r){return t.type&40?wt(t,r):null}var HE=UE,Rp;function ha(t,e,r,n){let o=Vm(t,n,e),i=e[de],s=n.parent||e[Le],a=Bm(s,n,e);if(o!=null)if(Array.isArray(r))for(let c=0;c<r.length;c++)Ap(i,o,r[c],a,!1);else Ap(i,o,r,a,!1);Rp!==void 0&&Rp(i,n,e,r,o)}function mo(t,e){if(e!==null){let r=e.type;if(r&3)return wt(e,t);if(r&4)return Eu(-1,t[e.index]);if(r&8){let n=e.child;if(n!==null)return mo(t,n);{let o=t[e.index];return Bt(o)?Eu(-1,o):yt(o)}}else{if(r&128)return mo(t,e.next);if(r&32)return xd(e,t)()||yt(t[e.index]);{let n=Um(t,e);if(n!==null){if(Array.isArray(n))return n[0];let o=Rn(t[Je]);return mo(o,n)}else return mo(t,e.next)}}}return null}function Um(t,e){if(e!==null){let n=t[Je][Le],o=e.projection;return n.projection[o]}return null}function Eu(t,e){let r=He+t+1;if(r<e.length){let n=e[r],o=n[W].firstChild;if(o!==null)return mo(n,o)}return e[An]}function Nd(t,e,r,n,o,i,s){for(;r!=null;){if(r.type===128){r=r.next;continue}let a=n[r.index],c=r.type;if(s&&e===0&&(a&&Pr(yt(a),n),r.flags|=2),!Ad(r))if(c&8)Nd(t,e,r.child,n,o,i,!1),mr(e,t,o,a,i);else if(c&32){let l=xd(r,n),d;for(;d=l();)mr(e,t,o,d,i);mr(e,t,o,a,i)}else c&16?Hm(t,e,n,r,o,i):mr(e,t,o,a,i);r=s?r.projectionNext:r.next}}function pa(t,e,r,n,o,i){Nd(r,n,t.firstChild,e,o,i,!1)}function $E(t,e,r){let n=e[de],o=Vm(t,r,e),i=r.parent||e[Le],s=Bm(i,r,e);Hm(n,0,e,r,o,s)}function Hm(t,e,r,n,o,i){let s=r[Je],c=s[Le].projection[n.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let d=c[l];mr(e,t,o,d,i)}else{let l=c,d=s[Se];im(n)&&(l.flags|=128),Nd(t,e,l,d,o,i,!0)}}function zE(t,e,r,n,o){let i=r[An],s=yt(r);i!==s&&mr(e,t,n,i,o);for(let a=He;a<r.length;a++){let c=r[a];pa(c[W],c,t,e,n,i)}}function GE(t,e,r,n,o){if(e)o?t.addClass(r,n):t.removeClass(r,n);else{let i=n.indexOf("-")===-1?void 0:Lt.DashCase;o==null?t.removeStyle(r,n,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Lt.Important),t.setStyle(r,n,o,i))}}function Bs(t,e,r,n,o=!1){for(;r!==null;){if(r.type===128){r=o?r.projectionNext:r.next;continue}let i=e[r.index];i!==null&&n.push(yt(i)),Bt(i)&&WE(i,n);let s=r.type;if(s&8)Bs(t,e,r.child,n);else if(s&32){let a=xd(r,e),c;for(;c=a();)n.push(c)}else if(s&16){let a=Um(e,r);if(Array.isArray(a))n.push(...a);else{let c=Rn(e[Je]);Bs(c[W],c,a,n,!0)}}r=o?r.projectionNext:r.next}return n}function WE(t,e){for(let r=He;r<t.length;r++){let n=t[r],o=n[W].firstChild;o!==null&&Bs(n[W],n,o,e)}t[An]!==t[Vt]&&e.push(t[An])}function $m(t){if(t[Jt]!==null){for(let e of t[Jt])e.impl.addSequence(e);t[Jt].length=0}}var zm=[];function qE(t){var e;return(e=t[Xe])!=null?e:ZE(t)}function ZE(t){var r;let e=(r=zm.pop())!=null?r:Object.create(QE);return e.lView=t,e}function YE(t){t.lView[Xe]!==t&&(t.lView=null,zm.push(t))}var QE=L(b({},co),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:t=>{xo(t.lView)},consumerOnSignalRead(){this.lView[Xe]=this}});function KE(t){var r;let e=(r=t[Xe])!=null?r:Object.create(XE);return e.lView=t,e}var XE=L(b({},co),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:t=>{let e=Rn(t.lView);for(;e&&!Gm(e[W]);)e=Rn(e);e&&Mg(e)},consumerOnSignalRead(){this.lView[Xe]=this}});function Gm(t){return t.type!==2}function Wm(t){if(t[xs]===null)return;let e=!0;for(;e;){let r=!1;for(let n of t[xs])n.dirty&&(r=!0,n.zone===null||Zone.current===n.zone?n.run():n.zone.run(()=>n.run()));e=r&&!!(t[V]&8192)}}var JE=100;function qm(t,e=!0,r=0){var s,a;let o=t[jt].rendererFactory,i=!1;i||(s=o.begin)==null||s.call(o);try{ew(t,r)}catch(c){throw e&&AE(t,c),c}finally{i||(a=o.end)==null||a.call(o)}}function ew(t,e){let r=xg();try{ap(!0),wu(t,e);let n=0;for(;ra(t);){if(n===JE)throw new R(103,!1);n++,wu(t,1)}}finally{ap(r)}}function tw(t,e,r,n){if(_o(e))return;let o=e[V],i=!1,s=!1;sd(e);let a=!0,c=null,l=null;i||(Gm(t)?(l=qE(e),c=as(l)):Cl()===null?(a=!1,l=KE(e),c=as(l)):e[Xe]&&(Tl(e[Xe]),e[Xe]=null));try{wg(e),D0(t.bindingStartIndex),r!==null&&Pm(t,e,r,2,n);let d=(o&3)===3;if(!i)if(d){let p=t.preOrderCheckHooks;p!==null&&Is(e,p,null)}else{let p=t.preOrderHooks;p!==null&&Cs(e,p,0,null),Ll(e,0)}if(s||nw(e),Wm(e),Zm(e,0),t.contentQueries!==null&&vm(t,e),!i)if(d){let p=t.contentCheckHooks;p!==null&&Is(e,p)}else{let p=t.contentHooks;p!==null&&Cs(e,p,1),Ll(e,1)}ow(t,e);let h=t.components;h!==null&&Qm(e,h,0);let g=t.viewQuery;if(g!==null&&vu(2,g,n),!i)if(d){let p=t.viewCheckHooks;p!==null&&Is(e,p)}else{let p=t.viewHooks;p!==null&&Cs(e,p,2),Ll(e,2)}if(t.firstUpdatePass===!0&&(t.firstUpdatePass=!1),e[jl]){for(let p of e[jl])p();e[jl]=null}i||($m(e),e[V]&=-73)}catch(d){throw i||xo(e),d}finally{l!==null&&(Ml(l,c),a&&YE(l)),ad()}}function Zm(t,e){for(let r=cm(t);r!==null;r=lm(r))for(let n=He;n<r.length;n++){let o=r[n];Ym(o,e)}}function nw(t){for(let e=cm(t);e!==null;e=lm(e)){if(!(e[V]&2))continue;let r=e[Er];for(let n=0;n<r.length;n++){let o=r[n];Mg(o)}}}function rw(t,e,r){oe(18);let n=Dt(e,t);Ym(n,r),oe(19,n[Fe])}function Ym(t,e){Ku(t)&&wu(t,e)}function wu(t,e){let n=t[W],o=t[V],i=t[Xe],s=!!(e===0&&o&16);if(s||(s=!!(o&64&&e===0)),s||(s=!!(o&1024)),s||(s=!!(i!=null&&i.dirty&&Sl(i))),s||(s=!1),i&&(i.dirty=!1),t[V]&=-9217,s)tw(n,t,n.template,t[Fe]);else if(o&8192){Wm(t),Zm(t,1);let a=n.components;a!==null&&Qm(t,a,1),$m(t)}}function Qm(t,e,r){for(let n=0;n<e.length;n++)rw(t,e[n],r)}function ow(t,e){let r=t.hostBindingOpCodes;if(r!==null)try{for(let n=0;n<r.length;n++){let o=r[n];if(o<0)Nn(~o);else{let i=o,s=r[++n],a=r[++n];C0(s,i);let c=e[i];oe(24,c),a(2,c),oe(25,c)}}}finally{Nn(-1)}}function Od(t,e){var n;let r=xg()?64:1088;for((n=t[jt].changeDetectionScheduler)==null||n.notify(e);t;){t[V]|=r;let o=Rn(t);if(Rs(t)&&!o)return t;t=o}return null}function Km(t,e,r,n){return[t,!0,0,e,null,n,null,r,null,null]}function Xm(t,e,r,n=!0){let o=e[W];if(iw(o,e,t,r),n){let s=Eu(r,t),a=e[de],c=a.parentNode(t[An]);c!==null&&FE(o,t[Le],a,e,c,s)}let i=e[Co];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Mu(t,e){if(t.length<=He)return;let r=He+e,n=t[r];if(n){let o=n[xn];o!==null&&o!==t&&Rd(o,n),e>0&&(t[r-1][st]=n[st]);let i=Ts(t,He+e);PE(n[W],n);let s=i[at];s!==null&&s.detachView(i[W]),n[Se]=null,n[st]=null,n[V]&=-129}return n}function iw(t,e,r,n){let o=He+n,i=r.length;n>0&&(r[o-1][st]=e),n<i-He?(e[st]=r[o],ag(r,He+n,e)):(r.push(e),e[st]=null),e[Se]=r;let s=e[xn];s!==null&&r!==s&&Jm(s,e);let a=e[at];a!==null&&a.insertView(t),ou(e),e[V]|=128}function Jm(t,e){let r=t[Er],n=e[Se];if(en(n))t[V]|=2;else{let o=n[Se][Je];e[Je]!==o&&(t[V]|=2)}r===null?t[Er]=[e]:r.push(e)}var wo=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let e=this._lView,r=e[W];return Bs(r,e,r.firstChild,[])}constructor(e,r,n=!0){this._lView=e,this._cdRefInjectingView=r,this.notifyErrorHandler=n}get context(){return this._lView[Fe]}set context(e){this._lView[Fe]=e}get destroyed(){return _o(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let e=this._lView[Se];if(Bt(e)){let r=e[As],n=r?r.indexOf(this):-1;n>-1&&(Mu(e,n),Ts(r,n))}this._attachedToViewContainer=!1}Lm(this._lView[W],this._lView)}onDestroy(e){Sg(this._lView,e)}markForCheck(){Od(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[V]&=-129}reattach(){ou(this._lView),this._lView[V]|=128}detectChanges(){this._lView[V]|=1024,qm(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new R(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let e=Rs(this._lView),r=this._lView[xn];r!==null&&!e&&Rd(r,this._lView),jm(this._lView[W],this._lView)}attachToAppRef(e){if(this._attachedToViewContainer)throw new R(902,!1);this._appRef=e;let r=Rs(this._lView),n=this._lView[xn];n!==null&&!r&&Jm(n,this._lView),ou(this._lView)}};var bt=(()=>{class t{}return u(t,"__NG_ELEMENT_ID__",cw),t})(),sw=bt,aw=class extends sw{_declarationLView;_declarationTContainer;elementRef;constructor(e,r,n){super(),this._declarationLView=e,this._declarationTContainer=r,this.elementRef=n}get ssrId(){var e;return((e=this._declarationTContainer.tView)==null?void 0:e.ssrId)||null}createEmbeddedView(e,r){return this.createEmbeddedViewImpl(e,r)}createEmbeddedViewImpl(e,r,n){let o=Fm(this._declarationLView,this._declarationTContainer,e,{embeddedViewInjector:r,dehydratedView:n});return new wo(o)}};function cw(){return kd(_e(),Y())}function kd(t,e){return t.type&4?new aw(e,t,kr(t,e)):null}function Po(t,e,r,n,o){let i=t.data[e];if(i===null)i=lw(t,e,r,n,o),I0()&&(i.flags|=32);else if(i.type&64){i.type=r,i.value=n,i.attrs=o;let s=m0();i.injectorIndex=s===null?-1:s.injectorIndex}return jn(i,!0),i}function lw(t,e,r,n,o){let i=_g(),s=rd(),a=s?i:i&&i.parent,c=t.data[e]=dw(t,a,r,e,n,o);return uw(t,c,i,s),c}function uw(t,e,r,n){t.firstChild===null&&(t.firstChild=e),r!==null&&(n?r.child==null&&e.parent!==null&&(r.child=e):r.next===null&&(r.next=e,e.prev=r))}function dw(t,e,r,n,o,i){let s=e?e.injectorIndex:-1,a=0;return Tg()&&(a|=128),{type:r,index:n,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:e,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var uV=new RegExp(`^(\\d+)*(${yb}|${vb})*(.*)`);var fw=()=>null;function Su(t,e){return fw(t,e)}var hw=class{},ev=class{},Tu=class{resolveComponentFactory(e){throw Error(`No component factory found for ${ke(e)}.`)}},Zl,ga=(Zl=class{},u(Zl,"NULL",new Tu),Zl),Tr=class{},rn=(()=>{class t{destroyNode=null}return u(t,"__NG_ELEMENT_ID__",()=>pw()),t})();function pw(){let t=Y(),e=_e(),r=Dt(e.index,t);return(en(r)?r:t)[de]}var gw=(()=>{let e=class e{};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>null}));let t=e;return t})();var Ul={},_u=class{injector;parentInjector;constructor(e,r){this.injector=e,this.parentInjector=r}get(e,r,n){n=Ks(n);let o=this.injector.get(e,Ul,n);return o!==Ul||r===Ul?o:this.parentInjector.get(e,r,n)}};function xu(t,e,r){let n=r?t.styles:null,o=r?t.classes:null,i=0;if(e!==null)for(let s=0;s<e.length;s++){let a=e[s];if(typeof a=="number")i=a;else if(i==1)o=Kh(o,a);else if(i==2){let c=a,l=e[++s];n=Kh(n,c+": "+l+";")}}r?t.styles=n:t.stylesWithoutHost=n,r?t.classes=o:t.classesWithoutHost=o}function f(t,e=$.Default){let r=Y();if(r===null)return k(t,e);let n=_e();return Wg(n,r,Me(t),e)}function tv(){let t="invalid";throw new Error(t)}function Pd(t,e,r,n,o){let i=n===null?null:{"":-1},s=o(t,r);if(s!==null){let a,c=null,l=null,d=vw(s);d===null?a=s:[a,c,l]=d,Iw(t,e,r,a,i,c,l)}i!==null&&n!==null&&mw(r,n,i)}function mw(t,e,r){let n=t.localNames=[];for(let o=0;o<e.length;o+=2){let i=r[e[o+1]];if(i==null)throw new R(-301,!1);n.push(e[o],i)}}function vw(t){let e=null,r=!1;for(let s=0;s<t.length;s++){let a=t[s];if(s===0&&vt(a)&&(e=a),a.findHostDirectiveDefs!==null){r=!0;break}}if(!r)return null;let n=null,o=null,i=null;for(let s of t)s.findHostDirectiveDefs!==null&&(n!=null||(n=[]),o!=null||(o=new Map),i!=null||(i=new Map),yw(s,n,i,o)),s===e&&(n!=null||(n=[]),n.push(s));return n!==null?(n.push(...e===null?t:t.slice(1)),[n,o,i]):null}function yw(t,e,r,n){let o=e.length;t.findHostDirectiveDefs(t,e,n),r.set(t,[o,e.length-1])}function Dw(t,e,r){var n;e.componentOffset=r,((n=t.components)!=null?n:t.components=[]).push(e.index)}function Iw(t,e,r,n,o,i,s){var g,p;let a=n.length,c=!1;for(let v=0;v<a;v++){let D=n[v];!c&&vt(D)&&(c=!0,Dw(t,r,v)),cu(ks(r,e),t,D.type)}Sw(r,t.data.length,a);for(let v=0;v<a;v++){let D=n[v];D.providersResolver&&D.providersResolver(D)}let l=!1,d=!1,h=Om(t,e,a,null);a>0&&(r.directiveToIndex=new Map);for(let v=0;v<a;v++){let D=n[v];if(r.mergedAttrs=Mr(r.mergedAttrs,D.hostAttrs),bw(t,r,e,h,D),Mw(h,D,o),s!==null&&s.has(D)){let[O,X]=s.get(D);r.directiveToIndex.set(D.type,[h,O+r.directiveStart,X+r.directiveStart])}else(i===null||!i.has(D))&&r.directiveToIndex.set(D.type,h);D.contentQueries!==null&&(r.flags|=4),(D.hostBindings!==null||D.hostAttrs!==null||D.hostVars!==0)&&(r.flags|=64);let x=D.type.prototype;!l&&(x.ngOnChanges||x.ngOnInit||x.ngDoCheck)&&(((g=t.preOrderHooks)!=null?g:t.preOrderHooks=[]).push(r.index),l=!0),!d&&(x.ngOnChanges||x.ngDoCheck)&&(((p=t.preOrderCheckHooks)!=null?p:t.preOrderCheckHooks=[]).push(r.index),d=!0),h++}Cw(t,r,i)}function Cw(t,e,r){for(let n=e.directiveStart;n<e.directiveEnd;n++){let o=t.data[n];if(r===null||!r.has(o))Np(0,e,o,n),Np(1,e,o,n),kp(e,n,!1);else{let i=r.get(o);Op(0,e,i,n),Op(1,e,i,n),kp(e,n,!0)}}}function Np(t,e,r,n){var i,s,a;let o=t===0?r.inputs:r.outputs;for(let c in o)if(o.hasOwnProperty(c)){let l;t===0?l=(i=e.inputs)!=null?i:e.inputs={}:l=(s=e.outputs)!=null?s:e.outputs={},(a=l[c])!=null||(l[c]=[]),l[c].push(n),nv(e,c)}}function Op(t,e,r,n){var i,s,a;let o=t===0?r.inputs:r.outputs;for(let c in o)if(o.hasOwnProperty(c)){let l=o[c],d;t===0?d=(i=e.hostDirectiveInputs)!=null?i:e.hostDirectiveInputs={}:d=(s=e.hostDirectiveOutputs)!=null?s:e.hostDirectiveOutputs={},(a=d[l])!=null||(d[l]=[]),d[l].push(n,c),nv(e,l)}}function nv(t,e){e==="class"?t.flags|=8:e==="style"&&(t.flags|=16)}function kp(t,e,r){var c,l;let{attrs:n,inputs:o,hostDirectiveInputs:i}=t;if(n===null||!r&&o===null||r&&i===null||Id(t)){(c=t.initialInputs)!=null||(t.initialInputs=[]),t.initialInputs.push(null);return}let s=null,a=0;for(;a<n.length;){let d=n[a];if(d===0){a+=4;continue}else if(d===5){a+=2;continue}else if(typeof d=="number")break;if(!r&&o.hasOwnProperty(d)){let h=o[d];for(let g of h)if(g===e){s!=null||(s=[]),s.push(d,n[a+1]);break}}else if(r&&i.hasOwnProperty(d)){let h=i[d];for(let g=0;g<h.length;g+=2)if(h[g]===e){s!=null||(s=[]),s.push(h[g+1],n[a+1]);break}}a+=2}(l=t.initialInputs)!=null||(t.initialInputs=[]),t.initialInputs.push(s)}function bw(t,e,r,n,o){t.data[n]=o;let i=o.factory||(o.factory=Ir(o.type,!0)),s=new On(i,vt(o),f);t.blueprint[n]=s,r[n]=s,Ew(t,e,n,Om(t,r,o.hostVars,St),o)}function Ew(t,e,r,n,o){let i=o.hostBindings;if(i){let s=t.hostBindingOpCodes;s===null&&(s=t.hostBindingOpCodes=[]);let a=~e.index;ww(s)!=a&&s.push(a),s.push(r,n,i)}}function ww(t){let e=t.length;for(;e>0;){let r=t[--e];if(typeof r=="number"&&r<0)return r}return 0}function Mw(t,e,r){if(r){if(e.exportAs)for(let n=0;n<e.exportAs.length;n++)r[e.exportAs[n]]=t;vt(e)&&(r[""]=t)}}function Sw(t,e,r){t.flags|=1,t.directiveStart=e,t.directiveEnd=e+r,t.providerIndexes=e}function rv(t,e,r,n,o,i,s,a){let c=e.consts,l=wr(c,s),d=Po(e,t,2,n,l);return i&&Pd(e,r,d,wr(c,a),o),d.mergedAttrs=Mr(d.mergedAttrs,d.attrs),d.attrs!==null&&xu(d,d.attrs,!1),d.mergedAttrs!==null&&xu(d,d.mergedAttrs,!0),e.queries!==null&&e.queries.elementStart(e,d),d}function ov(t,e){cd(t,e),Qu(e)&&t.queries.elementEnd(e)}var Us=class extends ga{ngModule;constructor(e){super(),this.ngModule=e}resolveComponentFactory(e){let r=tn(e);return new kn(r,this.ngModule)}};function Tw(t){return Object.keys(t).map(e=>{let[r,n,o]=t[e],i={propName:r,templateName:e,isSignal:(n&da.SignalBased)!==0};return o&&(i.transform=o),i})}function _w(t){return Object.keys(t).map(e=>({propName:t[e],templateName:e}))}function xw(t,e,r){let n=e instanceof se?e:e==null?void 0:e.injector;return n&&t.getStandaloneInjector!==null&&(n=t.getStandaloneInjector(n)||n),n?new _u(r,n):r}function Aw(t){let e=t.get(Tr,null);if(e===null)throw new R(407,!1);let r=t.get(gw,null),n=t.get(Sr,null);return{rendererFactory:e,sanitizer:r,changeDetectionScheduler:n}}function Rw(t,e){let r=(t.selectors[0][0]||"div").toLowerCase();return xm(e,r,r==="svg"?o0:r==="math"?i0:null)}var kn=class extends ev{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){var e;return(e=this.cachedInputs)!=null||(this.cachedInputs=Tw(this.componentDef.inputs)),this.cachedInputs}get outputs(){var e;return(e=this.cachedOutputs)!=null||(this.cachedOutputs=_w(this.componentDef.outputs)),this.cachedOutputs}constructor(e,r){var n;super(),this.componentDef=e,this.ngModule=r,this.componentType=e.type,this.selector=cE(e.selectors),this.ngContentSelectors=(n=e.ngContentSelectors)!=null?n:[],this.isBoundToModule=!!r}create(e,r,n,o){oe(22);let i=Z(null);try{let s=this.componentDef,a=n?["ng-version","19.2.7"]:lE(this.componentDef.selectors[0]),c=Cd(0,null,null,1,0,null,null,null,null,[a],null),l=xw(s,o||this.ngModule,e),d=Aw(l),h=d.rendererFactory.createRenderer(null,s),g=n?DE(h,n,s.encapsulation,l):Rw(s,h),p=bd(null,c,null,512|Nm(s),null,null,d,h,l,null,mm(g,l,!0));p[et]=g,sd(p);let v=null;try{let D=rv(et,c,p,"#host",()=>[this.componentDef],!0,0);g&&(Rm(h,g,D),Pr(g,p)),fa(c,p,D),vd(c,D,p),ov(c,D),r!==void 0&&Nw(D,this.ngContentSelectors,r),v=Dt(D.index,p),p[Fe]=v[Fe],_d(c,p,null)}catch(D){throw v!==null&&pu(v),pu(p),D}finally{oe(23),ad()}return new Au(this.componentType,p)}finally{Z(i)}}},Au=class extends hw{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(e,r){super(),this._rootLView=r,this._tNode=Eg(r[W],et),this.location=kr(this._tNode,r),this.instance=Dt(this._tNode.index,r)[Fe],this.hostView=this.changeDetectorRef=new wo(r,void 0,!1),this.componentType=e}setInput(e,r){var a;let n=this._tNode;if((a=this.previousInputValues)!=null||(this.previousInputValues=new Map),this.previousInputValues.has(e)&&Object.is(this.previousInputValues.get(e),r))return;let o=this._rootLView,i=Td(n,o[W],o,e,r);this.previousInputValues.set(e,r);let s=Dt(n.index,o);Od(s,1)}get injector(){return new Mn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(e){this.hostView.onDestroy(e)}};function Nw(t,e,r){let n=t.projection=[];for(let o=0;o<e.length;o++){let i=r[o];n.push(i!=null&&i.length?Array.from(i):null)}}var ze=(()=>{class t{}return u(t,"__NG_ELEMENT_ID__",Ow),t})();function Ow(){let t=_e();return sv(t,Y())}var kw=ze,iv=class extends kw{_lContainer;_hostTNode;_hostLView;constructor(e,r,n){super(),this._lContainer=e,this._hostTNode=r,this._hostLView=n}get element(){return kr(this._hostTNode,this._hostLView)}get injector(){return new Mn(this._hostTNode,this._hostLView)}get parentInjector(){let e=ld(this._hostTNode,this._hostLView);if(Bg(e)){let r=Os(e,this._hostLView),n=Ns(e),o=r[W].data[n+8];return new Mn(o,r)}else return new Mn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(e){let r=Pp(this._lContainer);return r!==null&&r[e]||null}get length(){return this._lContainer.length-He}createEmbeddedView(e,r,n){let o,i;typeof n=="number"?o=n:n!=null&&(o=n.index,i=n.injector);let s=Su(this._lContainer,e.ssrId),a=e.createEmbeddedViewImpl(r||{},i,s);return this.insertImpl(a,o,bu(this._hostTNode,s)),a}createComponent(e,r,n,o,i){var v,D,x;let s=e&&!go(e),a;if(s)a=r;else{let O=r||{};a=O.index,n=O.injector,o=O.projectableNodes,i=O.environmentInjector||O.ngModuleRef}let c=s?e:new kn(tn(e)),l=n||this.parentInjector;if(!i&&c.ngModule==null){let X=(s?l:this.parentInjector).get(se,null);X&&(i=X)}let d=tn((v=c.componentType)!=null?v:{}),h=Su(this._lContainer,(D=d==null?void 0:d.id)!=null?D:null),g=(x=h==null?void 0:h.firstChild)!=null?x:null,p=c.create(l,o,g,i);return this.insertImpl(p.hostView,a,bu(this._hostTNode,h)),p}insert(e,r){return this.insertImpl(e,r,!0)}insertImpl(e,r,n){let o=e._lView;if(a0(o)){let a=this.indexOf(e);if(a!==-1)this.detach(a);else{let c=o[Se],l=new iv(c,c[Le],c[Se]);l.detach(l.indexOf(e))}}let i=this._adjustIndex(r),s=this._lContainer;return Xm(s,o,i,n),e.attachToViewContainerRef(),ag(Hl(s),i,e),e}move(e,r){return this.insert(e,r)}indexOf(e){let r=Pp(this._lContainer);return r!==null?r.indexOf(e):-1}remove(e){let r=this._adjustIndex(e,-1),n=Mu(this._lContainer,r);n&&(Ts(Hl(this._lContainer),r),Lm(n[W],n))}detach(e){let r=this._adjustIndex(e,-1),n=Mu(this._lContainer,r);return n&&Ts(Hl(this._lContainer),r)!=null?new wo(n):null}_adjustIndex(e,r=0){return e==null?this.length+r:e}};function Pp(t){return t[As]}function Hl(t){return t[As]||(t[As]=[])}function sv(t,e){let r,n=e[t.index];return Bt(n)?r=n:(r=Km(n,e,null,t),e[t.index]=r,Ed(e,r)),Fw(r,e,t,n),new iv(r,t,e)}function Pw(t,e){let r=t[de],n=r.createComment(""),o=wt(e,t),i=r.parentNode(o);return Vs(r,i,n,r.nextSibling(o),!1),n}var Fw=Vw,jw=()=>!1;function Lw(t,e,r){return jw(t,e,r)}function Vw(t,e,r,n){if(t[An])return;let o;r.type&8?o=yt(n):o=Pw(e,r),t[An]=o}var Ru=class t{queryList;matches=null;constructor(e){this.queryList=e}clone(){return new t(this.queryList)}setDirty(){this.queryList.setDirty()}},Nu=class t{queries;constructor(e=[]){this.queries=e}createEmbeddedView(e){let r=e.queries;if(r!==null){let n=e.contentQueries!==null?e.contentQueries[0]:r.length,o=[];for(let i=0;i<n;i++){let s=r.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new t(o)}return null}insertView(e){this.dirtyQueriesWithMatches(e)}detachView(e){this.dirtyQueriesWithMatches(e)}finishViewCreation(e){this.dirtyQueriesWithMatches(e)}dirtyQueriesWithMatches(e){for(let r=0;r<this.queries.length;r++)Fd(e,r).matches!==null&&this.queries[r].setDirty()}},Hs=class{flags;read;predicate;constructor(e,r,n=null){this.flags=r,this.read=n,typeof e=="string"?this.predicate=qw(e):this.predicate=e}},Ou=class t{queries;constructor(e=[]){this.queries=e}elementStart(e,r){for(let n=0;n<this.queries.length;n++)this.queries[n].elementStart(e,r)}elementEnd(e){for(let r=0;r<this.queries.length;r++)this.queries[r].elementEnd(e)}embeddedTView(e){let r=null;for(let n=0;n<this.length;n++){let o=r!==null?r.length:0,i=this.getByIndex(n).embeddedTView(e,o);i&&(i.indexInDeclarationView=n,r!==null?r.push(i):r=[i])}return r!==null?new t(r):null}template(e,r){for(let n=0;n<this.queries.length;n++)this.queries[n].template(e,r)}getByIndex(e){return this.queries[e]}get length(){return this.queries.length}track(e){this.queries.push(e)}},ku=class t{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(e,r=-1){this.metadata=e,this._declarationNodeIndex=r}elementStart(e,r){this.isApplyingToNode(r)&&this.matchTNode(e,r)}elementEnd(e){this._declarationNodeIndex===e.index&&(this._appliesToNextNode=!1)}template(e,r){this.elementStart(e,r)}embeddedTView(e,r){return this.isApplyingToNode(e)?(this.crossesNgTemplate=!0,this.addMatch(-e.index,r),new t(this.metadata)):null}isApplyingToNode(e){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let r=this._declarationNodeIndex,n=e.parent;for(;n!==null&&n.type&8&&n.index!==r;)n=n.parent;return r===(n!==null?n.index:-1)}return this._appliesToNextNode}matchTNode(e,r){let n=this.metadata.predicate;if(Array.isArray(n))for(let o=0;o<n.length;o++){let i=n[o];this.matchTNodeWithReadOption(e,r,Bw(r,i)),this.matchTNodeWithReadOption(e,r,bs(r,e,i,!1,!1))}else n===bt?r.type&4&&this.matchTNodeWithReadOption(e,r,-1):this.matchTNodeWithReadOption(e,r,bs(r,e,n,!1,!1))}matchTNodeWithReadOption(e,r,n){if(n!==null){let o=this.metadata.read;if(o!==null)if(o===y||o===ze||o===bt&&r.type&4)this.addMatch(r.index,-2);else{let i=bs(r,e,o,!1,!1);i!==null&&this.addMatch(r.index,i)}else this.addMatch(r.index,n)}}addMatch(e,r){this.matches===null?this.matches=[e,r]:this.matches.push(e,r)}};function Bw(t,e){let r=t.localNames;if(r!==null){for(let n=0;n<r.length;n+=2)if(r[n]===e)return r[n+1]}return null}function Uw(t,e){return t.type&11?kr(t,e):t.type&4?kd(t,e):null}function Hw(t,e,r,n){return r===-1?Uw(e,t):r===-2?$w(t,e,n):Eo(t,t[W],r,e)}function $w(t,e,r){if(r===y)return kr(e,t);if(r===bt)return kd(e,t);if(r===ze)return sv(e,t)}function av(t,e,r,n){let o=e[at].queries[n];if(o.matches===null){let i=t.data,s=r.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let d=i[l];a.push(Hw(e,d,s[c+1],r.metadata.read))}}o.matches=a}return o.matches}function Pu(t,e,r,n){let o=t.queries.getByIndex(r),i=o.matches;if(i!==null){let s=av(t,e,o,r);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)n.push(s[a/2]);else{let l=i[a+1],d=e[-c];for(let h=He;h<d.length;h++){let g=d[h];g[xn]===g[Se]&&Pu(g[W],g,l,n)}if(d[Er]!==null){let h=d[Er];for(let g=0;g<h.length;g++){let p=h[g];Pu(p[W],p,l,n)}}}}}return n}function zw(t,e){return t[at].queries[e].queryList}function cv(t,e,r){var i;let n=new hu((r&4)===4);return u0(t,e,n,n.destroy),((i=e[at])!=null?i:e[at]=new Nu).queries.push(new Ru(n))-1}function Gw(t,e,r){let n=ge();return n.firstCreatePass&&(lv(n,new Hs(t,e,r),-1),(e&2)===2&&(n.staticViewQueries=!0)),cv(n,Y(),e)}function Ww(t,e,r,n){let o=ge();if(o.firstCreatePass){let i=_e();lv(o,new Hs(e,r,n),i.index),Zw(o,t),(r&2)===2&&(o.staticContentQueries=!0)}return cv(o,Y(),r)}function qw(t){return t.split(",").map(e=>e.trim())}function lv(t,e,r){t.queries===null&&(t.queries=new Ou),t.queries.track(new ku(e,r))}function Zw(t,e){let r=t.contentQueries||(t.contentQueries=[]),n=r.length?r[r.length-1]:-1;e!==n&&r.push(t.queries.length-1,e)}function Fd(t,e){return t.queries.getByIndex(e)}function Yw(t,e){let r=t[W],n=Fd(r,e);return n.crossesNgTemplate?Pu(r,t,e,[]):av(r,t,n,e)}function Qw(t){let e=[],r=new Map;function n(o){let i=r.get(o);if(!i){let s=t(o);r.set(o,i=s.then(eM))}return i}return $s.forEach((o,i)=>{var l,d;let s=[];o.templateUrl&&s.push(n(o.templateUrl).then(h=>{o.template=h}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&((l=o.styleUrls)!=null&&l.length))throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if((d=o.styleUrls)!=null&&d.length){let h=o.styles.length,g=o.styleUrls;o.styleUrls.forEach((p,v)=>{a.push(""),s.push(n(p).then(D=>{a[h+v]=D,g.splice(g.indexOf(p),1),g.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(n(o.styleUrl).then(h=>{a.push(h),o.styleUrl=void 0}));let c=Promise.all(s).then(()=>tM(i));e.push(c)}),Xw(),Promise.all(e).then(()=>{})}var $s=new Map,Kw=new Set;function Xw(){let t=$s;return $s=new Map,t}function Jw(){return $s.size===0}function eM(t){return typeof t=="string"?t:t.text()}function tM(t){Kw.delete(t)}var _r=class{},jd=class{};var zs=class extends _r{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Us(this);constructor(e,r,n,o=!0){super(),this.ngModuleType=e,this._parent=r;let i=ug(e);this._bootstrapComponents=Sm(i.bootstrap),this._r3Injector=Qg(e,r,[{provide:_r,useValue:this},{provide:ga,useValue:this.componentFactoryResolver},...n],ke(e),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let e=this._r3Injector;!e.destroyed&&e.destroy(),this.destroyCbs.forEach(r=>r()),this.destroyCbs=null}onDestroy(e){this.destroyCbs.push(e)}},Gs=class extends jd{moduleType;constructor(e){super(),this.moduleType=e}create(e){return new zs(this.moduleType,e,[])}};function nM(t,e,r){return new zs(t,e,r,!1)}var Fu=class extends _r{injector;componentFactoryResolver=new Us(this);instance=null;constructor(e){super();let r=new Io([...e.providers,{provide:_r,useValue:this},{provide:ga,useValue:this.componentFactoryResolver}],e.parent||ea(),e.debugName,new Set(["environment"]));this.injector=r,e.runEnvironmentInitializers&&r.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(e){this.injector.onDestroy(e)}};function Fo(t,e,r=null){return new Fu({providers:t,parent:e,debugName:r,runEnvironmentInitializers:!0}).injector}var rM=(()=>{let e=class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let o=dg(!1,n.type),i=o.length>0?Fo([o],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,i)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}};u(e,"\u0275prov",A({token:e,providedIn:"environment",factory:()=>new e(k(se))}));let t=e;return t})();function E(t){return Ar(()=>{var o;let e=uv(t),r=L(b({},e),{decls:t.decls,vars:t.vars,template:t.template,consts:t.consts||null,ngContentSelectors:t.ngContentSelectors,onPush:t.changeDetection===sm.OnPush,directiveDefs:null,pipeDefs:null,dependencies:e.standalone&&t.dependencies||null,getStandaloneInjector:e.standalone?i=>i.get(rM).getOrCreateStandaloneInjector(r):null,getExternalStyles:null,signals:(o=t.signals)!=null?o:!1,data:t.data||{},encapsulation:t.encapsulation||Ct.Emulated,styles:t.styles||Ke,_:null,schemas:t.schemas||null,tView:null,id:""});e.standalone&&No("NgStandalone"),dv(r);let n=t.dependencies;return r.directiveDefs=Fp(n,!1),r.pipeDefs=Fp(n,!0),r.id=cM(r),r})}function oM(t){return tn(t)||FC(t)}function iM(t){return t!==null}function Ae(t){return Ar(()=>({type:t.type,bootstrap:t.bootstrap||Ke,declarations:t.declarations||Ke,imports:t.imports||Ke,exports:t.exports||Ke,transitiveCompileScopes:null,schemas:t.schemas||null,id:t.id||null}))}function sM(t,e){var n;if(t==null)return _n;let r={};for(let o in t)if(t.hasOwnProperty(o)){let i=t[o],s,a,c,l;Array.isArray(i)?(c=i[0],s=i[1],a=(n=i[2])!=null?n:s,l=i[3]||null):(s=i,a=i,c=da.None,l=null),r[s]=[o,c,l],e[s]=a}return r}function aM(t){if(t==null)return _n;let e={};for(let r in t)t.hasOwnProperty(r)&&(e[t[r]]=r);return e}function B(t){return Ar(()=>{let e=uv(t);return dv(e),e})}function uv(t){var r;let e={};return{type:t.type,providersResolver:null,factory:null,hostBindings:t.hostBindings||null,hostVars:t.hostVars||0,hostAttrs:t.hostAttrs||null,contentQueries:t.contentQueries||null,declaredInputs:e,inputConfig:t.inputs||_n,exportAs:t.exportAs||null,standalone:(r=t.standalone)!=null?r:!0,signals:t.signals===!0,selectors:t.selectors||Ke,viewQuery:t.viewQuery||null,features:t.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:sM(t.inputs,e),outputs:aM(t.outputs),debugInfo:null}}function dv(t){var e;(e=t.features)==null||e.forEach(r=>r(t))}function Fp(t,e){if(!t)return null;let r=e?jC:oM;return()=>(typeof t=="function"?t():t).map(n=>r(n)).filter(iM)}function cM(t){let e=0,r=typeof t.consts=="function"?"":t.consts,n=[t.selectors,t.ngContentSelectors,t.hostVars,t.hostAttrs,r,t.vars,t.decls,t.encapsulation,t.standalone,t.signals,t.exportAs,JSON.stringify(t.inputs),JSON.stringify(t.outputs),Object.getOwnPropertyNames(t.type.prototype),!!t.contentQueries,!!t.viewQuery];for(let i of n.join("|"))e=Math.imul(31,e)+i.charCodeAt(0)<<0;return e+=2147483648,"c"+e}function lM(t){return Object.getPrototypeOf(t.prototype).constructor}function ne(t){let e=lM(t.type),r=!0,n=[t];for(;e;){let o;if(vt(t))o=e.\u0275cmp||e.\u0275dir;else{if(e.\u0275cmp)throw new R(903,!1);o=e.\u0275dir}if(o){if(r){n.push(o);let s=t;s.inputs=$l(t.inputs),s.declaredInputs=$l(t.declaredInputs),s.outputs=$l(t.outputs);let a=o.hostBindings;a&&pM(t,a);let c=o.viewQuery,l=o.contentQueries;if(c&&fM(t,c),l&&hM(t,l),uM(t,o),uC(t.outputs,o.outputs),vt(o)&&o.data.animation){let d=t.data;d.animation=(d.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(t),a===ne&&(r=!1)}}e=Object.getPrototypeOf(e)}dM(n)}function uM(t,e){for(let r in e.inputs){if(!e.inputs.hasOwnProperty(r)||t.inputs.hasOwnProperty(r))continue;let n=e.inputs[r];n!==void 0&&(t.inputs[r]=n,t.declaredInputs[r]=e.declaredInputs[r])}}function dM(t){let e=0,r=null;for(let n=t.length-1;n>=0;n--){let o=t[n];o.hostVars=e+=o.hostVars,o.hostAttrs=Mr(o.hostAttrs,r=Mr(r,o.hostAttrs))}}function $l(t){return t===_n?{}:t===Ke?[]:t}function fM(t,e){let r=t.viewQuery;r?t.viewQuery=(n,o)=>{e(n,o),r(n,o)}:t.viewQuery=e}function hM(t,e){let r=t.contentQueries;r?t.contentQueries=(n,o,i)=>{e(n,o,i),r(n,o,i)}:t.contentQueries=e}function pM(t,e){let r=t.hostBindings;r?t.hostBindings=(n,o)=>{e(n,o),r(n,o)}:t.hostBindings=e}function fv(t){return mM(t)?Array.isArray(t)||!(t instanceof Map)&&Symbol.iterator in t:!1}function gM(t,e){if(Array.isArray(t))for(let r=0;r<t.length;r++)e(t[r]);else{let r=t[Symbol.iterator](),n;for(;!(n=r.next()).done;)e(n.value)}}function mM(t){return t!==null&&(typeof t=="function"||typeof t=="object")}function vM(t,e,r){return t[e]=r}function yM(t,e){return t[e]}function Pn(t,e,r){let n=t[e];return Object.is(n,r)?!1:(t[e]=r,!0)}function DM(t,e,r,n){let o=Pn(t,e,r);return Pn(t,e+1,n)||o}function IM(t,e,r,n,o,i,s,a,c){let l=e.consts,d=Po(e,t,4,s||null,a||null);ed()&&Pd(e,r,d,wr(l,c),Sd),d.mergedAttrs=Mr(d.mergedAttrs,d.attrs),cd(e,d);let h=d.tView=Cd(2,d,n,o,i,e.directiveRegistry,e.pipeRegistry,null,e.schemas,l,null);return e.queries!==null&&(e.queries.template(e,d),h.queries=e.queries.embeddedTView(d)),d}function hv(t,e,r,n,o,i,s,a,c,l){let d=r+et,h=e.firstCreatePass?IM(d,e,t,n,o,i,s,a,c):e.data[d];jn(h,!1);let g=CM(e,t,h,r);sa()&&ha(e,t,g,h),Pr(g,t);let p=Km(g,t,g,h);return t[d]=p,Ed(t,p),Lw(p,h,t),na(h)&&fa(e,t,h),c!=null&&wd(t,h,l),h}function jo(t,e,r,n,o,i,s,a){let c=Y(),l=ge(),d=wr(l.consts,i);return hv(c,l,t,e,r,n,o,d,s,a),jo}var CM=bM;function bM(t,e,r,n){return aa(!0),e[de].createComment("")}var pv=(()=>{let e=class e{log(n){console.log(n)}warn(n){console.warn(n)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"platform"}));let t=e;return t})();var Ld=new N(""),Lo=new N(""),ma=(()=>{let e=class e{_ngZone;registry;_isZoneStable=!0;_callbacks=[];taskTrackingZone=null;constructor(n,o,i){this._ngZone=n,this.registry=o,yo||(EM(i),i.addToWindow(o)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{m.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(o=>o.updateCb&&o.updateCb(n)?(clearTimeout(o.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,o,i){let s=-1;o&&o>0&&(s=setTimeout(()=>{this._callbacks=this._callbacks.filter(a=>a.timeoutId!==s),n()},o)),this._callbacks.push({doneCb:n,timeoutId:s,updateCb:i})}whenStable(n,o,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,o,i),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,o,i){return[]}};u(e,"\u0275fac",function(o){return new(o||e)(k(m),k(va),k(Lo))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),va=(()=>{let e=class e{_applications=new Map;registerApplication(n,o){this._applications.set(n,o)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,o=!0){var i;return(i=yo==null?void 0:yo.findTestabilityInTree(this,n,o))!=null?i:null}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"platform"}));let t=e;return t})();function EM(t){yo=t}var yo,wM=(()=>{let e=class e{};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>new ju}));let t=e;return t})(),ju=class{queuedEffectCount=0;queues=new Map;schedule(e){this.enqueue(e)}remove(e){let r=e.zone,n=this.queues.get(r);n.has(e)&&(n.delete(e),this.queuedEffectCount--)}enqueue(e){let r=e.zone;this.queues.has(r)||this.queues.set(r,new Set);let n=this.queues.get(r);n.has(e)||(this.queuedEffectCount++,n.add(e))}flush(){for(;this.queuedEffectCount>0;)for(let[e,r]of this.queues)e===null?this.flushQueue(r):e.run(()=>this.flushQueue(r))}flushQueue(e){for(let r of e)e.delete(r),this.queuedEffectCount--,r.run()}};function Vn(t){return!!t&&typeof t.then=="function"}function gv(t){return!!t&&typeof t.subscribe=="function"}var ya=new N("");function Vd(t){return Xs([{provide:ya,multi:!0,useValue:t}])}var mv=(()=>{var e;let r=class r{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((o,i)=>{this.resolve=o,this.reject=i});appInits=(e=I(ya,{optional:!0}))!=null?e:[];injector=I(te);constructor(){}runInitializers(){if(this.initialized)return;let o=[];for(let s of this.appInits){let a=je(this.injector,s);if(Vn(a))o.push(a);else if(gv(a)){let c=new Promise((l,d)=>{a.subscribe({complete:l,error:d})});o.push(c)}}let i=()=>{this.done=!0,this.resolve()};Promise.all(o).then(()=>{i()}).catch(s=>{this.reject(s)}),o.length===0&&i(),this.initialized=!0}};u(r,"\u0275fac",function(i){return new(i||r)}),u(r,"\u0275prov",A({token:r,factory:r.\u0275fac,providedIn:"root"}));let t=r;return t})(),Bd=new N("");function MM(){xl(()=>{throw new R(600,!1)})}function SM(t){return t.isBoundToModule}var TM=10;function vv(t,e){return Array.isArray(e)?e.reduce(vv,t):b(b({},t),e)}var Et=(()=>{let e=class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=I(sb);afterRenderManager=I(gm);zonelessEnabled=I(Xg);rootEffectScheduler=I(wM);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new ee;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=I(Ut).hasPendingTasks.pipe(H(n=>!n));constructor(){I(Fr,{optional:!0})}whenStable(){let n;return new Promise(o=>{n=this.isStable.subscribe({next:i=>{i&&o()}})}).finally(()=>{n.unsubscribe()})}_injector=I(se);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,o){return this.bootstrapImpl(n,o)}bootstrapImpl(n,o,i=te.NULL){oe(10);let s=n instanceof ev;if(!this._injector.get(mv).done){let v="";throw new R(405,v)}let c;s?c=n:c=this._injector.get(ga).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let l=SM(c)?void 0:this._injector.get(_r),d=o||c.selector,h=c.create(i,[],d,l),g=h.location.nativeElement,p=h.injector.get(Ld,null);return p==null||p.registerApplication(g),h.onDestroy(()=>{this.detachView(h.hostView),Es(this.components,h),p==null||p.unregisterApplication(g)}),this._loadComponent(h),oe(11,h),h}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){oe(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(gd.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{var o;if(this._runningTick)throw new R(101,!1);let n=Z(null);try{this._runningTick=!0,this.synchronize()}catch(i){this.internalErrorHandler(i)}finally{this._runningTick=!1,(o=this.tracingSnapshot)==null||o.dispose(),this.tracingSnapshot=null,Z(n),this.afterTick.next(),oe(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Tr,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<TM;)oe(14),this.synchronizeOnce(),oe(15)}synchronizeOnce(){var n,o,i,s;if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let a=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:c,notifyErrorHandler:l}of this.allViews)_M(c,l,a,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else(o=(n=this._rendererFactory)==null?void 0:n.begin)==null||o.call(n),(s=(i=this._rendererFactory)==null?void 0:i.end)==null||s.call(i);this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>ra(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let o=n;this._views.push(o),o.attachToAppRef(this)}detachView(n){let o=n;Es(this._views,o),o.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Bd,[]).forEach(i=>i(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Es(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new R(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();function Es(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}function _M(t,e,r,n){if(!r&&!ra(t))return;qm(t,e,r&&!n?0:1)}function ct(t,e,r,n){let o=Y(),i=oa();if(Pn(o,i,e)){let s=ge(),a=ia();TE(a,o,t,e,r,n)}return ct}function yv(t,e,r,n){return Pn(t,oa(),r)?e+Tn(r)+n:St}function xM(t,e,r,n,o,i){let s=y0(),a=DM(t,s,r,o);return Ag(2),a?e+Tn(r)+n+Tn(o)+i:St}function ms(t,e){return t<<17|e<<2}function Fn(t){return t>>17&32767}function AM(t){return(t&2)==2}function RM(t,e){return t&131071|e<<17}function Lu(t){return t|2}function xr(t){return(t&131068)>>2}function zl(t,e){return t&-131069|e<<2}function NM(t){return(t&1)===1}function Vu(t){return t|1}function OM(t,e,r,n,o,i){let s=i?e.classBindings:e.styleBindings,a=Fn(s),c=xr(s);t[n]=r;let l=!1,d;if(Array.isArray(r)){let h=r;d=h[1],(d===null||To(h,d)>0)&&(l=!0)}else d=r;if(o)if(c!==0){let g=Fn(t[a+1]);t[n+1]=ms(g,a),g!==0&&(t[g+1]=zl(t[g+1],n)),t[a+1]=RM(t[a+1],n)}else t[n+1]=ms(a,0),a!==0&&(t[a+1]=zl(t[a+1],n)),a=n;else t[n+1]=ms(c,0),a===0?a=n:t[c+1]=zl(t[c+1],n),c=n;l&&(t[n+1]=Lu(t[n+1])),jp(t,d,n,!0),jp(t,d,n,!1),kM(e,d,t,n,i),s=ms(a,c),i?e.classBindings=s:e.styleBindings=s}function kM(t,e,r,n,o){let i=o?t.residualClasses:t.residualStyles;i!=null&&typeof e=="string"&&To(i,e)>=0&&(r[n+1]=Vu(r[n+1]))}function jp(t,e,r,n){let o=t[r+1],i=e===null,s=n?Fn(o):xr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=t[s],l=t[s+1];PM(c,e)&&(a=!0,t[s+1]=n?Vu(l):Lu(l)),s=n?Fn(l):xr(l)}a&&(t[r+1]=n?Lu(o):Vu(o))}function PM(t,e){return t===null||e==null||(Array.isArray(t)?t[1]:t)===e?!0:Array.isArray(t)&&typeof e=="string"?To(t,e)>=0:!1}function on(t,e,r){let n=Y(),o=oa();if(Pn(n,o,e)){let i=ge(),s=ia();Md(i,s,n,t,e,n[de],r,!1)}return on}function Lp(t,e,r,n,o){Td(e,t,r,o?"class":"style",n)}function Da(t,e){return FM(t,e,null,!0),Da}function FM(t,e,r,n){let o=Y(),i=ge(),s=Ag(2);if(i.firstUpdatePass&&LM(i,t,s,n),e!==St&&Pn(o,s,e)){let a=i.data[Ln()];$M(i,a,o,o[de],t,o[s+1]=zM(e,r),n,s)}}function jM(t,e){return e>=t.expandoStartIndex}function LM(t,e,r,n){let o=t.data;if(o[r+1]===null){let i=o[Ln()],s=jM(t,r);GM(i,n)&&e===null&&!s&&(e=!1),e=VM(o,i,e,n),OM(o,i,e,r,s,n)}}function VM(t,e,r,n){let o=E0(t),i=n?e.residualClasses:e.residualStyles;if(o===null)(n?e.classBindings:e.styleBindings)===0&&(r=Gl(null,t,e,r,n),r=Mo(r,e.attrs,n),i=null);else{let s=e.directiveStylingLast;if(s===-1||t[s]!==o)if(r=Gl(o,t,e,r,n),i===null){let c=BM(t,e,n);c!==void 0&&Array.isArray(c)&&(c=Gl(null,t,e,c[1],n),c=Mo(c,e.attrs,n),UM(t,e,n,c))}else i=HM(t,e,n)}return i!==void 0&&(n?e.residualClasses=i:e.residualStyles=i),r}function BM(t,e,r){let n=r?e.classBindings:e.styleBindings;if(xr(n)!==0)return t[Fn(n)]}function UM(t,e,r,n){let o=r?e.classBindings:e.styleBindings;t[Fn(o)]=n}function HM(t,e,r){let n,o=e.directiveEnd;for(let i=1+e.directiveStylingLast;i<o;i++){let s=t[i].hostAttrs;n=Mo(n,s,r)}return Mo(n,e.attrs,r)}function Gl(t,e,r,n,o){let i=null,s=r.directiveEnd,a=r.directiveStylingLast;for(a===-1?a=r.directiveStart:a++;a<s&&(i=e[a],n=Mo(n,i.hostAttrs,o),i!==t);)a++;return t!==null&&(r.directiveStylingLast=a),n}function Mo(t,e,r){let n=r?1:2,o=-1;if(e!==null)for(let i=0;i<e.length;i++){let s=e[i];typeof s=="number"?o=s:o===n&&(Array.isArray(t)||(t=t===void 0?[]:["",t]),kC(t,s,r?!0:e[++i]))}return t===void 0?null:t}function $M(t,e,r,n,o,i,s,a){if(!(e.type&3))return;let c=t.data,l=c[a+1],d=NM(l)?Vp(c,e,r,o,xr(l),s):void 0;if(!Ws(d)){Ws(i)||AM(l)&&(i=Vp(c,null,r,o,a,s));let h=bg(Ln(),r);GE(n,s,h,o,i)}}function Vp(t,e,r,n,o,i){let s=e===null,a;for(;o>0;){let c=t[o],l=Array.isArray(c),d=l?c[1]:c,h=d===null,g=r[o+1];g===St&&(g=h?Ke:void 0);let p=h?kl(g,n):d===n?g:void 0;if(l&&!Ws(p)&&(p=kl(c,n)),Ws(p)&&(a=p,s))return a;let v=t[o+1];o=s?Fn(v):xr(v)}if(e!==null){let c=i?e.residualClasses:e.residualStyles;c!=null&&(a=kl(c,n))}return a}function Ws(t){return t!==void 0}function zM(t,e){return t==null||t===""||(typeof e=="string"?t=t+e:typeof t=="object"&&(t=ke(Oo(t)))),t}function GM(t,e){return(t.flags&(e?8:16))!==0}function jr(t,e,r,n){let o=Y(),i=ge(),s=et+t,a=o[de],c=i.firstCreatePass?rv(s,i,o,e,Sd,ed(),r,n):i.data[s],l=WM(i,o,c,a,e,t);o[s]=l;let d=na(c);return jn(c,!0),Rm(a,l,c),!Ad(c)&&sa()&&ha(i,o,l,c),(d0()===0||d)&&Pr(l,o),f0(),d&&(fa(i,o,c),vd(i,c,o)),n!==null&&wd(o,c),jr}function Lr(){let t=_e();rd()?od():(t=t.parent,jn(t,!1));let e=t;p0(e)&&g0(),h0();let r=ge();return r.firstCreatePass&&ov(r,e),e.classesWithoutHost!=null&&x0(e)&&Lp(r,e,Y(),e.classesWithoutHost,!0),e.stylesWithoutHost!=null&&A0(e)&&Lp(r,e,Y(),e.stylesWithoutHost,!1),Lr}function Ud(t,e,r,n){return jr(t,e,r,n),Lr(),Ud}var WM=(t,e,r,n,o,i)=>(aa(!0),xm(n,o,S0()));function qM(t,e,r,n,o){let i=e.consts,s=wr(i,n),a=Po(e,t,8,"ng-container",s);s!==null&&xu(a,s,!0);let c=wr(i,o);return ed()&&Pd(e,r,a,c,Sd),a.mergedAttrs=Mr(a.mergedAttrs,a.attrs),e.queries!==null&&e.queries.elementStart(e,a),a}function Ia(t,e,r){let n=Y(),o=ge(),i=t+et,s=o.firstCreatePass?qM(i,o,n,e,r):o.data[i];jn(s,!0);let a=ZM(o,n,s,t);return n[i]=a,sa()&&ha(o,n,a,s),Pr(a,n),na(s)&&(fa(o,n,s),vd(o,s,n)),r!=null&&wd(n,s),Ia}function Ca(){let t=_e(),e=ge();return rd()?od():(t=t.parent,jn(t,!1)),e.firstCreatePass&&(cd(e,t),Qu(t)&&e.queries.elementEnd(t)),Ca}function ba(t,e,r){return Ia(t,e,r),Ca(),ba}var ZM=(t,e,r,n)=>(aa(!0),fE(e[de],""));function Dv(){return Y()}var qs="en-US";var YM=qs;function QM(t){typeof t=="string"&&(YM=t.toLowerCase().replace(/_/g,"-"))}function Bp(t,e,r){return function n(o){if(o===Function)return r;let i=Or(t)?Dt(t.index,e):e;Od(i,5);let s=e[Fe],a=Up(e,s,r,o),c=n.__ngNextListenerFn__;for(;c;)a=Up(e,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Up(t,e,r,n){let o=Z(null);try{return oe(6,e,r),r(n)!==!1}catch(i){return KM(t,i),!1}finally{oe(7,e,r),Z(o)}}function KM(t,e){let r=t[br],n=r?r.get(It,null):null;n&&n.handleError(e)}function Hp(t,e,r,n,o,i){let s=e[r],a=e[W],l=a.data[r].outputs[n],d=s[l],h=a.firstCreatePass?Ju(a):null,g=Xu(e),p=d.subscribe(i),v=g.length;g.push(i,p),h&&h.push(o,t.index,v,-(v+1))}var XM=(t,e,r)=>{};function Ie(t,e,r,n){let o=Y(),i=ge(),s=_e();return Iv(i,o,o[de],s,t,e,n),Ie}function JM(t,e,r,n){let o=t.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===r&&o[i+1]===n){let a=e[vr],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Iv(t,e,r,n,o,i,s){var g,p;let a=na(n),l=t.firstCreatePass?Ju(t):null,d=Xu(e),h=!0;if(n.type&3||s){let v=wt(n,e),D=s?s(v):v,x=d.length,O=s?Ue=>s(yt(Ue[n.index])):n.index,X=null;if(!s&&a&&(X=JM(t,e,o,n.index)),X!==null){let Ue=X.__ngLastListenerFn__||X;Ue.__ngNextListenerFn__=i,X.__ngLastListenerFn__=i,h=!1}else{i=Bp(n,e,i),XM(D,o,i);let Ue=r.listen(D,o,i);d.push(i,Ue),l&&l.push(o,O,x,x+1)}}else i=Bp(n,e,i);if(h){let v=(g=n.outputs)==null?void 0:g[o],D=(p=n.hostDirectiveOutputs)==null?void 0:p[o];if(D&&D.length)for(let x=0;x<D.length;x+=2){let O=D[x],X=D[x+1];Hp(n,e,O,X,o,i)}if(v&&v.length)for(let x of v)Hp(n,e,x,o,o,i)}}function Vo(t=1){return M0(t)}function eS(t,e){let r=null,n=rE(t);for(let o=0;o<e.length;o++){let i=e[o];if(i==="*"){r=o;continue}if(n===null?_m(t,i,!0):sE(n,i))return o}return r}function S(t){let e=Y()[Je][Le];if(!e.projection){let r=t?t.length:1,n=e.projection=ys(r,null),o=n.slice(),i=e.child;for(;i!==null;){if(i.type!==128){let s=t?eS(i,t):0;s!==null&&(o[s]?o[s].projectionNext=i:n[s]=i,o[s]=i)}i=i.next}}}function w(t,e=0,r,n,o,i){let s=Y(),a=ge(),c=n?t+1:null;c!==null&&hv(s,a,c,n,o,i,null,r);let l=Po(a,et+t,16,null,r||null);l.projection===null&&(l.projection=e),od();let h=!s[Co]||Tg();s[Je][Le].projection[l.projection]===null&&c!==null?tS(s,a,c):h&&!Ad(l)&&$E(a,s,l)}function tS(t,e,r){let n=et+r,o=e.data[n],i=t[n],s=Su(i,o.tView.ssrId),a=Fm(t,o,void 0,{dehydratedView:s});Xm(i,a,0,bu(o,s))}function nS(t,e,r){return Cv(t,"",e,"",r),nS}function Cv(t,e,r,n,o){let i=Y(),s=yv(i,e,r,n);if(s!==St){let a=ge(),c=ia();Md(a,c,i,t,s,i[de],o,!1)}return Cv}function sn(t,e,r,n){Ww(t,e,r,n)}function Bo(t,e,r){Gw(t,e,r)}function lt(t){let e=Y(),r=ge(),n=Rg();id(n+1);let o=Fd(r,n);if(t.dirty&&s0(e)===((o.metadata.flags&2)===2)){if(o.matches===null)t.reset([]);else{let i=Yw(e,n);t.reset(i,lb),t.notifyOnChanges()}return!0}return!1}function ut(){return zw(Y(),Rg())}function vV(t,e=""){let r=Y(),n=ge(),o=t+et,i=n.firstCreatePass?Po(n,o,1,e,null):n.data[o],s=rS(n,r,i,e,t);r[o]=s,sa()&&ha(n,r,s,i),jn(i,!1)}var rS=(t,e,r,n,o)=>(aa(!0),uE(e[de],n));function oS(t){return bv("",t,""),oS}function bv(t,e,r){let n=Y(),o=yv(n,t,e,r);return o!==St&&Ev(n,Ln(),o),bv}function iS(t,e,r,n,o){let i=Y(),s=xM(i,t,e,r,n,o);return s!==St&&Ev(i,Ln(),s),iS}function Ev(t,e,r){let n=bg(e,t);dE(t[de],n,r)}function sS(t,e,r){om(e)&&(e=e());let n=Y(),o=oa();if(Pn(n,o,e)){let i=ge(),s=ia();Md(i,s,n,t,e,n[de],r,!1)}return sS}function yV(t,e){let r=om(t);return r&&t.set(e),r}function aS(t,e){let r=Y(),n=ge(),o=_e();return Iv(n,r,r[de],o,t,e),aS}function cS(t,e,r){let n=ge();if(n.firstCreatePass){let o=vt(t);Bu(r,n.data,n.blueprint,o,!0),Bu(e,n.data,n.blueprint,o,!1)}}function Bu(t,e,r,n,o){if(t=Me(t),Array.isArray(t))for(let i=0;i<t.length;i++)Bu(t[i],e,r,n,o);else{let i=ge(),s=Y(),a=_e(),c=Cr(t)?t:Me(t.provide),l=pg(t),d=a.providerIndexes&1048575,h=a.directiveStart,g=a.providerIndexes>>20;if(Cr(t)||!t.multi){let p=new On(l,o,f),v=ql(c,e,o?d:d+g,h);v===-1?(cu(ks(a,s),i,c),Wl(i,t,e.length),e.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),r.push(p),s.push(p)):(r[v]=p,s[v]=p)}else{let p=ql(c,e,d+g,h),v=ql(c,e,d,d+g),D=p>=0&&r[p],x=v>=0&&r[v];if(o&&!x||!o&&!D){cu(ks(a,s),i,c);let O=dS(o?uS:lS,r.length,o,n,l);!o&&x&&(r[v].providerFactory=O),Wl(i,t,e.length,0),e.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),r.push(O),s.push(O)}else{let O=wv(r[o?v:p],l,!o&&n);Wl(i,t,p>-1?p:v,O)}!o&&n&&x&&r[v].componentProviders++}}}function Wl(t,e,r,n){let o=Cr(e),i=HC(e);if(o||i){let c=(i?Me(e.useClass):e).prototype.ngOnDestroy;if(c){let l=t.destroyHooks||(t.destroyHooks=[]);if(!o&&e.multi){let d=l.indexOf(r);d===-1?l.push(r,[n,c]):l[d+1].push(n,c)}else l.push(r,c)}}}function wv(t,e,r){return r&&t.componentProviders++,t.multi.push(e)-1}function ql(t,e,r,n){for(let o=r;o<n;o++)if(e[o]===t)return o;return-1}function lS(t,e,r,n){return Uu(this.multi,[])}function uS(t,e,r,n){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Eo(r,r[W],this.providerFactory.index,n);i=a.slice(0,s),Uu(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],Uu(o,i);return i}function Uu(t,e){for(let r=0;r<t.length;r++){let n=t[r];e.push(n())}return e}function dS(t,e,r,n,o){let i=new On(t,r,f);return i.multi=[],i.index=e,i.componentProviders=0,wv(i,o,n&&!r),i}function Re(t,e=[]){return r=>{r.providersResolver=(n,o)=>cS(n,o?o(t):t,e)}}function DV(t,e,r){let n=v0()+t,o=Y();return o[n]===St?vM(o,n,r?e.call(r):e()):yM(o,n)}var vs=null;function fS(t){vs!==null&&(t.defaultEncapsulation!==vs.defaultEncapsulation||t.preserveWhitespaces!==vs.preserveWhitespaces)||(vs=t)}var Hu=class{ngModuleFactory;componentFactories;constructor(e,r){this.ngModuleFactory=e,this.componentFactories=r}},Ea=(()=>{let e=class e{compileModuleSync(n){return new Gs(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let o=this.compileModuleSync(n),i=ug(n),s=Sm(i.declarations).reduce((a,c)=>{let l=tn(c);return l&&a.push(new kn(l)),a},[]);return new Hu(o,s)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),hS=new N("");function pS(t,e,r){let n=new Gs(r);return Promise.resolve(n)}function $p(t){for(let e=t.length-1;e>=0;e--)if(t[e]!==void 0)return t[e]}var gS=(()=>{let e=class e{zone=I(m);changeDetectionScheduler=I(Sr);applicationRef=I(Et);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){var n;(n=this._onMicrotaskEmptySubscription)==null||n.unsubscribe()}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();function mS({ngZoneFactory:t,ignoreChangesOutsideZone:e,scheduleInRootZone:r}){return t!=null||(t=()=>new m(L(b({},Mv()),{scheduleInRootZone:r}))),[{provide:m,useFactory:t},{provide:Do,multi:!0,useFactory:()=>{let n=I(gS,{optional:!0});return()=>n.initialize()}},{provide:Do,multi:!0,useFactory:()=>{let n=I(vS);return()=>{n.initialize()}}},e===!0?{provide:Jg,useValue:!0}:[],{provide:em,useValue:r!=null?r:Kg}]}function Mv(t){var e,r;return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:(e=t==null?void 0:t.eventCoalescing)!=null?e:!1,shouldCoalesceRunChangeDetection:(r=t==null?void 0:t.runCoalescing)!=null?r:!1}}var vS=(()=>{let e=class e{subscription=new ue;initialized=!1;zone=I(m);pendingTasks=I(Ut);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{m.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{m.assertInAngularZone(),n!=null||(n=this.pendingTasks.add())}))}ngOnDestroy(){this.subscription.unsubscribe()}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();var yS=(()=>{var e,r,n;let o=class o{appRef=I(Et);taskService=I(Ut);ngZone=I(m);zonelessEnabled=I(Xg);tracing=I(Fr,{optional:!0});disableScheduling=(e=I(Jg,{optional:!0}))!=null?e:!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new ue;angularZoneId=this.zoneIsDefined?(r=this.ngZone._inner)==null?void 0:r.get(Fs):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&((n=I(em,{optional:!0}))!=null?n:!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||(this.disableScheduling=!this.zonelessEnabled&&(this.ngZone instanceof js||!this.zoneIsDefined))}notify(s){var l,d;if(!this.zonelessEnabled&&s===5)return;let a=!1;switch(s){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,a=!0;break}case 12:{this.appRef.dirtyFlags|=16,a=!0;break}case 13:{this.appRef.dirtyFlags|=2,a=!0;break}case 11:{a=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=(d=(l=this.tracing)==null?void 0:l.snapshot(this.appRef.tracingSnapshot))!=null?d:null,!this.shouldScheduleTick(a))return;let c=this.useMicrotaskScheduler?vp:tm;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>c(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>c(()=>this.tick()))}shouldScheduleTick(s){return!(this.disableScheduling&&!s||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Fs+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let s=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(a){throw this.taskService.remove(s),a}finally{this.cleanup()}this.useMicrotaskScheduler=!0,vp(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(s)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){var s;if(this.runningTick=!1,(s=this.cancelScheduledCallback)==null||s.call(this),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let a=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(a)}}};u(o,"\u0275fac",function(a){return new(a||o)}),u(o,"\u0275prov",A({token:o,factory:o.\u0275fac,providedIn:"root"}));let t=o;return t})();function DS(){return typeof $localize<"u"&&$localize.locale||qs}var Hd=new N("",{providedIn:"root",factory:()=>I(Hd,$.Optional|$.SkipSelf)||DS()});var Zs=new N(""),IS=new N("");function ho(t){return!t.moduleRef}function CS(t){let e=ho(t)?t.r3Injector:t.moduleRef.injector,r=e.get(m);return r.run(()=>{ho(t)?t.r3Injector.resolveInjectorInitializers():t.moduleRef.resolveInjectorInitializers();let n=e.get(It,null),o;if(r.runOutsideAngular(()=>{o=r.onError.subscribe({next:i=>{n.handleError(i)}})}),ho(t)){let i=()=>e.destroy(),s=t.platformInjector.get(Zs);s.add(i),e.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>t.moduleRef.destroy(),s=t.platformInjector.get(Zs);s.add(i),t.moduleRef.onDestroy(()=>{Es(t.allPlatformModules,t.moduleRef),o.unsubscribe(),s.delete(i)})}return ES(n,r,()=>{let i=e.get(mv);return i.runInitializers(),i.donePromise.then(()=>{let s=e.get(Hd,qs);if(QM(s||qs),!e.get(IS,!0))return ho(t)?e.get(Et):(t.allPlatformModules.push(t.moduleRef),t.moduleRef);if(ho(t)){let c=e.get(Et);return t.rootComponent!==void 0&&c.bootstrap(t.rootComponent),c}else return bS(t.moduleRef,t.allPlatformModules),t.moduleRef})})})}function bS(t,e){let r=t.injector.get(Et);if(t._bootstrapComponents.length>0)t._bootstrapComponents.forEach(n=>r.bootstrap(n));else if(t.instance.ngDoBootstrap)t.instance.ngDoBootstrap(r);else throw new R(-403,!1);e.push(t)}function ES(t,e,r){try{let n=r();return Vn(n)?n.catch(o=>{throw e.runOutsideAngular(()=>t.handleError(o)),o}):n}catch(n){throw e.runOutsideAngular(()=>t.handleError(n)),n}}var Sv=(()=>{let e=class e{_injector;_modules=[];_destroyListeners=[];_destroyed=!1;constructor(n){this._injector=n}bootstrapModuleFactory(n,o){let i=o==null?void 0:o.scheduleInRootZone,s=()=>ib(o==null?void 0:o.ngZone,L(b({},Mv({eventCoalescing:o==null?void 0:o.ngZoneEventCoalescing,runCoalescing:o==null?void 0:o.ngZoneRunCoalescing})),{scheduleInRootZone:i})),a=o==null?void 0:o.ignoreChangesOutsideZone,c=[mS({ngZoneFactory:s,ignoreChangesOutsideZone:a}),{provide:Sr,useExisting:yS}],l=nM(n.moduleType,this.injector,c);return CS({moduleRef:l,allPlatformModules:this._modules,platformInjector:this.injector})}bootstrapModule(n,o=[]){let i=vv({},o);return pS(this.injector,i,n).then(s=>this.bootstrapModuleFactory(s,i))}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new R(404,!1);this._modules.slice().forEach(o=>o.destroy()),this._destroyListeners.forEach(o=>o());let n=this._injector.get(Zs,null);n&&(n.forEach(o=>o()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}};u(e,"\u0275fac",function(o){return new(o||e)(k(te))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"platform"}));let t=e;return t})(),Sn=null,Tv=new N("");function wS(t){if(Sn&&!Sn.get(Tv,!1))throw new R(400,!1);MM(),Sn=t;let e=t.get(Sv);return TS(t),e}function $d(t,e,r=[]){let n=`Platform: ${e}`,o=new N(n);return(i=[])=>{let s=_v();if(!s||s.injector.get(Tv,!1)){let a=[...r,...i,{provide:o,useValue:!0}];t?t(a):wS(MS(a,n))}return SS(o)}}function MS(t=[],e){return te.create({name:e,providers:[{provide:Js,useValue:"platform"},{provide:Zs,useValue:new Set([()=>Sn=null])},...t]})}function SS(t){let e=_v();if(!e)throw new R(401,!1);return e}function _v(){var t;return(t=Sn==null?void 0:Sn.get(Sv))!=null?t:null}function TS(t){let e=t.get(hd,null);je(t,()=>{e==null||e.forEach(r=>r())})}var C=(()=>{class t{}return u(t,"__NG_ELEMENT_ID__",_S),t})();function _S(t){return xS(_e(),Y(),(t&16)===16)}function xS(t,e,r){if(Or(t)&&!r){let n=Dt(t.index,e);return new wo(n,n)}else if(t.type&175){let n=e[Je];return new wo(n,e)}return null}var $u=class{constructor(){}supports(e){return fv(e)}create(e){return new zu(e)}},AS=(t,e)=>e,zu=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(e){this._trackByFn=e||AS}forEachItem(e){let r;for(r=this._itHead;r!==null;r=r._next)e(r)}forEachOperation(e){let r=this._itHead,n=this._removalsHead,o=0,i=null;for(;r||n;){let s=!n||r&&r.currentIndex<zp(n,o,i)?r:n,a=zp(s,o,i),c=s.currentIndex;if(s===n)o--,n=n._nextRemoved;else if(r=r._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,d=c-o;if(l!=d){for(let g=0;g<l;g++){let p=g<i.length?i[g]:i[g]=0,v=p+g;d<=v&&v<l&&(i[g]=p+1)}let h=s.previousIndex;i[h]=d-l}}a!==c&&e(s,a,c)}}forEachPreviousItem(e){let r;for(r=this._previousItHead;r!==null;r=r._nextPrevious)e(r)}forEachAddedItem(e){let r;for(r=this._additionsHead;r!==null;r=r._nextAdded)e(r)}forEachMovedItem(e){let r;for(r=this._movesHead;r!==null;r=r._nextMoved)e(r)}forEachRemovedItem(e){let r;for(r=this._removalsHead;r!==null;r=r._nextRemoved)e(r)}forEachIdentityChange(e){let r;for(r=this._identityChangesHead;r!==null;r=r._nextIdentityChange)e(r)}diff(e){if(e==null&&(e=[]),!fv(e))throw new R(900,!1);return this.check(e)?this:null}onDestroy(){}check(e){this._reset();let r=this._itHead,n=!1,o,i,s;if(Array.isArray(e)){this.length=e.length;for(let a=0;a<this.length;a++)i=e[a],s=this._trackByFn(a,i),r===null||!Object.is(r.trackById,s)?(r=this._mismatch(r,i,s,a),n=!0):(n&&(r=this._verifyReinsertion(r,i,s,a)),Object.is(r.item,i)||this._addIdentityChange(r,i)),r=r._next}else o=0,gM(e,a=>{s=this._trackByFn(o,a),r===null||!Object.is(r.trackById,s)?(r=this._mismatch(r,a,s,o),n=!0):(n&&(r=this._verifyReinsertion(r,a,s,o)),Object.is(r.item,a)||this._addIdentityChange(r,a)),r=r._next,o++}),this.length=o;return this._truncate(r),this.collection=e,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let e;for(e=this._previousItHead=this._itHead;e!==null;e=e._next)e._nextPrevious=e._next;for(e=this._additionsHead;e!==null;e=e._nextAdded)e.previousIndex=e.currentIndex;for(this._additionsHead=this._additionsTail=null,e=this._movesHead;e!==null;e=e._nextMoved)e.previousIndex=e.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(e,r,n,o){let i;return e===null?i=this._itTail:(i=e._prev,this._remove(e)),e=this._unlinkedRecords===null?null:this._unlinkedRecords.get(n,null),e!==null?(Object.is(e.item,r)||this._addIdentityChange(e,r),this._reinsertAfter(e,i,o)):(e=this._linkedRecords===null?null:this._linkedRecords.get(n,o),e!==null?(Object.is(e.item,r)||this._addIdentityChange(e,r),this._moveAfter(e,i,o)):e=this._addAfter(new Gu(r,n),i,o)),e}_verifyReinsertion(e,r,n,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(n,null);return i!==null?e=this._reinsertAfter(i,e._prev,o):e.currentIndex!=o&&(e.currentIndex=o,this._addToMoves(e,o)),e}_truncate(e){for(;e!==null;){let r=e._next;this._addToRemovals(this._unlink(e)),e=r}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(e,r,n){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(e);let o=e._prevRemoved,i=e._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(e,r,n),this._addToMoves(e,n),e}_moveAfter(e,r,n){return this._unlink(e),this._insertAfter(e,r,n),this._addToMoves(e,n),e}_addAfter(e,r,n){return this._insertAfter(e,r,n),this._additionsTail===null?this._additionsTail=this._additionsHead=e:this._additionsTail=this._additionsTail._nextAdded=e,e}_insertAfter(e,r,n){let o=r===null?this._itHead:r._next;return e._next=o,e._prev=r,o===null?this._itTail=e:o._prev=e,r===null?this._itHead=e:r._next=e,this._linkedRecords===null&&(this._linkedRecords=new Ys),this._linkedRecords.put(e),e.currentIndex=n,e}_remove(e){return this._addToRemovals(this._unlink(e))}_unlink(e){this._linkedRecords!==null&&this._linkedRecords.remove(e);let r=e._prev,n=e._next;return r===null?this._itHead=n:r._next=n,n===null?this._itTail=r:n._prev=r,e}_addToMoves(e,r){return e.previousIndex===r||(this._movesTail===null?this._movesTail=this._movesHead=e:this._movesTail=this._movesTail._nextMoved=e),e}_addToRemovals(e){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Ys),this._unlinkedRecords.put(e),e.currentIndex=null,e._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=e,e._prevRemoved=null):(e._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=e),e}_addIdentityChange(e,r){return e.item=r,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=e:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=e,e}},Gu=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(e,r){this.item=e,this.trackById=r}},Wu=class{_head=null;_tail=null;add(e){this._head===null?(this._head=this._tail=e,e._nextDup=null,e._prevDup=null):(this._tail._nextDup=e,e._prevDup=this._tail,e._nextDup=null,this._tail=e)}get(e,r){let n;for(n=this._head;n!==null;n=n._nextDup)if((r===null||r<=n.currentIndex)&&Object.is(n.trackById,e))return n;return null}remove(e){let r=e._prevDup,n=e._nextDup;return r===null?this._head=n:r._nextDup=n,n===null?this._tail=r:n._prevDup=r,this._head===null}},Ys=class{map=new Map;put(e){let r=e.trackById,n=this.map.get(r);n||(n=new Wu,this.map.set(r,n)),n.add(e)}get(e,r){let n=e,o=this.map.get(n);return o?o.get(e,r):null}remove(e){let r=e.trackById;return this.map.get(r).remove(e)&&this.map.delete(r),e}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function zp(t,e,r){let n=t.previousIndex;if(n===null)return n;let o=0;return r&&n<r.length&&(o=r[n]),n+e+o}function Gp(){return new zd([new $u])}var zd=(()=>{let e=class e{factories;constructor(n){this.factories=n}static create(n,o){if(o!=null){let i=o.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:o=>e.create(n,o||Gp()),deps:[[e,new sg,new ig]]}}find(n){let o=this.factories.find(i=>i.supports(n));if(o!=null)return o;throw new R(901,!1)}};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:Gp}));let t=e;return t})();var xv=$d(null,"core",[]),Av=(()=>{let e=class e{constructor(n){}};u(e,"\u0275fac",function(o){return new(o||e)(k(Et))}),u(e,"\u0275mod",Ae({type:e})),u(e,"\u0275inj",Te({}));let t=e;return t})();function an(t){return typeof t=="boolean"?t:t!=null&&t!=="false"}function $t(t){return Nl(t)}function Uo(t,e){return _l(t,e==null?void 0:e.equal)}var Wp=class{[Ye];constructor(e){this[Ye]=e}destroy(){this[Ye].destroy()}};function Rv(t,e){let r=tn(t),n=e.elementInjector||ea();return new kn(r).create(n,e.projectableNodes,e.hostElement,e.environmentInjector)}function wa(t){let e=tn(t);if(!e)return null;let r=new kn(e);return{get selector(){return r.selector},get type(){return r.componentType},get inputs(){return r.inputs},get outputs(){return r.outputs},get ngContentSelectors(){return r.ngContentSelectors},get isStandalone(){return e.standalone},get isSignal(){return e.signals}}}var ce=new N("");var Ma=null;function Ge(){return Ma}function Gd(t){Ma!=null||(Ma=t)}var Ho=class{},$o=(()=>{let e=class e{historyGo(n){throw new Error("")}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(kv),providedIn:"platform"}));let t=e;return t})(),Wd=new N(""),kv=(()=>{let e=class e extends $o{_location;_history;_doc=I(ce);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Ge().getBaseHref(this._doc)}onPopState(n){let o=Ge().getGlobalEventTarget(this._doc,"window");return o.addEventListener("popstate",n,!1),()=>o.removeEventListener("popstate",n)}onHashChange(n){let o=Ge().getGlobalEventTarget(this._doc,"window");return o.addEventListener("hashchange",n,!1),()=>o.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,o,i){this._history.pushState(n,o,i)}replaceState(n,o,i){this._history.replaceState(n,o,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>new e,providedIn:"platform"}));let t=e;return t})();function Sa(t,e){return t?e?t.endsWith("/")?e.startsWith("/")?t+e.slice(1):t+e:e.startsWith("/")?t+e:`${t}/${e}`:t:e}function Nv(t){let e=t.search(/#|\?|$/);return t[e-1]==="/"?t.slice(0,e-1)+t.slice(e):t}function ft(t){return t&&t[0]!=="?"?`?${t}`:t}var Ve=(()=>{let e=class e{historyGo(n){throw new Error("")}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(_a),providedIn:"root"}));let t=e;return t})(),Ta=new N(""),_a=(()=>{let e=class e extends Ve{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,o){var i,s,a;super(),this._platformLocation=n,this._baseHref=(a=(s=o!=null?o:this._platformLocation.getBaseHrefFromDOM())!=null?s:(i=I(ce).location)==null?void 0:i.origin)!=null?a:""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Sa(this._baseHref,n)}path(n=!1){let o=this._platformLocation.pathname+ft(this._platformLocation.search),i=this._platformLocation.hash;return i&&n?`${o}${i}`:o}pushState(n,o,i,s){let a=this.prepareExternalUrl(i+ft(s));this._platformLocation.pushState(n,o,a)}replaceState(n,o,i,s){let a=this.prepareExternalUrl(i+ft(s));this._platformLocation.replaceState(n,o,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){var o,i;(i=(o=this._platformLocation).historyGo)==null||i.call(o,n)}};u(e,"\u0275fac",function(o){return new(o||e)(k($o),k(Ta,8))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),We=(()=>{let e=class e{_subject=new ee;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let o=this._locationStrategy.getBaseHref();this._basePath=OS(Nv(Ov(o))),this._locationStrategy.onPopState(i=>{this._subject.next({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){var n;(n=this._urlChangeSubscription)==null||n.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,o=""){return this.path()==this.normalize(n+ft(o))}normalize(n){return e.stripTrailingSlash(NS(this._basePath,Ov(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,o="",i=null){this._locationStrategy.pushState(i,"",n,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+ft(o)),i)}replaceState(n,o="",i=null){this._locationStrategy.replaceState(i,"",n,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+ft(o)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){var o,i;(i=(o=this._locationStrategy).historyGo)==null||i.call(o,n)}onUrlChange(n){var o;return this._urlChangeListeners.push(n),(o=this._urlChangeSubscription)!=null||(this._urlChangeSubscription=this.subscribe(i=>{this._notifyUrlChangeListeners(i.url,i.state)})),()=>{var s;let i=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(i,1),this._urlChangeListeners.length===0&&((s=this._urlChangeSubscription)==null||s.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",o){this._urlChangeListeners.forEach(i=>i(n,o))}subscribe(n,o,i){return this._subject.subscribe({next:n,error:o!=null?o:void 0,complete:i!=null?i:void 0})}};u(e,"normalizeQueryParams",ft),u(e,"joinWithSlash",Sa),u(e,"stripTrailingSlash",Nv),u(e,"\u0275fac",function(o){return new(o||e)(k(Ve))}),u(e,"\u0275prov",A({token:e,factory:()=>RS(),providedIn:"root"}));let t=e;return t})();function RS(){return new We(k(Ve))}function NS(t,e){if(!t||!e.startsWith(t))return e;let r=e.substring(t.length);return r===""||["/",";","?","#"].includes(r[0])?r:e}function Ov(t){return t.replace(/\/index.html$/,"")}function OS(t){if(new RegExp("^(https?:)?//").test(t)){let[,r]=t.split(/\/\/[^\/]+/);return r}return t}var Zd=(()=>{let e=class e extends Ve{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,o){super(),this._platformLocation=n,o!=null&&(this._baseHref=o)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){var i;let o=(i=this._platformLocation.hash)!=null?i:"#";return o.length>0?o.substring(1):o}prepareExternalUrl(n){let o=Sa(this._baseHref,n);return o.length>0?"#"+o:o}pushState(n,o,i,s){let a=this.prepareExternalUrl(i+ft(s))||this._platformLocation.pathname;this._platformLocation.pushState(n,o,a)}replaceState(n,o,i,s){let a=this.prepareExternalUrl(i+ft(s))||this._platformLocation.pathname;this._platformLocation.replaceState(n,o,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){var o,i;(i=(o=this._platformLocation).historyGo)==null||i.call(o,n)}};u(e,"\u0275fac",function(o){return new(o||e)(k($o),k(Ta,8))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();var qd=/\s+/,Pv=[],kS=(()=>{let e=class e{_ngEl;_renderer;initialClasses=Pv;rawClass;stateMap=new Map;constructor(n,o){this._ngEl=n,this._renderer=o}set klass(n){this.initialClasses=n!=null?n.trim().split(qd):Pv}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(qd):n}ngDoCheck(){for(let o of this.initialClasses)this._updateState(o,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let o of n)this._updateState(o,!0);else if(n!=null)for(let o of Object.keys(n))this._updateState(o,!!n[o]);this._applyStateDiff()}_updateState(n,o){let i=this.stateMap.get(n);i!==void 0?(i.enabled!==o&&(i.changed=!0,i.enabled=o),i.touched=!0):this.stateMap.set(n,{enabled:o,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let o=n[0],i=n[1];i.changed?(this._toggleClass(o,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(o,!1),this.stateMap.delete(o)),i.touched=!1}}_toggleClass(n,o){n=n.trim(),n.length>0&&n.split(qd).forEach(i=>{o?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}};u(e,"\u0275fac",function(o){return new(o||e)(f(y),f(rn))}),u(e,"\u0275dir",B({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}}));let t=e;return t})();var xa=class{$implicit;ngForOf;index;count;constructor(e,r,n,o){this.$implicit=e,this.ngForOf=r,this.index=n,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Lv=(()=>{let e=class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,o,i){this._viewContainer=n,this._template=o,this._differs=i}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let o=this._viewContainer;n.forEachOperation((i,s,a)=>{if(i.previousIndex==null)o.createEmbeddedView(this._template,new xa(i.item,this._ngForOf,-1,-1),a===null?void 0:a);else if(a==null)o.remove(s===null?void 0:s);else if(s!==null){let c=o.get(s);o.move(c,a),Fv(c,i)}});for(let i=0,s=o.length;i<s;i++){let c=o.get(i).context;c.index=i,c.count=s,c.ngForOf=this._ngForOf}n.forEachIdentityChange(i=>{let s=o.get(i.currentIndex);Fv(s,i)})}static ngTemplateContextGuard(n,o){return!0}};u(e,"\u0275fac",function(o){return new(o||e)(f(ze),f(bt),f(zd))}),u(e,"\u0275dir",B({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}}));let t=e;return t})();function Fv(t,e){t.context.$implicit=e.item}var zo=(()=>{let e=class e{_viewContainer;_context=new Aa;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,o){this._viewContainer=n,this._thenTemplateRef=o}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){jv(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){jv(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,o){return!0}};u(e,"ngIfUseIfTypeGuard"),u(e,"ngTemplateGuard_ngIf"),u(e,"\u0275fac",function(o){return new(o||e)(f(ze),f(bt))}),u(e,"\u0275dir",B({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}}));let t=e;return t})(),Aa=class{$implicit=null;ngIf=null};function jv(t,e){if(t&&!t.createEmbeddedView)throw new R(2020,!1)}var Ra=(()=>{let e=class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(n){this._viewContainerRef=n}ngOnChanges(n){var o;if(this._shouldRecreateView(n)){let i=this._viewContainerRef;if(this._viewRef&&i.remove(i.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let s=this._createContextForwardProxy();this._viewRef=i.createEmbeddedView(this.ngTemplateOutlet,s,{injector:(o=this.ngTemplateOutletInjector)!=null?o:void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,o,i)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,o,i):!1,get:(n,o,i)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,o,i)}})}};u(e,"\u0275fac",function(o){return new(o||e)(f(ze))}),u(e,"\u0275dir",B({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[tt]}));let t=e;return t})();var Go=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Ae({type:e})),u(e,"\u0275inj",Te({}));let t=e;return t})();function Wo(t,e){e=encodeURIComponent(e);for(let r of t.split(";")){let n=r.indexOf("="),[o,i]=n==-1?[r,""]:[r.slice(0,n),r.slice(n+1)];if(o.trim()===e)return decodeURIComponent(i)}return null}var Yd="browser",Vv="server";function Na(t){return t===Vv}var Bn=class{};var Bv=(()=>{let e=class e{};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>new Qd(I(ce),window)}));let t=e;return t})(),Qd=class{document;window;offset=()=>[0,0];constructor(e,r){this.document=e,this.window=r}setOffset(e){Array.isArray(e)?this.offset=()=>e:this.offset=e}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(e){this.window.scrollTo(e[0],e[1])}scrollToAnchor(e){let r=FS(this.document,e);r&&(this.scrollToElement(r),r.focus())}setHistoryScrollRestoration(e){this.window.history.scrollRestoration=e}scrollToElement(e){let r=e.getBoundingClientRect(),n=r.left+this.window.pageXOffset,o=r.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(n-i[0],o-i[1])}};function FS(t,e){let r=t.getElementById(e)||t.getElementsByName(e)[0];if(r)return r;if(typeof t.createTreeWalker=="function"&&t.body&&typeof t.body.attachShadow=="function"){let n=t.createTreeWalker(t.body,NodeFilter.SHOW_ELEMENT),o=n.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(e)||i.querySelector(`[name="${e}"]`);if(s)return s}o=n.nextNode()}}return null}var Pa=new N(""),tf=(()=>{let e=class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,o){this._zone=o,n.forEach(i=>{i.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,o,i,s){return this._findPluginFor(o).addEventListener(n,o,i,s)}getZone(){return this._zone}_findPluginFor(n){let o=this._eventNameToPlugin.get(n);if(o)return o;if(o=this._plugins.find(s=>s.supports(n)),!o)throw new R(5101,!1);return this._eventNameToPlugin.set(n,o),o}};u(e,"\u0275fac",function(o){return new(o||e)(k(Pa),k(m))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),qo=class{_doc;constructor(e){this._doc=e}manager},Oa="ng-app-id";function Uv(t){for(let e of t)e.remove()}function Hv(t,e){let r=e.createElement("style");return r.textContent=t,r}function jS(t,e,r,n){var i;let o=(i=t.head)==null?void 0:i.querySelectorAll(`style[${Oa}="${e}"],link[${Oa}="${e}"]`);if(o)for(let s of o)s.removeAttribute(Oa),s instanceof HTMLLinkElement?n.set(s.href.slice(s.href.lastIndexOf("/")+1),{usage:0,elements:[s]}):s.textContent&&r.set(s.textContent,{usage:0,elements:[s]})}function Jd(t,e){let r=e.createElement("link");return r.setAttribute("rel","stylesheet"),r.setAttribute("href",t),r}var nf=(()=>{let e=class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,o,i,s={}){this.doc=n,this.appId=o,this.nonce=i,this.isServer=Na(s),jS(n,o,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,o){for(let i of n)this.addUsage(i,this.inline,Hv);o==null||o.forEach(i=>this.addUsage(i,this.external,Jd))}removeStyles(n,o){for(let i of n)this.removeUsage(i,this.inline);o==null||o.forEach(i=>this.removeUsage(i,this.external))}addUsage(n,o,i){let s=o.get(n);s?s.usage++:o.set(n,{usage:1,elements:[...this.hosts].map(a=>this.addElement(a,i(n,this.doc)))})}removeUsage(n,o){let i=o.get(n);i&&(i.usage--,i.usage<=0&&(Uv(i.elements),o.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])Uv(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[o,{elements:i}]of this.inline)i.push(this.addElement(n,Hv(o,this.doc)));for(let[o,{elements:i}]of this.external)i.push(this.addElement(n,Jd(o,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,o){return this.nonce&&o.setAttribute("nonce",this.nonce),this.isServer&&o.setAttribute(Oa,this.appId),n.appendChild(o)}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce),k(fd),k(pd,8),k(nn))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),Xd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},rf=/%COMP%/g;var zv="%COMP%",LS=`_nghost-${zv}`,VS=`_ngcontent-${zv}`,BS=!0,US=new N("",{providedIn:"root",factory:()=>BS});function HS(t){return VS.replace(rf,t)}function $S(t){return LS.replace(rf,t)}function Gv(t,e){return e.map(r=>r.replace(rf,t))}var of=(()=>{let e=class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,o,i,s,a,c,l,d=null,h=null){this.eventManager=n,this.sharedStylesHost=o,this.appId=i,this.removeStylesOnCompDestroy=s,this.doc=a,this.platformId=c,this.ngZone=l,this.nonce=d,this.tracingService=h,this.platformIsServer=Na(c),this.defaultRenderer=new Zo(n,a,l,this.platformIsServer,this.tracingService)}createRenderer(n,o){if(!n||!o)return this.defaultRenderer;this.platformIsServer&&o.encapsulation===Ct.ShadowDom&&(o=L(b({},o),{encapsulation:Ct.Emulated}));let i=this.getOrCreateRenderer(n,o);return i instanceof ka?i.applyToHost(n):i instanceof Yo&&i.applyStyles(),i}getOrCreateRenderer(n,o){let i=this.rendererByCompId,s=i.get(o.id);if(!s){let a=this.doc,c=this.ngZone,l=this.eventManager,d=this.sharedStylesHost,h=this.removeStylesOnCompDestroy,g=this.platformIsServer,p=this.tracingService;switch(o.encapsulation){case Ct.Emulated:s=new ka(l,d,o,this.appId,h,a,c,g,p);break;case Ct.ShadowDom:return new ef(l,d,n,o,a,c,this.nonce,g,p);default:s=new Yo(l,d,o,h,a,c,g,p);break}i.set(o.id,s)}return s}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}};u(e,"\u0275fac",function(o){return new(o||e)(k(tf),k(nf),k(fd),k(US),k(ce),k(nn),k(m),k(pd),k(Fr,8))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),Zo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(e,r,n,o,i){this.eventManager=e,this.doc=r,this.ngZone=n,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(e,r){return r?this.doc.createElementNS(Xd[r]||r,e):this.doc.createElement(e)}createComment(e){return this.doc.createComment(e)}createText(e){return this.doc.createTextNode(e)}appendChild(e,r){($v(e)?e.content:e).appendChild(r)}insertBefore(e,r,n){e&&($v(e)?e.content:e).insertBefore(r,n)}removeChild(e,r){r.remove()}selectRootElement(e,r){let n=typeof e=="string"?this.doc.querySelector(e):e;if(!n)throw new R(-5104,!1);return r||(n.textContent=""),n}parentNode(e){return e.parentNode}nextSibling(e){return e.nextSibling}setAttribute(e,r,n,o){if(o){r=o+":"+r;let i=Xd[o];i?e.setAttributeNS(i,r,n):e.setAttribute(r,n)}else e.setAttribute(r,n)}removeAttribute(e,r,n){if(n){let o=Xd[n];o?e.removeAttributeNS(o,r):e.removeAttribute(`${n}:${r}`)}else e.removeAttribute(r)}addClass(e,r){e.classList.add(r)}removeClass(e,r){e.classList.remove(r)}setStyle(e,r,n,o){o&(Lt.DashCase|Lt.Important)?e.style.setProperty(r,n,o&Lt.Important?"important":""):e.style[r]=n}removeStyle(e,r,n){n&Lt.DashCase?e.style.removeProperty(r):e.style[r]=""}setProperty(e,r,n){e!=null&&(e[r]=n)}setValue(e,r){e.nodeValue=r}listen(e,r,n,o){var s;if(typeof e=="string"&&(e=Ge().getGlobalEventTarget(this.doc,e),!e))throw new R(5102,!1);let i=this.decoratePreventDefault(n);return(s=this.tracingService)!=null&&s.wrapEventListener&&(i=this.tracingService.wrapEventListener(e,r,i)),this.eventManager.addEventListener(e,r,i,o)}decoratePreventDefault(e){return r=>{if(r==="__ngUnwrap__")return e;(this.platformIsServer?this.ngZone.runGuarded(()=>e(r)):e(r))===!1&&r.preventDefault()}}};function $v(t){return t.tagName==="TEMPLATE"&&t.content!==void 0}var ef=class extends Zo{sharedStylesHost;hostEl;shadowRoot;constructor(e,r,n,o,i,s,a,c,l){var p;super(e,i,s,c,l),this.sharedStylesHost=r,this.hostEl=n,this.shadowRoot=n.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let d=o.styles;d=Gv(o.id,d);for(let v of d){let D=document.createElement("style");a&&D.setAttribute("nonce",a),D.textContent=v,this.shadowRoot.appendChild(D)}let h=(p=o.getExternalStyles)==null?void 0:p.call(o);if(h)for(let v of h){let D=Jd(v,i);a&&D.setAttribute("nonce",a),this.shadowRoot.appendChild(D)}}nodeOrShadowRoot(e){return e===this.hostEl?this.shadowRoot:e}appendChild(e,r){return super.appendChild(this.nodeOrShadowRoot(e),r)}insertBefore(e,r,n){return super.insertBefore(this.nodeOrShadowRoot(e),r,n)}removeChild(e,r){return super.removeChild(null,r)}parentNode(e){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(e)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Yo=class extends Zo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(e,r,n,o,i,s,a,c,l){var g;super(e,i,s,a,c),this.sharedStylesHost=r,this.removeStylesOnCompDestroy=o;let d=n.styles;this.styles=l?Gv(l,d):d,this.styleUrls=(g=n.getExternalStyles)==null?void 0:g.call(n,l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},ka=class extends Yo{contentAttr;hostAttr;constructor(e,r,n,o,i,s,a,c,l){let d=o+"-"+n.id;super(e,r,n,i,s,a,c,l,d),this.contentAttr=HS(d),this.hostAttr=$S(d)}applyToHost(e){this.applyStyles(),this.setAttribute(e,this.hostAttr,"")}createElement(e,r){let n=super.createElement(e,r);return super.setAttribute(n,this.contentAttr,""),n}};var Fa=class t extends Ho{supportsDOMEvents=!0;static makeCurrent(){Gd(new t)}onAndCancel(e,r,n,o){return e.addEventListener(r,n,o),()=>{e.removeEventListener(r,n,o)}}dispatchEvent(e,r){e.dispatchEvent(r)}remove(e){e.remove()}createElement(e,r){return r=r||this.getDefaultDocument(),r.createElement(e)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(e){return e.nodeType===Node.ELEMENT_NODE}isShadowRoot(e){return e instanceof DocumentFragment}getGlobalEventTarget(e,r){return r==="window"?window:r==="document"?e:r==="body"?e.body:null}getBaseHref(e){let r=zS();return r==null?null:GS(r)}resetBaseElement(){Qo=null}getUserAgent(){return window.navigator.userAgent}getCookie(e){return Wo(document.cookie,e)}},Qo=null;function zS(){return Qo=Qo||document.querySelector("base"),Qo?Qo.getAttribute("href"):null}function GS(t){return new URL(t,document.baseURI).pathname}var ja=class{addToWindow(e){Pe.getAngularTestability=(n,o=!0)=>{let i=e.findTestabilityInTree(n,o);if(i==null)throw new R(5103,!1);return i},Pe.getAllAngularTestabilities=()=>e.getAllTestabilities(),Pe.getAllAngularRootElements=()=>e.getAllRootElements();let r=n=>{let o=Pe.getAllAngularTestabilities(),i=o.length,s=function(){i--,i==0&&n()};o.forEach(a=>{a.whenStable(s)})};Pe.frameworkStabilizers||(Pe.frameworkStabilizers=[]),Pe.frameworkStabilizers.push(r)}findTestabilityInTree(e,r,n){if(r==null)return null;let o=e.getTestability(r);return o!=null?o:n?Ge().isShadowRoot(r)?this.findTestabilityInTree(e,r.host,!0):this.findTestabilityInTree(e,r.parentElement,!0):null}},WS=(()=>{let e=class e{build(){return new XMLHttpRequest}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),qv=(()=>{let e=class e extends qo{constructor(n){super(n)}supports(n){return!0}addEventListener(n,o,i,s){return n.addEventListener(o,i,s),()=>this.removeEventListener(n,o,i,s)}removeEventListener(n,o,i,s){return n.removeEventListener(o,i,s)}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),Wv=["alt","control","meta","shift"],qS={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},ZS={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey},Zv=(()=>{let e=class e extends qo{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,o,i,s){let a=e.parseEventName(o),c=e.eventCallback(a.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Ge().onAndCancel(n,a.domEventName,c,s))}static parseEventName(n){let o=n.toLowerCase().split("."),i=o.shift();if(o.length===0||!(i==="keydown"||i==="keyup"))return null;let s=e._normalizeKey(o.pop()),a="",c=o.indexOf("code");if(c>-1&&(o.splice(c,1),a="code."),Wv.forEach(d=>{let h=o.indexOf(d);h>-1&&(o.splice(h,1),a+=d+".")}),a+=s,o.length!=0||s.length===0)return null;let l={};return l.domEventName=i,l.fullKey=a,l}static matchEventFullKeyCode(n,o){let i=qS[n.key]||n.key,s="";return o.indexOf("code.")>-1&&(i=n.code,s="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),Wv.forEach(a=>{if(a!==i){let c=ZS[a];c(n)&&(s+=a+".")}}),s+=i,s===o)}static eventCallback(n,o,i){return s=>{e.matchEventFullKeyCode(s,n)&&i.runGuarded(()=>o(s))}}static _normalizeKey(n){return n==="esc"?"escape":n}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();function YS(){Fa.makeCurrent()}function QS(){return new It}function KS(){return dm(document),document}var XS=[{provide:nn,useValue:Yd},{provide:hd,useValue:YS,multi:!0},{provide:ce,useFactory:KS}],JS=$d(xv,"browser",XS);var eT=[{provide:Lo,useClass:ja},{provide:Ld,useClass:ma,deps:[m,va,Lo]},{provide:ma,useClass:ma,deps:[m,va,Lo]}],tT=[{provide:Js,useValue:"root"},{provide:It,useFactory:QS},{provide:Pa,useClass:qv,multi:!0,deps:[ce]},{provide:Pa,useClass:Zv,multi:!0,deps:[ce]},of,nf,tf,{provide:Tr,useExisting:of},{provide:Bn,useClass:WS},[]],nT=(()=>{let e=class e{constructor(){}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Ae({type:e})),u(e,"\u0275inj",Te({providers:[...tT,...eT],imports:[Go,Av]}));let t=e;return t})();var Br=class{},Ko=class{},cn=class t{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(e){e?typeof e=="string"?this.lazyInit=()=>{this.headers=new Map,e.split(`
`).forEach(r=>{let n=r.indexOf(":");if(n>0){let o=r.slice(0,n),i=r.slice(n+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&e instanceof Headers?(this.headers=new Map,e.forEach((r,n)=>{this.addHeaderEntry(n,r)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(e).forEach(([r,n])=>{this.setHeaderEntries(r,n)})}:this.headers=new Map}has(e){return this.init(),this.headers.has(e.toLowerCase())}get(e){this.init();let r=this.headers.get(e.toLowerCase());return r&&r.length>0?r[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(e){return this.init(),this.headers.get(e.toLowerCase())||null}append(e,r){return this.clone({name:e,value:r,op:"a"})}set(e,r){return this.clone({name:e,value:r,op:"s"})}delete(e,r){return this.clone({name:e,value:r,op:"d"})}maybeSetNormalizedName(e,r){this.normalizedNames.has(r)||this.normalizedNames.set(r,e)}init(){this.lazyInit&&(this.lazyInit instanceof t?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(e=>this.applyUpdate(e)),this.lazyUpdate=null))}copyFrom(e){e.init(),Array.from(e.headers.keys()).forEach(r=>{this.headers.set(r,e.headers.get(r)),this.normalizedNames.set(r,e.normalizedNames.get(r))})}clone(e){let r=new t;return r.lazyInit=this.lazyInit&&this.lazyInit instanceof t?this.lazyInit:this,r.lazyUpdate=(this.lazyUpdate||[]).concat([e]),r}applyUpdate(e){let r=e.name.toLowerCase();switch(e.op){case"a":case"s":let n=e.value;if(typeof n=="string"&&(n=[n]),n.length===0)return;this.maybeSetNormalizedName(e.name,r);let o=(e.op==="a"?this.headers.get(r):void 0)||[];o.push(...n),this.headers.set(r,o);break;case"d":let i=e.value;if(!i)this.headers.delete(r),this.normalizedNames.delete(r);else{let s=this.headers.get(r);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(r),this.normalizedNames.delete(r)):this.headers.set(r,s)}break}}addHeaderEntry(e,r){let n=e.toLowerCase();this.maybeSetNormalizedName(e,n),this.headers.has(n)?this.headers.get(n).push(r):this.headers.set(n,[r])}setHeaderEntries(e,r){let n=(Array.isArray(r)?r:[r]).map(i=>i.toString()),o=e.toLowerCase();this.headers.set(o,n),this.maybeSetNormalizedName(e,o)}forEach(e){this.init(),Array.from(this.normalizedNames.keys()).forEach(r=>e(this.normalizedNames.get(r),this.headers.get(r)))}};var Va=class{encodeKey(e){return Yv(e)}encodeValue(e){return Yv(e)}decodeKey(e){return decodeURIComponent(e)}decodeValue(e){return decodeURIComponent(e)}};function rT(t,e){let r=new Map;return t.length>0&&t.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[e.decodeKey(o),""]:[e.decodeKey(o.slice(0,i)),e.decodeValue(o.slice(i+1))],c=r.get(s)||[];c.push(a),r.set(s,c)}),r}var oT=/%(\d[a-f0-9])/gi,iT={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Yv(t){return encodeURIComponent(t).replace(oT,(e,r)=>{var n;return(n=iT[r])!=null?n:e})}function La(t){return`${t}`}var zt=class t{map;encoder;updates=null;cloneFrom=null;constructor(e={}){if(this.encoder=e.encoder||new Va,e.fromString){if(e.fromObject)throw new R(2805,!1);this.map=rT(e.fromString,this.encoder)}else e.fromObject?(this.map=new Map,Object.keys(e.fromObject).forEach(r=>{let n=e.fromObject[r],o=Array.isArray(n)?n.map(La):[La(n)];this.map.set(r,o)})):this.map=null}has(e){return this.init(),this.map.has(e)}get(e){this.init();let r=this.map.get(e);return r?r[0]:null}getAll(e){return this.init(),this.map.get(e)||null}keys(){return this.init(),Array.from(this.map.keys())}append(e,r){return this.clone({param:e,value:r,op:"a"})}appendAll(e){let r=[];return Object.keys(e).forEach(n=>{let o=e[n];Array.isArray(o)?o.forEach(i=>{r.push({param:n,value:i,op:"a"})}):r.push({param:n,value:o,op:"a"})}),this.clone(r)}set(e,r){return this.clone({param:e,value:r,op:"s"})}delete(e,r){return this.clone({param:e,value:r,op:"d"})}toString(){return this.init(),this.keys().map(e=>{let r=this.encoder.encodeKey(e);return this.map.get(e).map(n=>r+"="+this.encoder.encodeValue(n)).join("&")}).filter(e=>e!=="").join("&")}clone(e){let r=new t({encoder:this.encoder});return r.cloneFrom=this.cloneFrom||this,r.updates=(this.updates||[]).concat(e),r}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(e=>this.map.set(e,this.cloneFrom.map.get(e))),this.updates.forEach(e=>{switch(e.op){case"a":case"s":let r=(e.op==="a"?this.map.get(e.param):void 0)||[];r.push(La(e.value)),this.map.set(e.param,r);break;case"d":if(e.value!==void 0){let n=this.map.get(e.param)||[],o=n.indexOf(La(e.value));o!==-1&&n.splice(o,1),n.length>0?this.map.set(e.param,n):this.map.delete(e.param)}else{this.map.delete(e.param);break}}}),this.cloneFrom=this.updates=null)}};var Ba=class{map=new Map;set(e,r){return this.map.set(e,r),this}get(e){return this.map.has(e)||this.map.set(e,e.defaultValue()),this.map.get(e)}delete(e){return this.map.delete(e),this}has(e){return this.map.has(e)}keys(){return this.map.keys()}};function sT(t){switch(t){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Qv(t){return typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer}function Kv(t){return typeof Blob<"u"&&t instanceof Blob}function Xv(t){return typeof FormData<"u"&&t instanceof FormData}function aT(t){return typeof URLSearchParams<"u"&&t instanceof URLSearchParams}var Jv="Content-Type",ey="Accept",ny="X-Request-URL",ry="text/plain",oy="application/json",cT=`${oy}, ${ry}, */*`,Vr=class t{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(e,r,n,o){var s,a;this.url=r,this.method=e.toUpperCase();let i;if(sT(this.method)||o?(this.body=n!==void 0?n:null,i=o):i=n,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),(s=this.headers)!=null||(this.headers=new cn),(a=this.context)!=null||(this.context=new Ba),!this.params)this.params=new zt,this.urlWithParams=r;else{let c=this.params.toString();if(c.length===0)this.urlWithParams=r;else{let l=r.indexOf("?"),d=l===-1?"?":l<r.length-1?"&":"";this.urlWithParams=r+d+c}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Qv(this.body)||Kv(this.body)||Xv(this.body)||aT(this.body)?this.body:this.body instanceof zt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Xv(this.body)?null:Kv(this.body)?this.body.type||null:Qv(this.body)?null:typeof this.body=="string"?ry:this.body instanceof zt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?oy:null}clone(e={}){var g,p,v,D;let r=e.method||this.method,n=e.url||this.url,o=e.responseType||this.responseType,i=(g=e.transferCache)!=null?g:this.transferCache,s=e.body!==void 0?e.body:this.body,a=(p=e.withCredentials)!=null?p:this.withCredentials,c=(v=e.reportProgress)!=null?v:this.reportProgress,l=e.headers||this.headers,d=e.params||this.params,h=(D=e.context)!=null?D:this.context;return e.setHeaders!==void 0&&(l=Object.keys(e.setHeaders).reduce((x,O)=>x.set(O,e.setHeaders[O]),l)),e.setParams&&(d=Object.keys(e.setParams).reduce((x,O)=>x.set(O,e.setParams[O]),d)),new t(r,n,s,{params:d,headers:l,context:h,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},Un=function(t){return t[t.Sent=0]="Sent",t[t.UploadProgress=1]="UploadProgress",t[t.ResponseHeader=2]="ResponseHeader",t[t.DownloadProgress=3]="DownloadProgress",t[t.Response=4]="Response",t[t.User=5]="User",t}(Un||{}),Ur=class{headers;status;statusText;url;ok;type;constructor(e,r=200,n="OK"){this.headers=e.headers||new cn,this.status=e.status!==void 0?e.status:r,this.statusText=e.statusText||n,this.url=e.url||null,this.ok=this.status>=200&&this.status<300}},Ua=class t extends Ur{constructor(e={}){super(e)}type=Un.ResponseHeader;clone(e={}){return new t({headers:e.headers||this.headers,status:e.status!==void 0?e.status:this.status,statusText:e.statusText||this.statusText,url:e.url||this.url||void 0})}},Xo=class t extends Ur{body;constructor(e={}){super(e),this.body=e.body!==void 0?e.body:null}type=Un.Response;clone(e={}){return new t({body:e.body!==void 0?e.body:this.body,headers:e.headers||this.headers,status:e.status!==void 0?e.status:this.status,statusText:e.statusText||this.statusText,url:e.url||this.url||void 0})}},Jo=class extends Ur{name="HttpErrorResponse";message;error;ok=!1;constructor(e){super(e,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${e.url||"(unknown url)"}`:this.message=`Http failure response for ${e.url||"(unknown url)"}: ${e.status} ${e.statusText}`,this.error=e.error||null}},lT=200,uT=204;function sf(t,e){return{body:e,headers:t.headers,context:t.context,observe:t.observe,params:t.params,reportProgress:t.reportProgress,responseType:t.responseType,withCredentials:t.withCredentials,transferCache:t.transferCache}}var iy=(()=>{let e=class e{handler;constructor(n){this.handler=n}request(n,o,i={}){let s;if(n instanceof Vr)s=n;else{let l;i.headers instanceof cn?l=i.headers:l=new cn(i.headers);let d;i.params&&(i.params instanceof zt?d=i.params:d=new zt({fromObject:i.params})),s=new Vr(n,o,i.body!==void 0?i.body:null,{headers:l,context:i.context,params:d,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let a=P(s).pipe(gt(l=>this.handler.handle(l)));if(n instanceof Vr||i.observe==="events")return a;let c=a.pipe(ye(l=>l instanceof Xo));switch(i.observe||"body"){case"body":switch(s.responseType){case"arraybuffer":return c.pipe(H(l=>{if(l.body!==null&&!(l.body instanceof ArrayBuffer))throw new R(2806,!1);return l.body}));case"blob":return c.pipe(H(l=>{if(l.body!==null&&!(l.body instanceof Blob))throw new R(2807,!1);return l.body}));case"text":return c.pipe(H(l=>{if(l.body!==null&&typeof l.body!="string")throw new R(2808,!1);return l.body}));case"json":default:return c.pipe(H(l=>l.body))}case"response":return c;default:throw new R(2809,!1)}}delete(n,o={}){return this.request("DELETE",n,o)}get(n,o={}){return this.request("GET",n,o)}head(n,o={}){return this.request("HEAD",n,o)}jsonp(n,o){return this.request("JSONP",n,{params:new zt().append(o,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,o={}){return this.request("OPTIONS",n,o)}patch(n,o,i={}){return this.request("PATCH",n,sf(i,o))}post(n,o,i={}){return this.request("POST",n,sf(i,o))}put(n,o,i={}){return this.request("PUT",n,sf(i,o))}};u(e,"\u0275fac",function(o){return new(o||e)(k(Br))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();var dT=new N("");function sy(t,e){return e(t)}function fT(t,e){return(r,n)=>e.intercept(r,{handle:o=>t(o,n)})}function hT(t,e,r){return(n,o)=>je(r,()=>e(n,i=>t(i,o)))}var ay=new N(""),cf=new N(""),cy=new N(""),lf=new N("",{providedIn:"root",factory:()=>!0});function pT(){let t=null;return(e,r)=>{var i;t===null&&(t=((i=I(ay,{optional:!0}))!=null?i:[]).reduceRight(fT,sy));let n=I(Ut);if(I(lf)){let s=n.add();return t(e,r).pipe(Kt(()=>n.remove(s)))}else return t(e,r)}}var Ha=(()=>{let e=class e extends Br{backend;injector;chain=null;pendingTasks=I(Ut);contributeToStability=I(lf);constructor(n,o){super(),this.backend=n,this.injector=o}handle(n){if(this.chain===null){let o=Array.from(new Set([...this.injector.get(cf),...this.injector.get(cy,[])]));this.chain=o.reduceRight((i,s)=>hT(i,s,this.injector),sy)}if(this.contributeToStability){let o=this.pendingTasks.add();return this.chain(n,i=>this.backend.handle(i)).pipe(Kt(()=>this.pendingTasks.remove(o)))}else return this.chain(n,o=>this.backend.handle(o))}};u(e,"\u0275fac",function(o){return new(o||e)(k(Ko),k(se))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();var gT=/^\)\]\}',?\n/,mT=RegExp(`^${ny}:`,"m");function vT(t){return"responseURL"in t&&t.responseURL?t.responseURL:mT.test(t.getAllResponseHeaders())?t.getResponseHeader(ny):null}var af=(()=>{let e=class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new R(-2800,!1);let o=this.xhrFactory;return(o.\u0275loadImpl?re(o.\u0275loadImpl()):P(null)).pipe(De(()=>new q(s=>{let a=o.build();if(a.open(n.method,n.urlWithParams),n.withCredentials&&(a.withCredentials=!0),n.headers.forEach((x,O)=>a.setRequestHeader(x,O.join(","))),n.headers.has(ey)||a.setRequestHeader(ey,cT),!n.headers.has(Jv)){let x=n.detectContentTypeHeader();x!==null&&a.setRequestHeader(Jv,x)}if(n.responseType){let x=n.responseType.toLowerCase();a.responseType=x!=="json"?x:"text"}let c=n.serializeBody(),l=null,d=()=>{if(l!==null)return l;let x=a.statusText||"OK",O=new cn(a.getAllResponseHeaders()),X=vT(a)||n.url;return l=new Ua({headers:O,status:a.status,statusText:x,url:X}),l},h=()=>{let{headers:x,status:O,statusText:X,url:Ue}=d(),he=null;O!==uT&&(he=typeof a.response>"u"?a.responseText:a.response),O===0&&(O=he?lT:0);let Ot=O>=200&&O<300;if(n.responseType==="json"&&typeof he=="string"){let Ri=he;he=he.replace(gT,"");try{he=he!==""?JSON.parse(he):null}catch(Qn){he=Ri,Ot&&(Ot=!1,he={error:Qn,text:he})}}Ot?(s.next(new Xo({body:he,headers:x,status:O,statusText:X,url:Ue||void 0})),s.complete()):s.error(new Jo({error:he,headers:x,status:O,statusText:X,url:Ue||void 0}))},g=x=>{let{url:O}=d(),X=new Jo({error:x,status:a.status||0,statusText:a.statusText||"Unknown Error",url:O||void 0});s.error(X)},p=!1,v=x=>{p||(s.next(d()),p=!0);let O={type:Un.DownloadProgress,loaded:x.loaded};x.lengthComputable&&(O.total=x.total),n.responseType==="text"&&a.responseText&&(O.partialText=a.responseText),s.next(O)},D=x=>{let O={type:Un.UploadProgress,loaded:x.loaded};x.lengthComputable&&(O.total=x.total),s.next(O)};return a.addEventListener("load",h),a.addEventListener("error",g),a.addEventListener("timeout",g),a.addEventListener("abort",g),n.reportProgress&&(a.addEventListener("progress",v),c!==null&&a.upload&&a.upload.addEventListener("progress",D)),a.send(c),s.next({type:Un.Sent}),()=>{a.removeEventListener("error",g),a.removeEventListener("abort",g),a.removeEventListener("load",h),a.removeEventListener("timeout",g),n.reportProgress&&(a.removeEventListener("progress",v),c!==null&&a.upload&&a.upload.removeEventListener("progress",D)),a.readyState!==a.DONE&&a.abort()}})))}};u(e,"\u0275fac",function(o){return new(o||e)(k(Bn))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),ly=new N(""),yT="XSRF-TOKEN",DT=new N("",{providedIn:"root",factory:()=>yT}),IT="X-XSRF-TOKEN",CT=new N("",{providedIn:"root",factory:()=>IT}),ei=class{},bT=(()=>{let e=class e{doc;platform;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,o,i){this.doc=n,this.platform=o,this.cookieName=i}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Wo(n,this.cookieName),this.lastCookieString=n),this.lastToken}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce),k(nn),k(DT))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();function ET(t,e){let r=t.url.toLowerCase();if(!I(ly)||t.method==="GET"||t.method==="HEAD"||r.startsWith("http://")||r.startsWith("https://"))return e(t);let n=I(ei).getToken(),o=I(CT);return n!=null&&!t.headers.has(o)&&(t=t.clone({headers:t.headers.set(o,n)})),e(t)}var uf=function(t){return t[t.Interceptors=0]="Interceptors",t[t.LegacyInterceptors=1]="LegacyInterceptors",t[t.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",t[t.NoXsrfProtection=3]="NoXsrfProtection",t[t.JsonpSupport=4]="JsonpSupport",t[t.RequestsMadeViaParent=5]="RequestsMadeViaParent",t[t.Fetch=6]="Fetch",t}(uf||{});function wT(t,e){return{\u0275kind:t,\u0275providers:e}}function uy(...t){let e=[iy,af,Ha,{provide:Br,useExisting:Ha},{provide:Ko,useFactory:()=>{var r;return(r=I(dT,{optional:!0}))!=null?r:I(af)}},{provide:cf,useValue:ET,multi:!0},{provide:ly,useValue:!0},{provide:ei,useClass:bT}];for(let r of t)e.push(...r.\u0275providers);return Xs(e)}var ty=new N("");function dy(){return wT(uf.LegacyInterceptors,[{provide:ty,useFactory:pT},{provide:cf,useExisting:ty,multi:!0}])}var MT=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Ae({type:e})),u(e,"\u0275inj",Te({providers:[uy(dy())]}));let t=e;return t})();var fy=(()=>{let e=class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();var j="primary",hi=Symbol("RouteTitle"),gf=class{params;constructor(e){this.params=e||{}}has(e){return Object.prototype.hasOwnProperty.call(this.params,e)}get(e){if(this.has(e)){let r=this.params[e];return Array.isArray(r)?r[0]:r}return null}getAll(e){if(this.has(e)){let r=this.params[e];return Array.isArray(r)?r:[r]}return[]}get keys(){return Object.keys(this.params)}};function zn(t){return new gf(t)}function Iy(t,e,r){let n=r.path.split("/");if(n.length>t.length||r.pathMatch==="full"&&(e.hasChildren()||n.length<t.length))return null;let o={};for(let i=0;i<n.length;i++){let s=n[i],a=t[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:t.slice(0,n.length),posParams:o}}function TT(t,e){if(t.length!==e.length)return!1;for(let r=0;r<t.length;++r)if(!Tt(t[r],e[r]))return!1;return!0}function Tt(t,e){let r=t?mf(t):void 0,n=e?mf(e):void 0;if(!r||!n||r.length!=n.length)return!1;let o;for(let i=0;i<r.length;i++)if(o=r[i],!Cy(t[o],e[o]))return!1;return!0}function mf(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function Cy(t,e){if(Array.isArray(t)&&Array.isArray(e)){if(t.length!==e.length)return!1;let r=[...t].sort(),n=[...e].sort();return r.every((o,i)=>n[i]===o)}else return t===e}function by(t){return t.length>0?t[t.length-1]:null}function fn(t){return cl(t)?t:Vn(t)?re(Promise.resolve(t)):P(t)}var _T={exact:wy,subset:My},Ey={exact:xT,subset:AT,ignored:()=>!0};function hy(t,e,r){return _T[r.paths](t.root,e.root,r.matrixParams)&&Ey[r.queryParams](t.queryParams,e.queryParams)&&!(r.fragment==="exact"&&t.fragment!==e.fragment)}function xT(t,e){return Tt(t,e)}function wy(t,e,r){if(!Hn(t.segments,e.segments)||!Ga(t.segments,e.segments,r)||t.numberOfChildren!==e.numberOfChildren)return!1;for(let n in e.children)if(!t.children[n]||!wy(t.children[n],e.children[n],r))return!1;return!0}function AT(t,e){return Object.keys(e).length<=Object.keys(t).length&&Object.keys(e).every(r=>Cy(t[r],e[r]))}function My(t,e,r){return Sy(t,e,e.segments,r)}function Sy(t,e,r,n){if(t.segments.length>r.length){let o=t.segments.slice(0,r.length);return!(!Hn(o,r)||e.hasChildren()||!Ga(o,r,n))}else if(t.segments.length===r.length){if(!Hn(t.segments,r)||!Ga(t.segments,r,n))return!1;for(let o in e.children)if(!t.children[o]||!My(t.children[o],e.children[o],n))return!1;return!0}else{let o=r.slice(0,t.segments.length),i=r.slice(t.segments.length);return!Hn(t.segments,o)||!Ga(t.segments,o,n)||!t.children[j]?!1:Sy(t.children[j],e,i,n)}}function Ga(t,e,r){return e.every((n,o)=>Ey[r](t[o].parameters,n.parameters))}var xt=class{root;queryParams;fragment;_queryParamMap;constructor(e=new K([],{}),r={},n=null){this.root=e,this.queryParams=r,this.fragment=n}get queryParamMap(){var e;return(e=this._queryParamMap)!=null||(this._queryParamMap=zn(this.queryParams)),this._queryParamMap}toString(){return OT.serialize(this)}},K=class{segments;children;parent=null;constructor(e,r){this.segments=e,this.children=r,Object.values(r).forEach(n=>n.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Wa(this)}},ln=class{path;parameters;_parameterMap;constructor(e,r){this.path=e,this.parameters=r}get parameterMap(){var e;return(e=this._parameterMap)!=null||(this._parameterMap=zn(this.parameters)),this._parameterMap}toString(){return _y(this)}};function RT(t,e){return Hn(t,e)&&t.every((r,n)=>Tt(r.parameters,e[n].parameters))}function Hn(t,e){return t.length!==e.length?!1:t.every((r,n)=>r.path===e[n].path)}function NT(t,e){let r=[];return Object.entries(t.children).forEach(([n,o])=>{n===j&&(r=r.concat(e(o,n)))}),Object.entries(t.children).forEach(([n,o])=>{n!==j&&(r=r.concat(e(o,n)))}),r}var Gt=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>new un,providedIn:"root"}));let t=e;return t})(),un=class{parse(e){let r=new yf(e);return new xt(r.parseRootSegment(),r.parseQueryParams(),r.parseFragment())}serialize(e){let r=`/${ti(e.root,!0)}`,n=FT(e.queryParams),o=typeof e.fragment=="string"?`#${kT(e.fragment)}`:"";return`${r}${n}${o}`}},OT=new un;function Wa(t){return t.segments.map(e=>_y(e)).join("/")}function ti(t,e){if(!t.hasChildren())return Wa(t);if(e){let r=t.children[j]?ti(t.children[j],!1):"",n=[];return Object.entries(t.children).forEach(([o,i])=>{o!==j&&n.push(`${o}:${ti(i,!1)}`)}),n.length>0?`${r}(${n.join("//")})`:r}else{let r=NT(t,(n,o)=>o===j?[ti(t.children[j],!1)]:[`${o}:${ti(n,!1)}`]);return Object.keys(t.children).length===1&&t.children[j]!=null?`${Wa(t)}/${r[0]}`:`${Wa(t)}/(${r.join("//")})`}}function Ty(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function $a(t){return Ty(t).replace(/%3B/gi,";")}function kT(t){return encodeURI(t)}function vf(t){return Ty(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function qa(t){return decodeURIComponent(t)}function py(t){return qa(t.replace(/\+/g,"%20"))}function _y(t){return`${vf(t.path)}${PT(t.parameters)}`}function PT(t){return Object.entries(t).map(([e,r])=>`;${vf(e)}=${vf(r)}`).join("")}function FT(t){let e=Object.entries(t).map(([r,n])=>Array.isArray(n)?n.map(o=>`${$a(r)}=${$a(o)}`).join("&"):`${$a(r)}=${$a(n)}`).filter(r=>r);return e.length?`?${e.join("&")}`:""}var jT=/^[^\/()?;#]+/;function df(t){let e=t.match(jT);return e?e[0]:""}var LT=/^[^\/()?;=#]+/;function VT(t){let e=t.match(LT);return e?e[0]:""}var BT=/^[^=?&#]+/;function UT(t){let e=t.match(BT);return e?e[0]:""}var HT=/^[^&#]+/;function $T(t){let e=t.match(HT);return e?e[0]:""}var yf=class{url;remaining;constructor(e){this.url=e,this.remaining=e}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new K([],{}):new K([],this.parseChildren())}parseQueryParams(){let e={};if(this.consumeOptional("?"))do this.parseQueryParam(e);while(this.consumeOptional("&"));return e}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let e=[];for(this.peekStartsWith("(")||e.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),e.push(this.parseSegment());let r={};this.peekStartsWith("/(")&&(this.capture("/"),r=this.parseParens(!0));let n={};return this.peekStartsWith("(")&&(n=this.parseParens(!1)),(e.length>0||Object.keys(r).length>0)&&(n[j]=new K(e,r)),n}parseSegment(){let e=df(this.remaining);if(e===""&&this.peekStartsWith(";"))throw new R(4009,!1);return this.capture(e),new ln(qa(e),this.parseMatrixParams())}parseMatrixParams(){let e={};for(;this.consumeOptional(";");)this.parseParam(e);return e}parseParam(e){let r=VT(this.remaining);if(!r)return;this.capture(r);let n="";if(this.consumeOptional("=")){let o=df(this.remaining);o&&(n=o,this.capture(n))}e[qa(r)]=qa(n)}parseQueryParam(e){let r=UT(this.remaining);if(!r)return;this.capture(r);let n="";if(this.consumeOptional("=")){let s=$T(this.remaining);s&&(n=s,this.capture(n))}let o=py(r),i=py(n);if(e.hasOwnProperty(o)){let s=e[o];Array.isArray(s)||(s=[s],e[o]=s),s.push(i)}else e[o]=i}parseParens(e){let r={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let n=df(this.remaining),o=this.remaining[n.length];if(o!=="/"&&o!==")"&&o!==";")throw new R(4010,!1);let i;n.indexOf(":")>-1?(i=n.slice(0,n.indexOf(":")),this.capture(i),this.capture(":")):e&&(i=j);let s=this.parseChildren();r[i]=Object.keys(s).length===1?s[j]:new K([],s),this.consumeOptional("//")}return r}peekStartsWith(e){return this.remaining.startsWith(e)}consumeOptional(e){return this.peekStartsWith(e)?(this.remaining=this.remaining.substring(e.length),!0):!1}capture(e){if(!this.consumeOptional(e))throw new R(4011,!1)}};function xy(t){return t.segments.length>0?new K([],{[j]:t}):t}function Ay(t){let e={};for(let[n,o]of Object.entries(t.children)){let i=Ay(o);if(n===j&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))e[s]=a;else(i.segments.length>0||i.hasChildren())&&(e[n]=i)}let r=new K(t.segments,e);return zT(r)}function zT(t){if(t.numberOfChildren===1&&t.children[j]){let e=t.children[j];return new K(t.segments.concat(e.segments),e.children)}return t}function dn(t){return t instanceof xt}function Ry(t,e,r=null,n=null){let o=Ny(t);return Oy(o,e,r,n)}function Ny(t){let e;function r(i){let s={};for(let c of i.children){let l=r(c);s[c.outlet]=l}let a=new K(i.url,s);return i===t&&(e=a),a}let n=r(t.root),o=xy(n);return e!=null?e:o}function Oy(t,e,r,n){let o=t;for(;o.parent;)o=o.parent;if(e.length===0)return ff(o,o,o,r,n);let i=GT(e);if(i.toRoot())return ff(o,o,new K([],{}),r,n);let s=WT(i,o,t),a=s.processChildren?ri(s.segmentGroup,s.index,i.commands):Py(s.segmentGroup,s.index,i.commands);return ff(o,s.segmentGroup,a,r,n)}function Ya(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function ii(t){return typeof t=="object"&&t!=null&&t.outlets}function ff(t,e,r,n,o){let i={};n&&Object.entries(n).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(d=>`${d}`):`${l}`});let s;t===e?s=r:s=ky(t,e,r);let a=xy(Ay(s));return new xt(a,i,o)}function ky(t,e,r){let n={};return Object.entries(t.children).forEach(([o,i])=>{i===e?n[o]=r:n[o]=ky(i,e,r)}),new K(t.segments,n)}var Qa=class{isAbsolute;numberOfDoubleDots;commands;constructor(e,r,n){if(this.isAbsolute=e,this.numberOfDoubleDots=r,this.commands=n,e&&n.length>0&&Ya(n[0]))throw new R(4003,!1);let o=n.find(ii);if(o&&o!==by(n))throw new R(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function GT(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new Qa(!0,0,t);let e=0,r=!1,n=t.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?r=!0:a===".."?e++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Qa(r,e,n)}var zr=class{segmentGroup;processChildren;index;constructor(e,r,n){this.segmentGroup=e,this.processChildren=r,this.index=n}};function WT(t,e,r){if(t.isAbsolute)return new zr(e,!0,0);if(!r)return new zr(e,!1,NaN);if(r.parent===null)return new zr(r,!0,0);let n=Ya(t.commands[0])?0:1,o=r.segments.length-1+n;return qT(r,o,t.numberOfDoubleDots)}function qT(t,e,r){let n=t,o=e,i=r;for(;i>o;){if(i-=o,n=n.parent,!n)throw new R(4005,!1);o=n.segments.length}return new zr(n,!1,o-i)}function ZT(t){return ii(t[0])?t[0].outlets:{[j]:t}}function Py(t,e,r){if(t!=null||(t=new K([],{})),t.segments.length===0&&t.hasChildren())return ri(t,e,r);let n=YT(t,e,r),o=r.slice(n.commandIndex);if(n.match&&n.pathIndex<t.segments.length){let i=new K(t.segments.slice(0,n.pathIndex),{});return i.children[j]=new K(t.segments.slice(n.pathIndex),t.children),ri(i,0,o)}else return n.match&&o.length===0?new K(t.segments,{}):n.match&&!t.hasChildren()?Df(t,e,r):n.match?ri(t,0,o):Df(t,e,r)}function ri(t,e,r){if(r.length===0)return new K(t.segments,{});{let n=ZT(r),o={};if(Object.keys(n).some(i=>i!==j)&&t.children[j]&&t.numberOfChildren===1&&t.children[j].segments.length===0){let i=ri(t.children[j],e,r);return new K(t.segments,i.children)}return Object.entries(n).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Py(t.children[i],e,s))}),Object.entries(t.children).forEach(([i,s])=>{n[i]===void 0&&(o[i]=s)}),new K(t.segments,o)}}function YT(t,e,r){let n=0,o=e,i={match:!1,pathIndex:0,commandIndex:0};for(;o<t.segments.length;){if(n>=r.length)return i;let s=t.segments[o],a=r[n];if(ii(a))break;let c=`${a}`,l=n<r.length-1?r[n+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!my(c,l,s))return i;n+=2}else{if(!my(c,{},s))return i;n++}o++}return{match:!0,pathIndex:o,commandIndex:n}}function Df(t,e,r){let n=t.segments.slice(0,e),o=0;for(;o<r.length;){let i=r[o];if(ii(i)){let c=QT(i.outlets);return new K(n,c)}if(o===0&&Ya(r[0])){let c=t.segments[e];n.push(new ln(c.path,gy(r[0]))),o++;continue}let s=ii(i)?i.outlets[j]:`${i}`,a=o<r.length-1?r[o+1]:null;s&&a&&Ya(a)?(n.push(new ln(s,gy(a))),o+=2):(n.push(new ln(s,{})),o++)}return new K(n,{})}function QT(t){let e={};return Object.entries(t).forEach(([r,n])=>{typeof n=="string"&&(n=[n]),n!==null&&(e[r]=Df(new K([],{}),0,n))}),e}function gy(t){let e={};return Object.entries(t).forEach(([r,n])=>e[r]=`${n}`),e}function my(t,e,r){return t==r.path&&Tt(e,r.parameters)}var Za="imperative",me=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(me||{}),Ze=class{id;url;constructor(e,r){this.id=e,this.url=r}},At=class extends Ze{type=me.NavigationStart;navigationTrigger;restoredState;constructor(e,r,n="imperative",o=null){super(e,r),this.navigationTrigger=n,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},nt=class extends Ze{urlAfterRedirects;type=me.NavigationEnd;constructor(e,r,n){super(e,r),this.urlAfterRedirects=n}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Be=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t}(Be||{}),Wr=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(Wr||{}),_t=class extends Ze{reason;code;type=me.NavigationCancel;constructor(e,r,n,o){super(e,r),this.reason=n,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Rt=class extends Ze{reason;code;type=me.NavigationSkipped;constructor(e,r,n,o){super(e,r),this.reason=n,this.code=o}},qr=class extends Ze{error;target;type=me.NavigationError;constructor(e,r,n,o){super(e,r),this.error=n,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},si=class extends Ze{urlAfterRedirects;state;type=me.RoutesRecognized;constructor(e,r,n,o){super(e,r),this.urlAfterRedirects=n,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ka=class extends Ze{urlAfterRedirects;state;type=me.GuardsCheckStart;constructor(e,r,n,o){super(e,r),this.urlAfterRedirects=n,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Xa=class extends Ze{urlAfterRedirects;state;shouldActivate;type=me.GuardsCheckEnd;constructor(e,r,n,o,i){super(e,r),this.urlAfterRedirects=n,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Ja=class extends Ze{urlAfterRedirects;state;type=me.ResolveStart;constructor(e,r,n,o){super(e,r),this.urlAfterRedirects=n,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ec=class extends Ze{urlAfterRedirects;state;type=me.ResolveEnd;constructor(e,r,n,o){super(e,r),this.urlAfterRedirects=n,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},tc=class{route;type=me.RouteConfigLoadStart;constructor(e){this.route=e}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},nc=class{route;type=me.RouteConfigLoadEnd;constructor(e){this.route=e}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},rc=class{snapshot;type=me.ChildActivationStart;constructor(e){this.snapshot=e}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},oc=class{snapshot;type=me.ChildActivationEnd;constructor(e){this.snapshot=e}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ic=class{snapshot;type=me.ActivationStart;constructor(e){this.snapshot=e}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},sc=class{snapshot;type=me.ActivationEnd;constructor(e){this.snapshot=e}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Zr=class{routerEvent;position;anchor;type=me.Scroll;constructor(e,r,n){this.routerEvent=e,this.position=r,this.anchor=n}toString(){let e=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${e}')`}},ai=class{},Yr=class{url;navigationBehaviorOptions;constructor(e,r){this.url=e,this.navigationBehaviorOptions=r}};function KT(t,e){var r;return t.providers&&!t._injector&&(t._injector=Fo(t.providers,e,`Route: ${t.path}`)),(r=t._injector)!=null?r:e}function ht(t){return t.outlet||j}function XT(t,e){let r=t.filter(n=>ht(n)===e);return r.push(...t.filter(n=>ht(n)!==e)),r}function pi(t){var e;if(!t)return null;if((e=t.routeConfig)!=null&&e._injector)return t.routeConfig._injector;for(let r=t.parent;r;r=r.parent){let n=r.routeConfig;if(n!=null&&n._loadedInjector)return n._loadedInjector;if(n!=null&&n._injector)return n._injector}return null}var ac=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){var e,r;return(r=pi((e=this.route)==null?void 0:e.snapshot))!=null?r:this.rootInjector}constructor(e){this.rootInjector=e,this.children=new Nt(this.rootInjector)}},Nt=(()=>{let e=class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,o){let i=this.getOrCreateContext(n);i.outlet=o,this.contexts.set(n,i)}onChildOutletDestroyed(n){let o=this.getContext(n);o&&(o.outlet=null,o.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let o=this.getContext(n);return o||(o=new ac(this.rootInjector),this.contexts.set(n,o)),o}getContext(n){return this.contexts.get(n)||null}};u(e,"\u0275fac",function(o){return new(o||e)(k(se))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),cc=class{_root;constructor(e){this._root=e}get root(){return this._root.value}parent(e){let r=this.pathFromRoot(e);return r.length>1?r[r.length-2]:null}children(e){let r=If(e,this._root);return r?r.children.map(n=>n.value):[]}firstChild(e){let r=If(e,this._root);return r&&r.children.length>0?r.children[0].value:null}siblings(e){let r=Cf(e,this._root);return r.length<2?[]:r[r.length-2].children.map(o=>o.value).filter(o=>o!==e)}pathFromRoot(e){return Cf(e,this._root).map(r=>r.value)}};function If(t,e){if(t===e.value)return e;for(let r of e.children){let n=If(t,r);if(n)return n}return null}function Cf(t,e){if(t===e.value)return[e];for(let r of e.children){let n=Cf(t,r);if(n.length)return n.unshift(e),n}return[]}var qe=class{value;children;constructor(e,r){this.value=e,this.children=r}toString(){return`TreeNode(${this.value})`}};function $r(t){let e={};return t&&t.children.forEach(r=>e[r.value.outlet]=r),e}var ci=class extends cc{snapshot;constructor(e,r){super(e),this.snapshot=r,xf(this,e)}toString(){return this.snapshot.toString()}};function Fy(t){let e=JT(t),r=new pe([new ln("",{})]),n=new pe({}),o=new pe({}),i=new pe({}),s=new pe(""),a=new xe(r,n,i,s,o,j,t,e.root);return a.snapshot=e.root,new ci(new qe(a,[]),e)}function JT(t){let e={},r={},n={},o="",i=new $n([],e,n,o,r,j,t,null,{});return new li("",new qe(i,[]))}var xe=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(e,r,n,o,i,s,a,c){var l,d;this.urlSubject=e,this.paramsSubject=r,this.queryParamsSubject=n,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=(d=(l=this.dataSubject)==null?void 0:l.pipe(H(h=>h[hi])))!=null?d:P(void 0),this.url=e,this.params=r,this.queryParams=n,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){var e;return(e=this._paramMap)!=null||(this._paramMap=this.params.pipe(H(r=>zn(r)))),this._paramMap}get queryParamMap(){var e;return(e=this._queryParamMap)!=null||(this._queryParamMap=this.queryParams.pipe(H(r=>zn(r)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function lc(t,e,r="emptyOnly"){var i,s;let n,{routeConfig:o}=t;return e!==null&&(r==="always"||(o==null?void 0:o.path)===""||!e.component&&!((i=e.routeConfig)!=null&&i.loadComponent))?n={params:b(b({},e.params),t.params),data:b(b({},e.data),t.data),resolve:b(b(b(b({},t.data),e.data),o==null?void 0:o.data),t._resolvedData)}:n={params:b({},t.params),data:b({},t.data),resolve:b(b({},t.data),(s=t._resolvedData)!=null?s:{})},o&&Ly(o)&&(n.resolve[hi]=o.title),n}var $n=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){var e;return(e=this.data)==null?void 0:e[hi]}constructor(e,r,n,o,i,s,a,c,l){this.url=e,this.params=r,this.queryParams=n,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){var e;return(e=this._paramMap)!=null||(this._paramMap=zn(this.params)),this._paramMap}get queryParamMap(){var e;return(e=this._queryParamMap)!=null||(this._queryParamMap=zn(this.queryParams)),this._queryParamMap}toString(){let e=this.url.map(n=>n.toString()).join("/"),r=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${e}', path:'${r}')`}},li=class extends cc{url;constructor(e,r){super(r),this.url=e,xf(this,r)}toString(){return jy(this._root)}};function xf(t,e){e.value._routerState=t,e.children.forEach(r=>xf(t,r))}function jy(t){let e=t.children.length>0?` { ${t.children.map(jy).join(", ")} } `:"";return`${t.value}${e}`}function hf(t){if(t.snapshot){let e=t.snapshot,r=t._futureSnapshot;t.snapshot=r,Tt(e.queryParams,r.queryParams)||t.queryParamsSubject.next(r.queryParams),e.fragment!==r.fragment&&t.fragmentSubject.next(r.fragment),Tt(e.params,r.params)||t.paramsSubject.next(r.params),TT(e.url,r.url)||t.urlSubject.next(r.url),Tt(e.data,r.data)||t.dataSubject.next(r.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function bf(t,e){let r=Tt(t.params,e.params)&&RT(t.url,e.url),n=!t.parent!=!e.parent;return r&&!n&&(!t.parent||bf(t.parent,e.parent))}function Ly(t){return typeof t.title=="string"||t.title===null}var Vy=new N(""),Af=(()=>{let e=class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=j;activateEvents=new ie;deactivateEvents=new ie;attachEvents=new ie;detachEvents=new ie;routerOutletData=rm(void 0);parentContexts=I(Nt);location=I(ze);changeDetector=I(C);inputBinder=I(gi,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:o,previousValue:i}=n.name;if(o)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){var n;this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),(n=this.inputBinder)==null||n.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){var o;return((o=this.parentContexts.getContext(n))==null?void 0:o.outlet)===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n!=null&&n.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new R(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new R(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new R(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,o){var i;this.activated=n,this._activatedRoute=o,this.location.insert(n.hostView),(i=this.inputBinder)==null||i.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,o){var d;if(this.isActivated)throw new R(4013,!1);this._activatedRoute=n;let i=this.location,a=n.snapshot.component,c=this.parentContexts.getOrCreateContext(this.name).children,l=new Ef(n,c,i.injector,this.routerOutletData);this.activated=i.createComponent(a,{index:i.length,injector:l,environmentInjector:o}),this.changeDetector.markForCheck(),(d=this.inputBinder)==null||d.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275dir",B({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[tt]}));let t=e;return t})(),Ef=class{route;childContexts;parent;outletData;constructor(e,r,n,o){this.route=e,this.childContexts=r,this.parent=n,this.outletData=o}get(e,r){return e===xe?this.route:e===Nt?this.childContexts:e===Vy?this.outletData:this.parent.get(e,r)}},gi=new N(""),Rf=(()=>{let e=class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){var o;(o=this.outletDataSubscriptions.get(n))==null||o.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:o}=n,i=Cn([o.queryParams,o.params,o.data]).pipe(De(([s,a,c],l)=>(c=b(b(b({},s),a),c),l===0?P(c):Promise.resolve(c)))).subscribe(s=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==o||o.component===null){this.unsubscribeFromRouteData(n);return}let a=wa(o.component);if(!a){this.unsubscribeFromRouteData(n);return}for(let{templateName:c}of a.inputs)n.activatedComponentRef.setInput(c,s[c])});this.outletDataSubscriptions.set(n,i)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();function e_(t,e,r){let n=ui(t,e._root,r?r._root:void 0);return new ci(n,e)}function ui(t,e,r){if(r&&t.shouldReuseRoute(e.value,r.value.snapshot)){let n=r.value;n._futureSnapshot=e.value;let o=t_(t,e,r);return new qe(n,o)}else{if(t.shouldAttach(e.value)){let i=t.retrieve(e.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=e.value,s.children=e.children.map(a=>ui(t,a)),s}}let n=n_(e.value),o=e.children.map(i=>ui(t,i));return new qe(n,o)}}function t_(t,e,r){return e.children.map(n=>{for(let o of r.children)if(t.shouldReuseRoute(n.value,o.value.snapshot))return ui(t,n,o);return ui(t,n)})}function n_(t){return new xe(new pe(t.url),new pe(t.params),new pe(t.queryParams),new pe(t.fragment),new pe(t.data),t.outlet,t.component,t)}var Qr=class{redirectTo;navigationBehaviorOptions;constructor(e,r){this.redirectTo=e,this.navigationBehaviorOptions=r}},By="ngNavigationCancelingError";function uc(t,e){let{redirectTo:r,navigationBehaviorOptions:n}=dn(e)?{redirectTo:e,navigationBehaviorOptions:void 0}:e,o=Uy(!1,Be.Redirect);return o.url=r,o.navigationBehaviorOptions=n,o}function Uy(t,e){let r=new Error(`NavigationCancelingError: ${t||""}`);return r[By]=!0,r.cancellationCode=e,r}function r_(t){return Hy(t)&&dn(t.url)}function Hy(t){return!!t&&t[By]}var o_=(t,e,r,n)=>H(o=>(new wf(e,o.targetRouterState,o.currentRouterState,r,n).activate(t),o)),wf=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(e,r,n,o,i){this.routeReuseStrategy=e,this.futureState=r,this.currState=n,this.forwardEvent=o,this.inputBindingEnabled=i}activate(e){let r=this.futureState._root,n=this.currState?this.currState._root:null;this.deactivateChildRoutes(r,n,e),hf(this.futureState.root),this.activateChildRoutes(r,n,e)}deactivateChildRoutes(e,r,n){let o=$r(r);e.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],n),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,n)})}deactivateRoutes(e,r,n){let o=e.value,i=r?r.value:null;if(o===i)if(o.component){let s=n.getContext(o.outlet);s&&this.deactivateChildRoutes(e,r,s.children)}else this.deactivateChildRoutes(e,r,n);else i&&this.deactivateRouteAndItsChildren(r,n)}deactivateRouteAndItsChildren(e,r){e.value.component&&this.routeReuseStrategy.shouldDetach(e.value.snapshot)?this.detachAndStoreRouteSubtree(e,r):this.deactivateRouteAndOutlet(e,r)}detachAndStoreRouteSubtree(e,r){let n=r.getContext(e.value.outlet),o=n&&e.value.component?n.children:r,i=$r(e);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(n&&n.outlet){let s=n.outlet.detach(),a=n.children.onOutletDeactivated();this.routeReuseStrategy.store(e.value.snapshot,{componentRef:s,route:e,contexts:a})}}deactivateRouteAndOutlet(e,r){let n=r.getContext(e.value.outlet),o=n&&e.value.component?n.children:r,i=$r(e);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);n&&(n.outlet&&(n.outlet.deactivate(),n.children.onOutletDeactivated()),n.attachRef=null,n.route=null)}activateChildRoutes(e,r,n){let o=$r(r);e.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],n),this.forwardEvent(new sc(i.value.snapshot))}),e.children.length&&this.forwardEvent(new oc(e.value.snapshot))}activateRoutes(e,r,n){let o=e.value,i=r?r.value:null;if(hf(o),o===i)if(o.component){let s=n.getOrCreateContext(o.outlet);this.activateChildRoutes(e,r,s.children)}else this.activateChildRoutes(e,r,n);else if(o.component){let s=n.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),hf(a.route.value),this.activateChildRoutes(e,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(e,null,s.children)}else this.activateChildRoutes(e,null,n)}},dc=class{path;route;constructor(e){this.path=e,this.route=this.path[this.path.length-1]}},Gr=class{component;route;constructor(e,r){this.component=e,this.route=r}};function i_(t,e,r){let n=t._root,o=e?e._root:null;return ni(n,o,r,[n.value])}function s_(t){let e=t.routeConfig?t.routeConfig.canActivateChild:null;return!e||e.length===0?null:{node:t,guards:e}}function Xr(t,e){let r=Symbol(),n=e.get(t,r);return n===r?typeof t=="function"&&!Jp(t)?t:e.get(t):n}function ni(t,e,r,n,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=$r(e);return t.children.forEach(s=>{a_(s,i[s.value.outlet],r,n.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>oi(a,r.getContext(s),o)),o}function a_(t,e,r,n,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=t.value,s=e?e.value:null,a=r?r.getContext(t.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=c_(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new dc(n)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?ni(t,e,a?a.children:null,n,o):ni(t,e,r,n,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Gr(a.outlet.component,s))}else s&&oi(e,a,o),o.canActivateChecks.push(new dc(n)),i.component?ni(t,null,a?a.children:null,n,o):ni(t,null,r,n,o);return o}function c_(t,e,r){if(typeof r=="function")return r(t,e);switch(r){case"pathParamsChange":return!Hn(t.url,e.url);case"pathParamsOrQueryParamsChange":return!Hn(t.url,e.url)||!Tt(t.queryParams,e.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!bf(t,e)||!Tt(t.queryParams,e.queryParams);case"paramsChange":default:return!bf(t,e)}}function oi(t,e,r){let n=$r(t),o=t.value;Object.entries(n).forEach(([i,s])=>{o.component?e?oi(s,e.children.getContext(i),r):oi(s,null,r):oi(s,e,r)}),o.component?e&&e.outlet&&e.outlet.isActivated?r.canDeactivateChecks.push(new Gr(e.outlet.component,o)):r.canDeactivateChecks.push(new Gr(null,o)):r.canDeactivateChecks.push(new Gr(null,o))}function mi(t){return typeof t=="function"}function l_(t){return typeof t=="boolean"}function u_(t){return t&&mi(t.canLoad)}function d_(t){return t&&mi(t.canActivate)}function f_(t){return t&&mi(t.canActivateChild)}function h_(t){return t&&mi(t.canDeactivate)}function p_(t){return t&&mi(t.canMatch)}function $y(t){return t instanceof rt||(t==null?void 0:t.name)==="EmptyError"}var za=Symbol("INITIAL_VALUE");function Kr(){return De(t=>Cn(t.map(e=>e.pipe(kt(1),hl(za)))).pipe(H(e=>{for(let r of e)if(r!==!0){if(r===za)return za;if(r===!1||g_(r))return r}return!0}),ye(e=>e!==za),kt(1)))}function g_(t){return dn(t)||t instanceof Qr}function m_(t,e){return le(r=>{let{targetSnapshot:n,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=r;return s.length===0&&i.length===0?P(L(b({},r),{guardsResult:!0})):v_(s,n,o,t).pipe(le(a=>a&&l_(a)?y_(n,i,t,e):P(a)),H(a=>L(b({},r),{guardsResult:a})))})}function v_(t,e,r,n){return re(t).pipe(le(o=>E_(o.component,o.route,r,e,n)),Pt(o=>o!==!0,!0))}function y_(t,e,r,n){return re(e).pipe(gt(o=>lr(I_(o.route.parent,n),D_(o.route,n),b_(t,o.path,r),C_(t,o.route,r))),Pt(o=>o!==!0,!0))}function D_(t,e){return t!==null&&e&&e(new ic(t)),P(!0)}function I_(t,e){return t!==null&&e&&e(new rc(t)),P(!0)}function C_(t,e,r){let n=e.routeConfig?e.routeConfig.canActivate:null;if(!n||n.length===0)return P(!0);let o=n.map(i=>rs(()=>{var l;let s=(l=pi(e))!=null?l:r,a=Xr(i,s),c=d_(a)?a.canActivate(e,t):je(s,()=>a(e,t));return fn(c).pipe(Pt())}));return P(o).pipe(Kr())}function b_(t,e,r){let n=e[e.length-1],i=e.slice(0,e.length-1).reverse().map(s=>s_(s)).filter(s=>s!==null).map(s=>rs(()=>{let a=s.guards.map(c=>{var g;let l=(g=pi(s.node))!=null?g:r,d=Xr(c,l),h=f_(d)?d.canActivateChild(n,t):je(l,()=>d(n,t));return fn(h).pipe(Pt())});return P(a).pipe(Kr())}));return P(i).pipe(Kr())}function E_(t,e,r,n,o){let i=e&&e.routeConfig?e.routeConfig.canDeactivate:null;if(!i||i.length===0)return P(!0);let s=i.map(a=>{var h;let c=(h=pi(e))!=null?h:o,l=Xr(a,c),d=h_(l)?l.canDeactivate(t,e,r,n):je(c,()=>l(t,e,r,n));return fn(d).pipe(Pt())});return P(s).pipe(Kr())}function w_(t,e,r,n){let o=e.canLoad;if(o===void 0||o.length===0)return P(!0);let i=o.map(s=>{let a=Xr(s,t),c=u_(a)?a.canLoad(e,r):je(t,()=>a(e,r));return fn(c)});return P(i).pipe(Kr(),zy(n))}function zy(t){return rl(Ee(e=>{if(typeof e!="boolean")throw uc(t,e)}),H(e=>e===!0))}function M_(t,e,r,n){let o=e.canMatch;if(!o||o.length===0)return P(!0);let i=o.map(s=>{let a=Xr(s,t),c=p_(a)?a.canMatch(e,r):je(t,()=>a(e,r));return fn(c)});return P(i).pipe(Kr(),zy(n))}var di=class{segmentGroup;constructor(e){this.segmentGroup=e||null}},fi=class extends Error{urlTree;constructor(e){super(),this.urlTree=e}};function Hr(t){return sr(new di(t))}function S_(t){return sr(new R(4e3,!1))}function T_(t){return sr(Uy(!1,Be.GuardRejected))}var Mf=class{urlSerializer;urlTree;constructor(e,r){this.urlSerializer=e,this.urlTree=r}lineralizeSegments(e,r){let n=[],o=r.root;for(;;){if(n=n.concat(o.segments),o.numberOfChildren===0)return P(n);if(o.numberOfChildren>1||!o.children[j])return S_(`${e.redirectTo}`);o=o.children[j]}}applyRedirectCommands(e,r,n,o,i){if(typeof r!="string"){let a=r,{queryParams:c,fragment:l,routeConfig:d,url:h,outlet:g,params:p,data:v,title:D}=o,x=je(i,()=>a({params:p,data:v,queryParams:c,fragment:l,routeConfig:d,url:h,outlet:g,title:D}));if(x instanceof xt)throw new fi(x);r=x}let s=this.applyRedirectCreateUrlTree(r,this.urlSerializer.parse(r),e,n);if(r[0]==="/")throw new fi(s);return s}applyRedirectCreateUrlTree(e,r,n,o){let i=this.createSegmentGroup(e,r.root,n,o);return new xt(i,this.createQueryParams(r.queryParams,this.urlTree.queryParams),r.fragment)}createQueryParams(e,r){let n={};return Object.entries(e).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);n[o]=r[a]}else n[o]=i}),n}createSegmentGroup(e,r,n,o){let i=this.createSegments(e,r.segments,n,o),s={};return Object.entries(r.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(e,c,n,o)}),new K(i,s)}createSegments(e,r,n,o){return r.map(i=>i.path[0]===":"?this.findPosParam(e,i,o):this.findOrReturn(i,n))}findPosParam(e,r,n){let o=n[r.path.substring(1)];if(!o)throw new R(4001,!1);return o}findOrReturn(e,r){let n=0;for(let o of r){if(o.path===e.path)return r.splice(n),o;n++}return e}},Sf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function __(t,e,r,n,o){let i=Gy(t,e,r);return i.matched?(n=KT(e,n),M_(n,e,r,o).pipe(H(s=>s===!0?i:b({},Sf)))):P(i)}function Gy(t,e,r){var a,c;if(e.path==="**")return x_(r);if(e.path==="")return e.pathMatch==="full"&&(t.hasChildren()||r.length>0)?b({},Sf):{matched:!0,consumedSegments:[],remainingSegments:r,parameters:{},positionalParamSegments:{}};let o=(e.matcher||Iy)(r,t,e);if(!o)return b({},Sf);let i={};Object.entries((a=o.posParams)!=null?a:{}).forEach(([l,d])=>{i[l]=d.path});let s=o.consumed.length>0?b(b({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:r.slice(o.consumed.length),parameters:s,positionalParamSegments:(c=o.posParams)!=null?c:{}}}function x_(t){return{matched:!0,parameters:t.length>0?by(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function vy(t,e,r,n){return r.length>0&&N_(t,r,n)?{segmentGroup:new K(e,R_(n,new K(r,t.children))),slicedSegments:[]}:r.length===0&&O_(t,r,n)?{segmentGroup:new K(t.segments,A_(t,r,n,t.children)),slicedSegments:r}:{segmentGroup:new K(t.segments,t.children),slicedSegments:r}}function A_(t,e,r,n){let o={};for(let i of r)if(hc(t,e,i)&&!n[ht(i)]){let s=new K([],{});o[ht(i)]=s}return b(b({},n),o)}function R_(t,e){let r={};r[j]=e;for(let n of t)if(n.path===""&&ht(n)!==j){let o=new K([],{});r[ht(n)]=o}return r}function N_(t,e,r){return r.some(n=>hc(t,e,n)&&ht(n)!==j)}function O_(t,e,r){return r.some(n=>hc(t,e,n))}function hc(t,e,r){return(t.hasChildren()||e.length>0)&&r.pathMatch==="full"?!1:r.path===""}function k_(t,e,r){return e.length===0&&!t.children[r]}var Tf=class{};function P_(t,e,r,n,o,i,s="emptyOnly"){return new _f(t,e,r,n,o,s,i).recognize()}var F_=31,_f=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(e,r,n,o,i,s,a){this.injector=e,this.configLoader=r,this.rootComponentType=n,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Mf(this.urlSerializer,this.urlTree)}noMatchError(e){return new R(4002,`'${e.segmentGroup}'`)}recognize(){let e=vy(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(e).pipe(H(({children:r,rootSnapshot:n})=>{let o=new qe(n,r),i=new li("",o),s=Ry(n,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(e){let r=new $n([],Object.freeze({}),Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),j,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,e,j,r).pipe(H(n=>({children:n,rootSnapshot:r})),pt(n=>{if(n instanceof fi)return this.urlTree=n.urlTree,this.match(n.urlTree.root);throw n instanceof di?this.noMatchError(n):n}))}processSegmentGroup(e,r,n,o,i){return n.segments.length===0&&n.hasChildren()?this.processChildren(e,r,n,i):this.processSegment(e,r,n,n.segments,o,!0,i).pipe(H(s=>s instanceof qe?[s]:[]))}processChildren(e,r,n,o){let i=[];for(let s of Object.keys(n.children))s==="primary"?i.unshift(s):i.push(s);return re(i).pipe(gt(s=>{let a=n.children[s],c=XT(r,s);return this.processSegmentGroup(e,c,a,s,o)}),fl((s,a)=>(s.push(...a),s)),Qt(null),dl(),le(s=>{if(s===null)return Hr(n);let a=Wy(s);return j_(a),P(a)}))}processSegment(e,r,n,o,i,s,a){return re(r).pipe(gt(c=>{var l;return this.processSegmentAgainstRoute((l=c._injector)!=null?l:e,r,c,n,o,i,s,a).pipe(pt(d=>{if(d instanceof di)return P(null);throw d}))}),Pt(c=>!!c),pt(c=>{if($y(c))return k_(n,o,i)?P(new Tf):Hr(n);throw c}))}processSegmentAgainstRoute(e,r,n,o,i,s,a,c){return ht(n)!==s&&(s===j||!hc(o,i,n))?Hr(o):n.redirectTo===void 0?this.matchSegmentAgainstRoute(e,o,n,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(e,o,r,n,i,s,c):Hr(o)}expandSegmentAgainstRouteUsingRedirect(e,r,n,o,i,s,a){var x,O;let{matched:c,parameters:l,consumedSegments:d,positionalParamSegments:h,remainingSegments:g}=Gy(r,o,i);if(!c)return Hr(r);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>F_&&(this.allowRedirects=!1));let p=new $n(i,l,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,yy(o),ht(o),(O=(x=o.component)!=null?x:o._loadedComponent)!=null?O:null,o,Dy(o)),v=lc(p,a,this.paramsInheritanceStrategy);p.params=Object.freeze(v.params),p.data=Object.freeze(v.data);let D=this.applyRedirects.applyRedirectCommands(d,o.redirectTo,h,p,e);return this.applyRedirects.lineralizeSegments(o,D).pipe(le(X=>this.processSegment(e,n,r,X.concat(g),s,!1,a)))}matchSegmentAgainstRoute(e,r,n,o,i,s){let a=__(r,n,o,e,this.urlSerializer);return n.path==="**"&&(r.children={}),a.pipe(De(c=>{var l;return c.matched?(e=(l=n._injector)!=null?l:e,this.getChildConfig(e,n,o).pipe(De(({routes:d})=>{var he,Ot,Ri;let h=(he=n._loadedInjector)!=null?he:e,{parameters:g,consumedSegments:p,remainingSegments:v}=c,D=new $n(p,g,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,yy(n),ht(n),(Ri=(Ot=n.component)!=null?Ot:n._loadedComponent)!=null?Ri:null,n,Dy(n)),x=lc(D,s,this.paramsInheritanceStrategy);D.params=Object.freeze(x.params),D.data=Object.freeze(x.data);let{segmentGroup:O,slicedSegments:X}=vy(r,p,v,d);if(X.length===0&&O.hasChildren())return this.processChildren(h,d,O,D).pipe(H(Qn=>new qe(D,Qn)));if(d.length===0&&X.length===0)return P(new qe(D,[]));let Ue=ht(n)===i;return this.processSegment(h,d,O,X,Ue?j:i,!0,D).pipe(H(Qn=>new qe(D,Qn instanceof qe?[Qn]:[])))}))):Hr(r)}))}getChildConfig(e,r,n){return r.children?P({routes:r.children,injector:e}):r.loadChildren?r._loadedRoutes!==void 0?P({routes:r._loadedRoutes,injector:r._loadedInjector}):w_(e,r,n,this.urlSerializer).pipe(le(o=>o?this.configLoader.loadChildren(e,r).pipe(Ee(i=>{r._loadedRoutes=i.routes,r._loadedInjector=i.injector})):T_(r))):P({routes:[],injector:e})}};function j_(t){t.sort((e,r)=>e.value.outlet===j?-1:r.value.outlet===j?1:e.value.outlet.localeCompare(r.value.outlet))}function L_(t){let e=t.value.routeConfig;return e&&e.path===""}function Wy(t){let e=[],r=new Set;for(let n of t){if(!L_(n)){e.push(n);continue}let o=e.find(i=>n.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...n.children),r.add(o)):e.push(n)}for(let n of r){let o=Wy(n.children);e.push(new qe(n.value,o))}return e.filter(n=>!r.has(n))}function yy(t){return t.data||{}}function Dy(t){return t.resolve||{}}function V_(t,e,r,n,o,i){return le(s=>P_(t,e,r,n,s.extractedUrl,o,i).pipe(H(({state:a,tree:c})=>L(b({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function B_(t,e){return le(r=>{let{targetSnapshot:n,guards:{canActivateChecks:o}}=r;if(!o.length)return P(r);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of qy(c))s.add(l);let a=0;return re(s).pipe(gt(c=>i.has(c)?U_(c,n,t,e):(c.data=lc(c,c.parent,t).resolve,P(void 0))),Ee(()=>a++),ur(1),le(c=>a===s.size?P(r):Ne))})}function qy(t){let e=t.children.map(r=>qy(r)).flat();return[t,...e]}function U_(t,e,r,n){let o=t.routeConfig,i=t._resolve;return(o==null?void 0:o.title)!==void 0&&!Ly(o)&&(i[hi]=o.title),H_(i,t,e,n).pipe(H(s=>(t._resolvedData=s,t.data=lc(t,t.parent,r).resolve,null)))}function H_(t,e,r,n){let o=mf(t);if(o.length===0)return P({});let i={};return re(o).pipe(le(s=>$_(t[s],e,r,n).pipe(Pt(),Ee(a=>{if(a instanceof Qr)throw uc(new un,a);i[s]=a}))),ur(1),H(()=>i),pt(s=>$y(s)?Ne:sr(s)))}function $_(t,e,r,n){var a;let o=(a=pi(e))!=null?a:n,i=Xr(t,o),s=i.resolve?i.resolve(e,r):je(o,()=>i(e,r));return fn(s)}function pf(t){return De(e=>{let r=t(e);return r?re(r).pipe(H(()=>e)):P(e)})}var Nf=(()=>{let e=class e{buildTitle(n){var s;let o,i=n.root;for(;i!==void 0;)o=(s=this.getResolvedTitleForRoute(i))!=null?s:o,i=i.children.find(a=>a.outlet===j);return o}getResolvedTitleForRoute(n){return n.data[hi]}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(Zy),providedIn:"root"}));let t=e;return t})(),Zy=(()=>{let e=class e extends Nf{title;constructor(n){super(),this.title=n}updateTitle(n){let o=this.buildTitle(n);o!==void 0&&this.title.setTitle(o)}};u(e,"\u0275fac",function(o){return new(o||e)(k(fy))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),Gn=new N("",{providedIn:"root",factory:()=>({})}),Of=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275cmp",E({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(o,i){o&1&&Ud(0,"router-outlet")},dependencies:[Af],encapsulation:2}));let t=e;return t})();function kf(t){let e=t.children&&t.children.map(kf),r=e?L(b({},t),{children:e}):b({},t);return!r.component&&!r.loadComponent&&(e||r.loadChildren)&&r.outlet&&r.outlet!==j&&(r.component=Of),r}var Jr=new N(""),pc=(()=>{let e=class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=I(Ea);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return P(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let o=fn(n.loadComponent()).pipe(H(Qy),Ee(s=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=s}),Kt(()=>{this.componentLoaders.delete(n)})),i=new or(o,()=>new ee).pipe(rr());return this.componentLoaders.set(n,i),i}loadChildren(n,o){if(this.childrenLoaders.get(o))return this.childrenLoaders.get(o);if(o._loadedRoutes)return P({routes:o._loadedRoutes,injector:o._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(o);let s=Yy(o,this.compiler,n,this.onLoadEndListener).pipe(Kt(()=>{this.childrenLoaders.delete(o)})),a=new or(s,()=>new ee).pipe(rr());return this.childrenLoaders.set(o,a),a}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();function Yy(t,e,r,n){return fn(t.loadChildren()).pipe(H(Qy),le(o=>o instanceof jd||Array.isArray(o)?P(o):re(e.compileModuleAsync(o))),H(o=>{n&&n(t);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(r).injector,s=i.get(Jr,[],{optional:!0,self:!0}).flat()),{routes:s.map(kf),injector:i}}))}function z_(t){return t&&typeof t=="object"&&"default"in t}function Qy(t){return z_(t)?t.default:t}var gc=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(G_),providedIn:"root"}));let t=e;return t})(),G_=(()=>{let e=class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,o){return n}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),Pf=new N(""),Ff=new N("");function Ky(t,e,r){let n=t.get(Ff),o=t.get(ce);return t.get(m).runOutsideAngular(()=>{if(!o.startViewTransition||n.skipNextTransition)return n.skipNextTransition=!1,new Promise(l=>setTimeout(l));let i,s=new Promise(l=>{i=l}),a=o.startViewTransition(()=>(i(),W_(t))),{onViewTransitionCreated:c}=n;return c&&je(t,()=>c({transition:a,from:e,to:r})),s})}function W_(t){return new Promise(e=>{md({read:()=>setTimeout(e)},{injector:t})})}var jf=new N(""),mc=(()=>{let e=class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new ee;transitionAbortSubject=new ee;configLoader=I(pc);environmentInjector=I(se);destroyRef=I(Ao);urlSerializer=I(Gt);rootContexts=I(Nt);location=I(We);inputBindingEnabled=I(gi,{optional:!0})!==null;titleStrategy=I(Nf);options=I(Gn,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=I(gc);createViewTransition=I(Pf,{optional:!0});navigationErrorHandler=I(jf,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>P(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=i=>this.events.next(new tc(i)),o=i=>this.events.next(new nc(i));this.configLoader.onLoadEndListener=o,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){var n;(n=this.transitions)==null||n.complete()}handleNavigationRequest(n){var i;let o=++this.navigationId;(i=this.transitions)==null||i.next(L(b({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:o}))}setupNavigations(n){return this.transitions=new pe(null),this.transitions.pipe(ye(o=>o!==null),De(o=>{let i=!1,s=!1;return P(o).pipe(De(a=>{var d;if(this.navigationId>o.id)return this.cancelNavigationTransition(o,"",Be.SupersededByNewNavigation),Ne;this.currentTransition=o,this.currentNavigation={id:a.id,initialUrl:a.rawUrl,extractedUrl:a.extractedUrl,targetBrowserUrl:typeof a.extras.browserUrl=="string"?this.urlSerializer.parse(a.extras.browserUrl):a.extras.browserUrl,trigger:a.source,extras:a.extras,previousNavigation:this.lastSuccessfulNavigation?L(b({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let c=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),l=(d=a.extras.onSameUrlNavigation)!=null?d:n.onSameUrlNavigation;if(!c&&l!=="reload"){let h="";return this.events.next(new Rt(a.id,this.urlSerializer.serialize(a.rawUrl),h,Wr.IgnoredSameUrlNavigation)),a.resolve(!1),Ne}if(this.urlHandlingStrategy.shouldProcessUrl(a.rawUrl))return P(a).pipe(De(h=>(this.events.next(new At(h.id,this.urlSerializer.serialize(h.extractedUrl),h.source,h.restoredState)),h.id!==this.navigationId?Ne:Promise.resolve(h))),V_(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),Ee(h=>{o.targetSnapshot=h.targetSnapshot,o.urlAfterRedirects=h.urlAfterRedirects,this.currentNavigation=L(b({},this.currentNavigation),{finalUrl:h.urlAfterRedirects});let g=new si(h.id,this.urlSerializer.serialize(h.extractedUrl),this.urlSerializer.serialize(h.urlAfterRedirects),h.targetSnapshot);this.events.next(g)}));if(c&&this.urlHandlingStrategy.shouldProcessUrl(a.currentRawUrl)){let{id:h,extractedUrl:g,source:p,restoredState:v,extras:D}=a,x=new At(h,this.urlSerializer.serialize(g),p,v);this.events.next(x);let O=Fy(this.rootComponentType).snapshot;return this.currentTransition=o=L(b({},a),{targetSnapshot:O,urlAfterRedirects:g,extras:L(b({},D),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=g,P(o)}else{let h="";return this.events.next(new Rt(a.id,this.urlSerializer.serialize(a.extractedUrl),h,Wr.IgnoredByUrlHandlingStrategy)),a.resolve(!1),Ne}}),Ee(a=>{let c=new Ka(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),H(a=>(this.currentTransition=o=L(b({},a),{guards:i_(a.targetSnapshot,a.currentSnapshot,this.rootContexts)}),o)),m_(this.environmentInjector,a=>this.events.next(a)),Ee(a=>{if(o.guardsResult=a.guardsResult,a.guardsResult&&typeof a.guardsResult!="boolean")throw uc(this.urlSerializer,a.guardsResult);let c=new Xa(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot,!!a.guardsResult);this.events.next(c)}),ye(a=>a.guardsResult?!0:(this.cancelNavigationTransition(a,"",Be.GuardRejected),!1)),pf(a=>{if(a.guards.canActivateChecks.length!==0)return P(a).pipe(Ee(c=>{let l=new Ja(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}),De(c=>{let l=!1;return P(c).pipe(B_(this.paramsInheritanceStrategy,this.environmentInjector),Ee({next:()=>l=!0,complete:()=>{l||this.cancelNavigationTransition(c,"",Be.NoDataFromResolver)}}))}),Ee(c=>{let l=new ec(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}))}),pf(a=>{let c=l=>{var h;let d=[];(h=l.routeConfig)!=null&&h.loadComponent&&!l.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(l.routeConfig).pipe(Ee(g=>{l.component=g}),H(()=>{})));for(let g of l.children)d.push(...c(g));return d};return Cn(c(a.targetSnapshot.root)).pipe(Qt(null),kt(1))}),pf(()=>this.afterPreactivation()),De(()=>{var d;let{currentSnapshot:a,targetSnapshot:c}=o,l=(d=this.createViewTransition)==null?void 0:d.call(this,this.environmentInjector,a.root,c.root);return l?re(l).pipe(H(()=>o)):P(o)}),H(a=>{let c=e_(n.routeReuseStrategy,a.targetSnapshot,a.currentRouterState);return this.currentTransition=o=L(b({},a),{targetRouterState:c}),this.currentNavigation.targetRouterState=c,o}),Ee(()=>{this.events.next(new ai)}),o_(this.rootContexts,n.routeReuseStrategy,a=>this.events.next(a),this.inputBindingEnabled),kt(1),Ee({next:a=>{var c;i=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new nt(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects))),(c=this.titleStrategy)==null||c.updateTitle(a.targetRouterState.snapshot),a.resolve(!0)},complete:()=>{i=!0}}),pl(this.transitionAbortSubject.pipe(Ee(a=>{throw a}))),Kt(()=>{var a;!i&&!s&&this.cancelNavigationTransition(o,"",Be.SupersededByNewNavigation),((a=this.currentTransition)==null?void 0:a.id)===o.id&&(this.currentNavigation=null,this.currentTransition=null)}),pt(a=>{var c;if(this.destroyed)return o.resolve(!1),Ne;if(s=!0,Hy(a))this.events.next(new _t(o.id,this.urlSerializer.serialize(o.extractedUrl),a.message,a.cancellationCode)),r_(a)?this.events.next(new Yr(a.url,a.navigationBehaviorOptions)):o.resolve(!1);else{let l=new qr(o.id,this.urlSerializer.serialize(o.extractedUrl),a,(c=o.targetSnapshot)!=null?c:void 0);try{let d=je(this.environmentInjector,()=>{var h;return(h=this.navigationErrorHandler)==null?void 0:h.call(this,l)});if(d instanceof Qr){let{message:h,cancellationCode:g}=uc(this.urlSerializer,d);this.events.next(new _t(o.id,this.urlSerializer.serialize(o.extractedUrl),h,g)),this.events.next(new Yr(d.redirectTo,d.navigationBehaviorOptions))}else throw this.events.next(l),a}catch(d){this.options.resolveNavigationPromiseOnError?o.resolve(!1):o.reject(d)}}return Ne}))}))}cancelNavigationTransition(n,o,i){let s=new _t(n.id,this.urlSerializer.serialize(n.extractedUrl),o,i);this.events.next(s),n.resolve(!1)}isUpdatingInternalState(){var n,o;return((n=this.currentTransition)==null?void 0:n.extractedUrl.toString())!==((o=this.currentTransition)==null?void 0:o.currentUrlTree.toString())}isUpdatedBrowserUrl(){var i,s,a,c;let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),o=(a=(i=this.currentNavigation)==null?void 0:i.targetBrowserUrl)!=null?a:(s=this.currentNavigation)==null?void 0:s.extractedUrl;return n.toString()!==(o==null?void 0:o.toString())&&!((c=this.currentNavigation)!=null&&c.extras.skipLocationChange)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();function q_(t){return t!==Za}var Xy=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(Z_),providedIn:"root"}));let t=e;return t})(),fc=class{shouldDetach(e){return!1}store(e,r){}shouldAttach(e){return!1}retrieve(e){return null}shouldReuseRoute(e,r){return e.routeConfig===r.routeConfig}},Z_=(()=>{let e=class e extends fc{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),Jy=(()=>{let e=class e{urlSerializer=I(Gt);options=I(Gn,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=I(We);urlHandlingStrategy=I(gc);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new xt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:o,targetBrowserUrl:i}){let s=n!==void 0?this.urlHandlingStrategy.merge(n,o):o,a=i!=null?i:s;return a instanceof xt?this.urlSerializer.serialize(a):a}commitTransition({targetRouterState:n,finalUrl:o,initialUrl:i}){o&&n?(this.currentUrlTree=o,this.rawUrlTree=this.urlHandlingStrategy.merge(o,i),this.routerState=n):this.rawUrlTree=i}routerState=Fy(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n!=null?n:this.rawUrlTree)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(Y_),providedIn:"root"}));let t=e;return t})(),Y_=(()=>{let e=class e extends Jy{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){var n,o;return this.canceledNavigationResolution!=="computed"?this.currentPageId:(o=(n=this.restoredState())==null?void 0:n.\u0275routerPageId)!=null?o:this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(o=>{o.type==="popstate"&&setTimeout(()=>{n(o.url,o.state,"popstate")})})}handleRouterEvent(n,o){n instanceof At?this.updateStateMemento():n instanceof Rt?this.commitTransition(o):n instanceof si?this.urlUpdateStrategy==="eager"&&(o.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(o),o)):n instanceof ai?(this.commitTransition(o),this.urlUpdateStrategy==="deferred"&&!o.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(o),o)):n instanceof _t&&(n.code===Be.GuardRejected||n.code===Be.NoDataFromResolver)?this.restoreHistory(o):n instanceof qr?this.restoreHistory(o,!0):n instanceof nt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:o,id:i}){let{replaceUrl:s,state:a}=o;if(this.location.isCurrentPathEqualTo(n)||s){let c=this.browserPageId,l=b(b({},a),this.generateNgRouterState(i,c));this.location.replaceState(n,"",l)}else{let c=b(b({},a),this.generateNgRouterState(i,this.browserPageId+1));this.location.go(n,"",c)}}restoreHistory(n,o=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,s=this.currentPageId-i;s!==0?this.location.historyGo(s):this.getCurrentUrlTree()===n.finalUrl&&s===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(o&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,o){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:o}:{navigationId:n}}};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();function vc(t,e){t.events.pipe(ye(r=>r instanceof nt||r instanceof _t||r instanceof qr||r instanceof Rt),H(r=>r instanceof nt||r instanceof Rt?0:(r instanceof _t?r.code===Be.Redirect||r.code===Be.SupersededByNewNavigation:!1)?2:1),ye(r=>r!==2),kt(1)).subscribe(()=>{e()})}var Q_={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},K_={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Ce=(()=>{var e,r;let n=class n{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=I(pv);stateManager=I(Jy);options=I(Gn,{optional:!0})||{};pendingTasks=I(Ut);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=I(mc);urlSerializer=I(Gt);location=I(We);urlHandlingStrategy=I(gc);_events=new ee;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=I(Xy);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=(r=(e=I(Jr,{optional:!0}))==null?void 0:e.flat())!=null?r:[];componentInputBindingEnabled=!!I(gi,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:i=>{this.console.warn(i)}}),this.subscribeToNavigationEvents()}eventsSubscription=new ue;subscribeToNavigationEvents(){let i=this.navigationTransitions.events.subscribe(s=>{try{let a=this.navigationTransitions.currentTransition,c=this.navigationTransitions.currentNavigation;if(a!==null&&c!==null){if(this.stateManager.handleRouterEvent(s,c),s instanceof _t&&s.code!==Be.Redirect&&s.code!==Be.SupersededByNewNavigation)this.navigated=!0;else if(s instanceof nt)this.navigated=!0;else if(s instanceof Yr){let l=s.navigationBehaviorOptions,d=this.urlHandlingStrategy.merge(s.url,a.currentRawUrl),h=b({browserUrl:a.extras.browserUrl,info:a.extras.info,skipLocationChange:a.extras.skipLocationChange,replaceUrl:a.extras.replaceUrl||this.urlUpdateStrategy==="eager"||q_(a.source)},l);this.scheduleNavigation(d,Za,null,h,{resolve:a.resolve,reject:a.reject,promise:a.promise})}}J_(s)&&this._events.next(s)}catch(a){this.navigationTransitions.transitionAbortSubject.next(a)}});this.eventsSubscription.add(i)}resetRootComponentType(i){this.routerState.root.component=i,this.navigationTransitions.rootComponentType=i}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Za,this.stateManager.restoredState())}setUpLocationChangeListener(){var i;(i=this.nonRouterCurrentEntryChangeSubscription)!=null||(this.nonRouterCurrentEntryChangeSubscription=this.stateManager.registerNonRouterCurrentEntryChangeListener((s,a,c)=>{this.navigateToSyncWithBrowser(s,c,a)}))}navigateToSyncWithBrowser(i,s,a){let c={replaceUrl:!0},l=a!=null&&a.navigationId?a:null;if(a){let h=b({},a);delete h.navigationId,delete h.\u0275routerPageId,Object.keys(h).length!==0&&(c.state=h)}let d=this.parseUrl(i);this.scheduleNavigation(d,s,l,c)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(i){this.config=i.map(kf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(i,s={}){let{relativeTo:a,queryParams:c,fragment:l,queryParamsHandling:d,preserveFragment:h}=s,g=h?this.currentUrlTree.fragment:l,p=null;switch(d!=null?d:this.options.defaultQueryParamsHandling){case"merge":p=b(b({},this.currentUrlTree.queryParams),c);break;case"preserve":p=this.currentUrlTree.queryParams;break;default:p=c||null}p!==null&&(p=this.removeEmptyProps(p));let v;try{let D=a?a.snapshot:this.routerState.snapshot.root;v=Ny(D)}catch{(typeof i[0]!="string"||i[0][0]!=="/")&&(i=[]),v=this.currentUrlTree.root}return Oy(v,i,p,g!=null?g:null)}navigateByUrl(i,s={skipLocationChange:!1}){let a=dn(i)?i:this.parseUrl(i),c=this.urlHandlingStrategy.merge(a,this.rawUrlTree);return this.scheduleNavigation(c,Za,null,s)}navigate(i,s={skipLocationChange:!1}){return X_(i),this.navigateByUrl(this.createUrlTree(i,s),s)}serializeUrl(i){return this.urlSerializer.serialize(i)}parseUrl(i){try{return this.urlSerializer.parse(i)}catch{return this.urlSerializer.parse("/")}}isActive(i,s){let a;if(s===!0?a=b({},Q_):s===!1?a=b({},K_):a=s,dn(i))return hy(this.currentUrlTree,i,a);let c=this.parseUrl(i);return hy(this.currentUrlTree,c,a)}removeEmptyProps(i){return Object.entries(i).reduce((s,[a,c])=>(c!=null&&(s[a]=c),s),{})}scheduleNavigation(i,s,a,c,l){if(this.disposed)return Promise.resolve(!1);let d,h,g;l?(d=l.resolve,h=l.reject,g=l.promise):g=new Promise((v,D)=>{d=v,h=D});let p=this.pendingTasks.add();return vc(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(p))}),this.navigationTransitions.handleNavigationRequest({source:s,restoredState:a,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:i,extras:c,resolve:d,reject:h,promise:g,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),g.catch(v=>Promise.reject(v))}};u(n,"\u0275fac",function(s){return new(s||n)}),u(n,"\u0275prov",A({token:n,factory:n.\u0275fac,providedIn:"root"}));let t=n;return t})();function X_(t){for(let e=0;e<t.length;e++)if(t[e]==null)throw new R(4008,!1)}function J_(t){return!(t instanceof ai)&&!(t instanceof Yr)}var yi=(()=>{let e=class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new ee;constructor(n,o,i,s,a,c){var d;this.router=n,this.route=o,this.tabIndexAttribute=i,this.renderer=s,this.el=a,this.locationStrategy=c;let l=(d=a.nativeElement.tagName)==null?void 0:d.toLowerCase();this.isAnchorElement=l==="a"||l==="area",this.isAnchorElement?this.subscription=n.events.subscribe(h=>{h instanceof nt&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(dn(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,o,i,s,a){let c=this.urlTree;if(c===null||this.isAnchorElement&&(n!==0||o||i||s||a||typeof this.target=="string"&&this.target!="_self"))return!0;let l={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(c,l),!this.isAnchorElement}ngOnDestroy(){var n;(n=this.subscription)==null||n.unsubscribe()}updateHref(){var i;let n=this.urlTree;this.href=n!==null&&this.locationStrategy?(i=this.locationStrategy)==null?void 0:i.prepareExternalUrl(this.router.serializeUrl(n)):null;let o=this.href===null?null:Mm(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",o)}applyAttributeValue(n,o){let i=this.renderer,s=this.el.nativeElement;o!==null?i.setAttribute(s,n,o):i.removeAttribute(s,n)}get urlTree(){return this.routerLinkInput===null?null:dn(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}};u(e,"\u0275fac",function(o){return new(o||e)(f(Ce),f(xe),Mt("tabindex"),f(rn),f(y),f(Ve))}),u(e,"\u0275dir",B({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(o,i){o&1&&Ie("click",function(a){return i.onClick(a.button,a.ctrlKey,a.shiftKey,a.altKey,a.metaKey)}),o&2&&ct("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",an],skipLocationChange:[2,"skipLocationChange","skipLocationChange",an],replaceUrl:[2,"replaceUrl","replaceUrl",an],routerLink:"routerLink"},features:[tt]}));let t=e;return t})();var vi=class{},ex=(()=>{let e=class e{preload(n,o){return o().pipe(pt(()=>P(null)))}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();var eD=(()=>{let e=class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,o,i,s,a){this.router=n,this.injector=i,this.preloadingStrategy=s,this.loader=a}setUpPreloading(){this.subscription=this.router.events.pipe(ye(n=>n instanceof nt),gt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,o){var s,a,c;let i=[];for(let l of o){l.providers&&!l._injector&&(l._injector=Fo(l.providers,n,`Route: ${l.path}`));let d=(s=l._injector)!=null?s:n,h=(a=l._loadedInjector)!=null?a:d;(l.loadChildren&&!l._loadedRoutes&&l.canLoad===void 0||l.loadComponent&&!l._loadedComponent)&&i.push(this.preloadConfig(d,l)),(l.children||l._loadedRoutes)&&i.push(this.processRoutes(h,(c=l.children)!=null?c:l._loadedRoutes))}return re(i).pipe(cr())}preloadConfig(n,o){return this.preloadingStrategy.preload(o,()=>{let i;o.loadChildren&&o.canLoad===void 0?i=this.loader.loadChildren(n,o):i=P(null);let s=i.pipe(le(a=>{var c;return a===null?P(void 0):(o._loadedRoutes=a.routes,o._loadedInjector=a.injector,this.processRoutes((c=a.injector)!=null?c:n,a.routes))}));if(o.loadComponent&&!o._loadedComponent){let a=this.loader.loadComponent(o);return re([s,a]).pipe(cr())}else return s})}};u(e,"\u0275fac",function(o){return new(o||e)(k(Ce),k(Ea),k(se),k(vi),k(pc))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),tD=new N(""),tx=(()=>{let e=class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(n,o,i,s,a={}){this.urlSerializer=n,this.transitions=o,this.viewportScroller=i,this.zone=s,this.options=a,a.scrollPositionRestoration||(a.scrollPositionRestoration="disabled"),a.anchorScrolling||(a.anchorScrolling="disabled")}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof At?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof nt?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof Rt&&n.code===Wr.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Zr&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,o){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Zr(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,o))})},0)})}ngOnDestroy(){var n,o;(n=this.routerEventsSubscription)==null||n.unsubscribe(),(o=this.scrollEventsSubscription)==null||o.unsubscribe()}};u(e,"\u0275fac",function(o){tv()}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();function nx(t){return t.routerState.root}function Di(t,e){return{\u0275kind:t,\u0275providers:e}}function rx(){let t=I(te);return e=>{var i,s;let r=t.get(Et);if(e!==r.components[0])return;let n=t.get(Ce),o=t.get(nD);t.get(Vf)===1&&n.initialNavigation(),(i=t.get(iD,null,$.Optional))==null||i.setUpPreloading(),(s=t.get(tD,null,$.Optional))==null||s.init(),n.resetRootComponentType(r.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var nD=new N("",{factory:()=>new ee}),Vf=new N("",{providedIn:"root",factory:()=>1});function rD(){let t=[{provide:Vf,useValue:0},Vd(()=>{let e=I(te);return e.get(Wd,Promise.resolve()).then(()=>new Promise(n=>{let o=e.get(Ce),i=e.get(nD);vc(o,()=>{n(!0)}),e.get(mc).afterPreactivation=()=>(n(!0),i.closed?P(void 0):i),o.initialNavigation()}))})];return Di(2,t)}function oD(){let t=[Vd(()=>{I(Ce).setUpLocationChangeListener()}),{provide:Vf,useValue:2}];return Di(3,t)}var iD=new N("");function sD(t){return Di(0,[{provide:iD,useExisting:eD},{provide:vi,useExisting:t}])}function aD(){return Di(8,[Rf,{provide:gi,useExisting:Rf}])}function cD(t){No("NgRouterViewTransitions");let e=[{provide:Pf,useValue:Ky},{provide:Ff,useValue:b({skipNextTransition:!!(t!=null&&t.skipInitialTransition)},t)}];return Di(9,e)}var lD=[We,{provide:Gt,useClass:un},Ce,Nt,{provide:xe,useFactory:nx,deps:[Ce]},pc,[]],ox=(()=>{let e=class e{constructor(){}static forRoot(n,o){return{ngModule:e,providers:[lD,[],{provide:Jr,multi:!0,useValue:n},[],o!=null&&o.errorHandler?{provide:jf,useValue:o.errorHandler}:[],{provide:Gn,useValue:o||{}},o!=null&&o.useHash?sx():ax(),ix(),o!=null&&o.preloadingStrategy?sD(o.preloadingStrategy).\u0275providers:[],o!=null&&o.initialNavigation?cx(o):[],o!=null&&o.bindToComponentInputs?aD().\u0275providers:[],o!=null&&o.enableViewTransitions?cD().\u0275providers:[],lx()]}}static forChild(n){return{ngModule:e,providers:[{provide:Jr,multi:!0,useValue:n}]}}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Ae({type:e})),u(e,"\u0275inj",Te({}));let t=e;return t})();function ix(){return{provide:tD,useFactory:()=>{let t=I(Bv),e=I(m),r=I(Gn),n=I(mc),o=I(Gt);return r.scrollOffset&&t.setOffset(r.scrollOffset),new tx(o,n,t,e,r)}}}function sx(){return{provide:Ve,useClass:Zd}}function ax(){return{provide:Ve,useClass:_a}}function cx(t){return[t.initialNavigation==="disabled"?oD().\u0275providers:[],t.initialNavigation==="enabledBlocking"?rD().\u0275providers:[]]}var Lf=new N("");function lx(){return[{provide:Lf,useFactory:rx},{provide:Bd,multi:!0,useExisting:Lf}]}var DD=(()=>{let e=class e{_renderer;_elementRef;onChange=n=>{};onTouched=()=>{};constructor(n,o){this._renderer=n,this._elementRef=o}setProperty(n,o){this._renderer.setProperty(this._elementRef.nativeElement,n,o)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(rn),f(y))}),u(e,"\u0275dir",B({type:e}));let t=e;return t})(),ux=(()=>{let e=class e extends DD{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",B({type:e,features:[ne]}));let t=e;return t})(),qn=new N("");var dx={provide:qn,useExisting:$e(()=>ID),multi:!0};function fx(){let t=Ge()?Ge().getUserAgent():"";return/android (\d+)/.test(t.toLowerCase())}var hx=new N(""),ID=(()=>{let e=class e extends DD{_compositionMode;_composing=!1;constructor(n,o,i){super(n,o),this._compositionMode=i,this._compositionMode==null&&(this._compositionMode=!fx())}writeValue(n){let o=n==null?"":n;this.setProperty("value",o)}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(rn),f(y),f(hx,8))}),u(e,"\u0275dir",B({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(o,i){o&1&&Ie("input",function(a){return i._handleInput(a.target.value)})("blur",function(){return i.onTouched()})("compositionstart",function(){return i._compositionStart()})("compositionend",function(a){return i._compositionEnd(a.target.value)})},standalone:!1,features:[Re([dx]),ne]}));let t=e;return t})();function px(t){return t==null||CD(t)===0}function CD(t){return t==null?null:Array.isArray(t)||typeof t=="string"?t.length:t instanceof Set?t.size:null}var Wt=new N(""),bD=new N("");function gx(t){return e=>{if(e.value==null||t==null)return null;let r=parseFloat(e.value);return!isNaN(r)&&r<t?{min:{min:t,actual:e.value}}:null}}function mx(t){return e=>{if(e.value==null||t==null)return null;let r=parseFloat(e.value);return!isNaN(r)&&r>t?{max:{max:t,actual:e.value}}:null}}function vx(t){return px(t.value)?{required:!0}:null}function yx(t){return e=>{var n,o;let r=(o=(n=e.value)==null?void 0:n.length)!=null?o:CD(e.value);return r!==null&&r>t?{maxlength:{requiredLength:t,actualLength:r}}:null}}function dD(t){return null}function ED(t){return t!=null}function wD(t){return Vn(t)?re(t):t}function MD(t){let e={};return t.forEach(r=>{e=r!=null?b(b({},e),r):e}),Object.keys(e).length===0?null:e}function SD(t,e){return e.map(r=>r(t))}function Dx(t){return!t.validate}function TD(t){return t.map(e=>Dx(e)?e:r=>e.validate(r))}function Ix(t){if(!t)return null;let e=t.filter(ED);return e.length==0?null:function(r){return MD(SD(r,e))}}function Uf(t){return t!=null?Ix(TD(t)):null}function Cx(t){if(!t)return null;let e=t.filter(ED);return e.length==0?null:function(r){let n=SD(r,e).map(wD);return ll(n).pipe(H(MD))}}function Hf(t){return t!=null?Cx(TD(t)):null}function fD(t,e){return t===null?[e]:Array.isArray(t)?[...t,e]:[t,e]}function bx(t){return t._rawValidators}function Ex(t){return t._rawAsyncValidators}function Bf(t){return t?Array.isArray(t)?t:[t]:[]}function Dc(t,e){return Array.isArray(t)?t.includes(e):t===e}function hD(t,e){let r=Bf(e);return Bf(t).forEach(o=>{Dc(r,o)||r.push(o)}),r}function pD(t,e){return Bf(e).filter(r=>!Dc(t,r))}var Ic=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(e){this._rawValidators=e||[],this._composedValidatorFn=Uf(this._rawValidators)}_setAsyncValidators(e){this._rawAsyncValidators=e||[],this._composedAsyncValidatorFn=Hf(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(e){this._onDestroyCallbacks.push(e)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(e=>e()),this._onDestroyCallbacks=[]}reset(e=void 0){this.control&&this.control.reset(e)}hasError(e,r){return this.control?this.control.hasError(e,r):!1}getError(e,r){return this.control?this.control.getError(e,r):null}},no=class extends Ic{name;get formDirective(){return null}get path(){return null}},Wn=class extends Ic{_parent=null;name=null;valueAccessor=null},Cc=class{_cd;constructor(e){this._cd=e}get isTouched(){var e,r,n,o,i;return(n=(r=(e=this._cd)==null?void 0:e.control)==null?void 0:r._touched)==null||n.call(r),!!((i=(o=this._cd)==null?void 0:o.control)!=null&&i.touched)}get isUntouched(){var e,r;return!!((r=(e=this._cd)==null?void 0:e.control)!=null&&r.untouched)}get isPristine(){var e,r,n,o,i;return(n=(r=(e=this._cd)==null?void 0:e.control)==null?void 0:r._pristine)==null||n.call(r),!!((i=(o=this._cd)==null?void 0:o.control)!=null&&i.pristine)}get isDirty(){var e,r;return!!((r=(e=this._cd)==null?void 0:e.control)!=null&&r.dirty)}get isValid(){var e,r,n,o,i;return(n=(r=(e=this._cd)==null?void 0:e.control)==null?void 0:r._status)==null||n.call(r),!!((i=(o=this._cd)==null?void 0:o.control)!=null&&i.valid)}get isInvalid(){var e,r;return!!((r=(e=this._cd)==null?void 0:e.control)!=null&&r.invalid)}get isPending(){var e,r;return!!((r=(e=this._cd)==null?void 0:e.control)!=null&&r.pending)}get isSubmitted(){var e,r,n;return(r=(e=this._cd)==null?void 0:e._submitted)==null||r.call(e),!!((n=this._cd)!=null&&n.submitted)}},wx={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},CH=L(b({},wx),{"[class.ng-submitted]":"isSubmitted"}),bH=(()=>{let e=class e extends Cc{constructor(n){super(n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(Wn,2))}),u(e,"\u0275dir",B({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(o,i){o&2&&Da("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)},standalone:!1,features:[ne]}));let t=e;return t})(),EH=(()=>{let e=class e extends Cc{constructor(n){super(n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(no,10))}),u(e,"\u0275dir",B({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(o,i){o&2&&Da("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)("ng-submitted",i.isSubmitted)},standalone:!1,features:[ne]}));let t=e;return t})();var Ii="VALID",yc="INVALID",eo="PENDING",Ci="DISABLED",ro=class{},bc=class extends ro{value;source;constructor(e,r){super(),this.value=e,this.source=r}},Ei=class extends ro{pristine;source;constructor(e,r){super(),this.pristine=e,this.source=r}},wi=class extends ro{touched;source;constructor(e,r){super(),this.touched=e,this.source=r}},to=class extends ro{status;source;constructor(e,r){super(),this.status=e,this.source=r}};function _D(t){return(Mc(t)?t.validators:t)||null}function Mx(t){return Array.isArray(t)?Uf(t):t||null}function xD(t,e){return(Mc(e)?e.asyncValidators:t)||null}function Sx(t){return Array.isArray(t)?Hf(t):t||null}function Mc(t){return t!=null&&!Array.isArray(t)&&typeof t=="object"}function Tx(t,e,r){let n=t.controls;if(!(e?Object.keys(n):n).length)throw new R(1e3,"");if(!n[r])throw new R(1001,"")}function _x(t,e,r){t._forEachChild((n,o)=>{if(r[o]===void 0)throw new R(1002,"")})}var Ec=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(e,r){this._assignValidators(e),this._assignAsyncValidators(r)}get validator(){return this._composedValidatorFn}set validator(e){this._rawValidators=this._composedValidatorFn=e}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(e){this._rawAsyncValidators=this._composedAsyncValidatorFn=e}get parent(){return this._parent}get status(){return $t(this.statusReactive)}set status(e){$t(()=>this.statusReactive.set(e))}_status=Uo(()=>this.statusReactive());statusReactive=Ro(void 0);get valid(){return this.status===Ii}get invalid(){return this.status===yc}get pending(){return this.status==eo}get disabled(){return this.status===Ci}get enabled(){return this.status!==Ci}errors;get pristine(){return $t(this.pristineReactive)}set pristine(e){$t(()=>this.pristineReactive.set(e))}_pristine=Uo(()=>this.pristineReactive());pristineReactive=Ro(!0);get dirty(){return!this.pristine}get touched(){return $t(this.touchedReactive)}set touched(e){$t(()=>this.touchedReactive.set(e))}_touched=Uo(()=>this.touchedReactive());touchedReactive=Ro(!1);get untouched(){return!this.touched}_events=new ee;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(e){this._assignValidators(e)}setAsyncValidators(e){this._assignAsyncValidators(e)}addValidators(e){this.setValidators(hD(e,this._rawValidators))}addAsyncValidators(e){this.setAsyncValidators(hD(e,this._rawAsyncValidators))}removeValidators(e){this.setValidators(pD(e,this._rawValidators))}removeAsyncValidators(e){this.setAsyncValidators(pD(e,this._rawAsyncValidators))}hasValidator(e){return Dc(this._rawValidators,e)}hasAsyncValidator(e){return Dc(this._rawAsyncValidators,e)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(e={}){var o;let r=this.touched===!1;this.touched=!0;let n=(o=e.sourceControl)!=null?o:this;this._parent&&!e.onlySelf&&this._parent.markAsTouched(L(b({},e),{sourceControl:n})),r&&e.emitEvent!==!1&&this._events.next(new wi(!0,n))}markAllAsTouched(e={}){this.markAsTouched({onlySelf:!0,emitEvent:e.emitEvent,sourceControl:this}),this._forEachChild(r=>r.markAllAsTouched(e))}markAsUntouched(e={}){var o;let r=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let n=(o=e.sourceControl)!=null?o:this;this._forEachChild(i=>{i.markAsUntouched({onlySelf:!0,emitEvent:e.emitEvent,sourceControl:n})}),this._parent&&!e.onlySelf&&this._parent._updateTouched(e,n),r&&e.emitEvent!==!1&&this._events.next(new wi(!1,n))}markAsDirty(e={}){var o;let r=this.pristine===!0;this.pristine=!1;let n=(o=e.sourceControl)!=null?o:this;this._parent&&!e.onlySelf&&this._parent.markAsDirty(L(b({},e),{sourceControl:n})),r&&e.emitEvent!==!1&&this._events.next(new Ei(!1,n))}markAsPristine(e={}){var o;let r=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let n=(o=e.sourceControl)!=null?o:this;this._forEachChild(i=>{i.markAsPristine({onlySelf:!0,emitEvent:e.emitEvent})}),this._parent&&!e.onlySelf&&this._parent._updatePristine(e,n),r&&e.emitEvent!==!1&&this._events.next(new Ei(!0,n))}markAsPending(e={}){var n;this.status=eo;let r=(n=e.sourceControl)!=null?n:this;e.emitEvent!==!1&&(this._events.next(new to(this.status,r)),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.markAsPending(L(b({},e),{sourceControl:r}))}disable(e={}){var o;let r=this._parentMarkedDirty(e.onlySelf);this.status=Ci,this.errors=null,this._forEachChild(i=>{i.disable(L(b({},e),{onlySelf:!0}))}),this._updateValue();let n=(o=e.sourceControl)!=null?o:this;e.emitEvent!==!1&&(this._events.next(new bc(this.value,n)),this._events.next(new to(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(L(b({},e),{skipPristineCheck:r}),this),this._onDisabledChange.forEach(i=>i(!0))}enable(e={}){let r=this._parentMarkedDirty(e.onlySelf);this.status=Ii,this._forEachChild(n=>{n.enable(L(b({},e),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent}),this._updateAncestors(L(b({},e),{skipPristineCheck:r}),this),this._onDisabledChange.forEach(n=>n(!1))}_updateAncestors(e,r){this._parent&&!e.onlySelf&&(this._parent.updateValueAndValidity(e),e.skipPristineCheck||this._parent._updatePristine({},r),this._parent._updateTouched({},r))}setParent(e){this._parent=e}getRawValue(){return this.value}updateValueAndValidity(e={}){var n;if(this._setInitialStatus(),this._updateValue(),this.enabled){let o=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Ii||this.status===eo)&&this._runAsyncValidator(o,e.emitEvent)}let r=(n=e.sourceControl)!=null?n:this;e.emitEvent!==!1&&(this._events.next(new bc(this.value,r)),this._events.next(new to(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.updateValueAndValidity(L(b({},e),{sourceControl:r}))}_updateTreeValidity(e={emitEvent:!0}){this._forEachChild(r=>r._updateTreeValidity(e)),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Ci:Ii}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(e,r){if(this.asyncValidator){this.status=eo,this._hasOwnPendingAsyncValidator={emitEvent:r!==!1};let n=wD(this.asyncValidator(this));this._asyncValidationSubscription=n.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:r,shouldHaveEmitted:e})})}}_cancelExistingSubscription(){var e,r;if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=(r=(e=this._hasOwnPendingAsyncValidator)==null?void 0:e.emitEvent)!=null?r:!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(e,r={}){this.errors=e,this._updateControlsErrors(r.emitEvent!==!1,this,r.shouldHaveEmitted)}get(e){let r=e;return r==null||(Array.isArray(r)||(r=r.split(".")),r.length===0)?null:r.reduce((n,o)=>n&&n._find(o),this)}getError(e,r){let n=r?this.get(r):this;return n&&n.errors?n.errors[e]:null}hasError(e,r){return!!this.getError(e,r)}get root(){let e=this;for(;e._parent;)e=e._parent;return e}_updateControlsErrors(e,r,n){this.status=this._calculateStatus(),e&&this.statusChanges.emit(this.status),(e||n)&&this._events.next(new to(this.status,r)),this._parent&&this._parent._updateControlsErrors(e,r,n)}_initObservables(){this.valueChanges=new ie,this.statusChanges=new ie}_calculateStatus(){return this._allControlsDisabled()?Ci:this.errors?yc:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(eo)?eo:this._anyControlsHaveStatus(yc)?yc:Ii}_anyControlsHaveStatus(e){return this._anyControls(r=>r.status===e)}_anyControlsDirty(){return this._anyControls(e=>e.dirty)}_anyControlsTouched(){return this._anyControls(e=>e.touched)}_updatePristine(e,r){let n=!this._anyControlsDirty(),o=this.pristine!==n;this.pristine=n,this._parent&&!e.onlySelf&&this._parent._updatePristine(e,r),o&&this._events.next(new Ei(this.pristine,r))}_updateTouched(e={},r){this.touched=this._anyControlsTouched(),this._events.next(new wi(this.touched,r)),this._parent&&!e.onlySelf&&this._parent._updateTouched(e,r)}_onDisabledChange=[];_registerOnCollectionChange(e){this._onCollectionChange=e}_setUpdateStrategy(e){Mc(e)&&e.updateOn!=null&&(this._updateOn=e.updateOn)}_parentMarkedDirty(e){let r=this._parent&&this._parent.dirty;return!e&&!!r&&!this._parent._anyControlsDirty()}_find(e){return null}_assignValidators(e){this._rawValidators=Array.isArray(e)?e.slice():e,this._composedValidatorFn=Mx(this._rawValidators)}_assignAsyncValidators(e){this._rawAsyncValidators=Array.isArray(e)?e.slice():e,this._composedAsyncValidatorFn=Sx(this._rawAsyncValidators)}},wc=class extends Ec{constructor(e,r,n){super(_D(r),xD(n,r)),this.controls=e,this._initObservables(),this._setUpdateStrategy(r),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(e,r){return this.controls[e]?this.controls[e]:(this.controls[e]=r,r.setParent(this),r._registerOnCollectionChange(this._onCollectionChange),r)}addControl(e,r,n={}){this.registerControl(e,r),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}removeControl(e,r={}){this.controls[e]&&this.controls[e]._registerOnCollectionChange(()=>{}),delete this.controls[e],this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}setControl(e,r,n={}){this.controls[e]&&this.controls[e]._registerOnCollectionChange(()=>{}),delete this.controls[e],r&&this.registerControl(e,r),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}contains(e){return this.controls.hasOwnProperty(e)&&this.controls[e].enabled}setValue(e,r={}){_x(this,!0,e),Object.keys(e).forEach(n=>{Tx(this,!0,n),this.controls[n].setValue(e[n],{onlySelf:!0,emitEvent:r.emitEvent})}),this.updateValueAndValidity(r)}patchValue(e,r={}){e!=null&&(Object.keys(e).forEach(n=>{let o=this.controls[n];o&&o.patchValue(e[n],{onlySelf:!0,emitEvent:r.emitEvent})}),this.updateValueAndValidity(r))}reset(e={},r={}){this._forEachChild((n,o)=>{n.reset(e?e[o]:null,{onlySelf:!0,emitEvent:r.emitEvent})}),this._updatePristine(r,this),this._updateTouched(r,this),this.updateValueAndValidity(r)}getRawValue(){return this._reduceChildren({},(e,r,n)=>(e[n]=r.getRawValue(),e))}_syncPendingControls(){let e=this._reduceChildren(!1,(r,n)=>n._syncPendingControls()?!0:r);return e&&this.updateValueAndValidity({onlySelf:!0}),e}_forEachChild(e){Object.keys(this.controls).forEach(r=>{let n=this.controls[r];n&&e(n,r)})}_setUpControls(){this._forEachChild(e=>{e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(e){for(let[r,n]of Object.entries(this.controls))if(this.contains(r)&&e(n))return!0;return!1}_reduceValue(){let e={};return this._reduceChildren(e,(r,n,o)=>((n.enabled||this.disabled)&&(r[o]=n.value),r))}_reduceChildren(e,r){let n=e;return this._forEachChild((o,i)=>{n=r(n,o,i)}),n}_allControlsDisabled(){for(let e of Object.keys(this.controls))if(this.controls[e].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(e){return this.controls.hasOwnProperty(e)?this.controls[e]:null}};var $f=new N("",{providedIn:"root",factory:()=>zf}),zf="always";function xx(t,e){return[...e.path,t]}function AD(t,e,r=zf){var n,o;RD(t,e),e.valueAccessor.writeValue(t.value),(t.disabled||r==="always")&&((o=(n=e.valueAccessor).setDisabledState)==null||o.call(n,t.disabled)),Rx(t,e),Ox(t,e),Nx(t,e),Ax(t,e)}function gD(t,e){t.forEach(r=>{r.registerOnValidatorChange&&r.registerOnValidatorChange(e)})}function Ax(t,e){if(e.valueAccessor.setDisabledState){let r=n=>{e.valueAccessor.setDisabledState(n)};t.registerOnDisabledChange(r),e._registerOnDestroy(()=>{t._unregisterOnDisabledChange(r)})}}function RD(t,e){let r=bx(t);e.validator!==null?t.setValidators(fD(r,e.validator)):typeof r=="function"&&t.setValidators([r]);let n=Ex(t);e.asyncValidator!==null?t.setAsyncValidators(fD(n,e.asyncValidator)):typeof n=="function"&&t.setAsyncValidators([n]);let o=()=>t.updateValueAndValidity();gD(e._rawValidators,o),gD(e._rawAsyncValidators,o)}function Rx(t,e){e.valueAccessor.registerOnChange(r=>{t._pendingValue=r,t._pendingChange=!0,t._pendingDirty=!0,t.updateOn==="change"&&ND(t,e)})}function Nx(t,e){e.valueAccessor.registerOnTouched(()=>{t._pendingTouched=!0,t.updateOn==="blur"&&t._pendingChange&&ND(t,e),t.updateOn!=="submit"&&t.markAsTouched()})}function ND(t,e){t._pendingDirty&&t.markAsDirty(),t.setValue(t._pendingValue,{emitModelToViewChange:!1}),e.viewToModelUpdate(t._pendingValue),t._pendingChange=!1}function Ox(t,e){let r=(n,o)=>{e.valueAccessor.writeValue(n),o&&e.viewToModelUpdate(n)};t.registerOnChange(r),e._registerOnDestroy(()=>{t._unregisterOnChange(r)})}function kx(t,e){t==null,RD(t,e)}function Px(t,e){if(!t.hasOwnProperty("model"))return!1;let r=t.model;return r.isFirstChange()?!0:!Object.is(e,r.currentValue)}function Fx(t){return Object.getPrototypeOf(t.constructor)===ux}function jx(t,e){t._syncPendingControls(),e.forEach(r=>{let n=r.control;n.updateOn==="submit"&&n._pendingChange&&(r.viewToModelUpdate(n._pendingValue),n._pendingChange=!1)})}function Lx(t,e){if(!e)return null;Array.isArray(e);let r,n,o;return e.forEach(i=>{i.constructor===ID?r=i:Fx(i)?n=i:o=i}),o||n||r||null}var Vx={provide:no,useExisting:$e(()=>Bx)},bi=Promise.resolve(),Bx=(()=>{let e=class e extends no{callSetDisabledState;get submitted(){return $t(this.submittedReactive)}_submitted=Uo(()=>this.submittedReactive());submittedReactive=Ro(!1);_directives=new Set;form;ngSubmit=new ie;options;constructor(n,o,i){super(),this.callSetDisabledState=i,this.form=new wc({},Uf(n),Hf(o))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(n){bi.then(()=>{let o=this._findContainer(n.path);n.control=o.registerControl(n.name,n.control),AD(n.control,n,this.callSetDisabledState),n.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(n)})}getControl(n){return this.form.get(n.path)}removeControl(n){bi.then(()=>{let o=this._findContainer(n.path);o&&o.removeControl(n.name),this._directives.delete(n)})}addFormGroup(n){bi.then(()=>{let o=this._findContainer(n.path),i=new wc({});kx(i,n),o.registerControl(n.name,i),i.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(n){bi.then(()=>{let o=this._findContainer(n.path);o&&o.removeControl(n.name)})}getFormGroup(n){return this.form.get(n.path)}updateModel(n,o){bi.then(()=>{this.form.get(n.path).setValue(o)})}setValue(n){this.control.setValue(n)}onSubmit(n){var o;return this.submittedReactive.set(!0),jx(this.form,this._directives),this.ngSubmit.emit(n),((o=n==null?void 0:n.target)==null?void 0:o.method)==="dialog"}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this.submittedReactive.set(!1)}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(n){return n.pop(),n.length?this.form.get(n):this.form}};u(e,"\u0275fac",function(o){return new(o||e)(f(Wt,10),f(bD,10),f($f,8))}),u(e,"\u0275dir",B({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(o,i){o&1&&Ie("submit",function(a){return i.onSubmit(a)})("reset",function(){return i.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[Re([Vx]),ne]}));let t=e;return t})();function mD(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}function vD(t){return typeof t=="object"&&t!==null&&Object.keys(t).length===2&&"value"in t&&"disabled"in t}var Ux=class extends Ec{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(e=null,r,n){super(_D(r),xD(n,r)),this._applyFormState(e),this._setUpdateStrategy(r),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Mc(r)&&(r.nonNullable||r.initialValueIsDefault)&&(vD(e)?this.defaultValue=e.value:this.defaultValue=e)}setValue(e,r={}){this.value=this._pendingValue=e,this._onChange.length&&r.emitModelToViewChange!==!1&&this._onChange.forEach(n=>n(this.value,r.emitViewToModelChange!==!1)),this.updateValueAndValidity(r)}patchValue(e,r={}){this.setValue(e,r)}reset(e=this.defaultValue,r={}){this._applyFormState(e),this.markAsPristine(r),this.markAsUntouched(r),this.setValue(this.value,r),this._pendingChange=!1}_updateValue(){}_anyControls(e){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(e){this._onChange.push(e)}_unregisterOnChange(e){mD(this._onChange,e)}registerOnDisabledChange(e){this._onDisabledChange.push(e)}_unregisterOnDisabledChange(e){mD(this._onDisabledChange,e)}_forEachChild(e){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(e){vD(e)?(this.value=this._pendingValue=e.value,e.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=e}};var Hx={provide:Wn,useExisting:$e(()=>$x)},yD=Promise.resolve(),$x=(()=>{let e=class e extends Wn{_changeDetectorRef;callSetDisabledState;control=new Ux;_registered=!1;viewModel;name="";isDisabled;model;options;update=new ie;constructor(n,o,i,s,a,c){super(),this._changeDetectorRef=a,this.callSetDisabledState=c,this._parent=n,this._setValidators(o),this._setAsyncValidators(i),this.valueAccessor=Lx(this,s)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){let o=n.name.previousValue;this.formDirective.removeControl({name:o,path:this._getPath(o)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),Px(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){AD(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(n){yD.then(()=>{var o;this.control.setValue(n,{emitViewToModelChange:!1}),(o=this._changeDetectorRef)==null||o.markForCheck()})}_updateDisabled(n){let o=n.isDisabled.currentValue,i=o!==0&&an(o);yD.then(()=>{var s;i&&!this.control.disabled?this.control.disable():!i&&this.control.disabled&&this.control.enable(),(s=this._changeDetectorRef)==null||s.markForCheck()})}_getPath(n){return this._parent?xx(n,this._parent):[n]}};u(e,"ngAcceptInputType_isDisabled"),u(e,"\u0275fac",function(o){return new(o||e)(f(no,9),f(Wt,10),f(bD,10),f(qn,10),f(C,8),f($f,8))}),u(e,"\u0275dir",B({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[Re([Hx]),ne,tt]}));let t=e;return t})();var MH=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275dir",B({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1}));let t=e;return t})();function zx(t){return typeof t=="number"?t:parseInt(t,10)}function OD(t){return typeof t=="number"?t:parseFloat(t)}var Sc=(()=>{let e=class e{_validator=dD;_onChange;_enabled;ngOnChanges(n){if(this.inputName in n){let o=this.normalizeInput(n[this.inputName].currentValue);this._enabled=this.enabled(o),this._validator=this._enabled?this.createValidator(o):dD,this._onChange&&this._onChange()}}validate(n){return this._validator(n)}registerOnValidatorChange(n){this._onChange=n}enabled(n){return n!=null}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275dir",B({type:e,features:[tt]}));let t=e;return t})(),Gx={provide:Wt,useExisting:$e(()=>Gf),multi:!0},Gf=(()=>{let e=class e extends Sc{max;inputName="max";normalizeInput=n=>OD(n);createValidator=n=>mx(n)};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",B({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(o,i){o&2&&ct("max",i._enabled?i.max:null)},inputs:{max:"max"},standalone:!1,features:[Re([Gx]),ne]}));let t=e;return t})(),Wx={provide:Wt,useExisting:$e(()=>Wf),multi:!0},Wf=(()=>{let e=class e extends Sc{min;inputName="min";normalizeInput=n=>OD(n);createValidator=n=>gx(n)};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",B({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(o,i){o&2&&ct("min",i._enabled?i.min:null)},inputs:{min:"min"},standalone:!1,features:[Re([Wx]),ne]}));let t=e;return t})(),qx={provide:Wt,useExisting:$e(()=>Zx),multi:!0};var Zx=(()=>{let e=class e extends Sc{required;inputName="required";normalizeInput=an;createValidator=n=>vx;enabled(n){return n}};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",B({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(o,i){o&2&&ct("required",i._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[Re([qx]),ne]}));let t=e;return t})();var Yx={provide:Wt,useExisting:$e(()=>Qx),multi:!0},Qx=(()=>{let e=class e extends Sc{maxlength;inputName="maxlength";normalizeInput=n=>zx(n);createValidator=n=>yx(n)};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",B({type:e,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(o,i){o&2&&ct("maxlength",i._enabled?i.maxlength:null)},inputs:{maxlength:"maxlength"},standalone:!1,features:[Re([Yx]),ne]}));let t=e;return t})();var Kx=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Ae({type:e})),u(e,"\u0275inj",Te({}));let t=e;return t})();var SH=(()=>{let e=class e{static withConfig(n){var o;return{ngModule:e,providers:[{provide:$f,useValue:(o=n.callSetDisabledState)!=null?o:zf}]}}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Ae({type:e})),u(e,"\u0275inj",Te({imports:[Kx]}));let t=e;return t})();var Tc=t=>Xx(t),Mi=(t,e)=>(typeof t=="string"&&(e=t,t=void 0),Tc(t).includes(e)),Xx=(t=window)=>{if(typeof t>"u")return[];t.Ionic=t.Ionic||{};let e=t.Ionic.platforms;return e==null&&(e=t.Ionic.platforms=Jx(t),e.forEach(r=>t.document.documentElement.classList.add(`plt-${r}`))),e},Jx=t=>{let e=Kn.get("platform");return Object.keys(kD).filter(r=>{let n=e==null?void 0:e[r];return typeof n=="function"?n(t):kD[r](t)})},eA=t=>_c(t)&&!FD(t),qf=t=>!!(Zn(t,/iPad/i)||Zn(t,/Macintosh/i)&&_c(t)),tA=t=>Zn(t,/iPhone/i),nA=t=>Zn(t,/iPhone|iPod/i)||qf(t),PD=t=>Zn(t,/android|sink/i),rA=t=>PD(t)&&!Zn(t,/mobile/i),oA=t=>{let e=t.innerWidth,r=t.innerHeight,n=Math.min(e,r),o=Math.max(e,r);return n>390&&n<520&&o>620&&o<800},iA=t=>{let e=t.innerWidth,r=t.innerHeight,n=Math.min(e,r),o=Math.max(e,r);return qf(t)||rA(t)||n>460&&n<820&&o>780&&o<1400},_c=t=>lA(t,"(any-pointer:coarse)"),sA=t=>!_c(t),FD=t=>jD(t)||LD(t),jD=t=>!!(t.cordova||t.phonegap||t.PhoneGap),LD=t=>{let e=t.Capacitor;return!!(e!=null&&e.isNative||e!=null&&e.isNativePlatform&&e.isNativePlatform())},aA=t=>Zn(t,/electron/i),cA=t=>{var e;return!!(!((e=t.matchMedia)===null||e===void 0)&&e.call(t,"(display-mode: standalone)").matches||t.navigator.standalone)},Zn=(t,e)=>e.test(t.navigator.userAgent),lA=(t,e)=>{var r;return(r=t.matchMedia)===null||r===void 0?void 0:r.call(t,e).matches},kD={ipad:qf,iphone:tA,ios:nA,android:PD,phablet:oA,tablet:iA,cordova:jD,capacitor:LD,electron:aA,pwa:cA,mobile:_c,mobileweb:eA,desktop:sA,hybrid:FD},uA,Zf=t=>t&&dh(t)||uA;var jH=t=>{try{if(t instanceof Kf)return t.value;if(!dA()||typeof t!="string"||t==="")return t;if(t.includes("onload="))return"";let e=document.createDocumentFragment(),r=document.createElement("div");e.appendChild(r),r.innerHTML=t,hA.forEach(s=>{let a=e.querySelectorAll(s);for(let c=a.length-1;c>=0;c--){let l=a[c];l.parentNode?l.parentNode.removeChild(l):e.removeChild(l);let d=Qf(l);for(let h=0;h<d.length;h++)Yf(d[h])}});let n=Qf(e);for(let s=0;s<n.length;s++)Yf(n[s]);let o=document.createElement("div");o.appendChild(e);let i=o.querySelector("div");return i!==null?i.innerHTML:o.innerHTML}catch(e){return Ni("sanitizeDOMString",e),""}},Yf=t=>{if(t.nodeType&&t.nodeType!==1)return;if(typeof NamedNodeMap<"u"&&!(t.attributes instanceof NamedNodeMap)){t.remove();return}for(let r=t.attributes.length-1;r>=0;r--){let n=t.attributes.item(r),o=n.name;if(!fA.includes(o.toLowerCase())){t.removeAttribute(o);continue}let i=n.value,s=t[o];(i!=null&&i.toLowerCase().includes("javascript:")||s!=null&&s.toLowerCase().includes("javascript:"))&&t.removeAttribute(o)}let e=Qf(t);for(let r=0;r<e.length;r++)Yf(e[r])},Qf=t=>t.children!=null?t.children:t.childNodes,dA=()=>{var t;let e=window,r=(t=e==null?void 0:e.Ionic)===null||t===void 0?void 0:t.config;return r?r.get?r.get("sanitizerEnabled",!0):r.sanitizerEnabled===!0||r.sanitizerEnabled===void 0:!0},fA=["class","id","href","src","name","slot"],hA=["script","style","iframe","meta","link","object","embed"],Kf=class{constructor(e){this.value=e}};var LH=!1;var BH=(t,e)=>typeof t=="string"&&t.length>0?Object.assign({"ion-color":!0,[`ion-color-${t}`]:!0},e):e,pA=t=>t!==void 0?(Array.isArray(t)?t:t.split(" ")).filter(r=>r!=null).map(r=>r.trim()).filter(r=>r!==""):[],UH=t=>{let e={};return pA(t).forEach(r=>e[r]=!0),e};var VD=()=>{let t,e;return{attachViewToDom:(c,l,...d)=>ve(void 0,[c,l,...d],function*(o,i,s={},a=[]){var h,g;t=o;let p;if(i){let D=typeof i=="string"?(h=t.ownerDocument)===null||h===void 0?void 0:h.createElement(i):i;a.forEach(x=>D.classList.add(x)),Object.assign(D,s),t.appendChild(D),p=D,yield new Promise(x=>mn(D,x))}else if(t.children.length>0&&(t.tagName==="ION-MODAL"||t.tagName==="ION-POPOVER")&&!(p=t.children[0]).classList.contains("ion-delegate-host")){let x=(g=t.ownerDocument)===null||g===void 0?void 0:g.createElement("div");x.classList.add("ion-delegate-host"),a.forEach(O=>x.classList.add(O)),x.append(...t.children),t.appendChild(x),p=x}let v=document.querySelector("ion-app")||document.body;return e=document.createComment("ionic teleport"),t.parentNode.insertBefore(e,t),v.appendChild(t),p!=null?p:t}),removeViewFromDom:()=>(t&&e&&(e.parentNode.insertBefore(t,e),e.remove()),Promise.resolve())}};var Ti='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',BD=(t,e)=>{let r=t.querySelector(Ti);zD(r,e!=null?e:t)},UD=(t,e)=>{let r=Array.from(t.querySelectorAll(Ti)),n=r.length>0?r[r.length-1]:null;zD(n,e!=null?e:t)},zD=(t,e)=>{let r=t,n=t==null?void 0:t.shadowRoot;if(n&&(r=n.querySelector(Ti)||t),r){let o=r.closest("ion-radio-group");o?o.setFocus():Fc(r)}else e.focus()},Xf=0,gA=0,xc=new WeakMap,mA=t=>({create(r){return yA(t,r)},dismiss(r,n,o){return bA(document,r,n,t,o)},getTop(){return ve(this,null,function*(){return Si(document,t)})}});var vA=mA("ion-loading");var e$=t=>{typeof document<"u"&&CA(document);let e=Xf++;t.overlayIndex=e},t$=t=>(t.hasAttribute("id")||(t.id=`ion-overlay-${++gA}`),t.id),yA=(t,e)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(t).then(()=>{let r=document.createElement(t);return r.classList.add("overlay-hidden"),Object.assign(r,Object.assign(Object.assign({},e),{hasController:!0})),WD(document).appendChild(r),new Promise(n=>mn(r,n))}):Promise.resolve(),DA=t=>t.classList.contains("overlay-hidden"),HD=(t,e)=>{let r=t,n=t==null?void 0:t.shadowRoot;n&&(r=n.querySelector(Ti)||t),r?Fc(r):e.focus()},IA=(t,e)=>{let r=Si(e,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),n=t.target;if(!r||!n||r.classList.contains(xA))return;let o=()=>{if(r===n)r.lastFocus=void 0;else if(n.tagName==="ION-TOAST")HD(r.lastFocus,r);else{let s=ph(r);if(!s.contains(n))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(n)||n===s.querySelector("ion-backdrop"))r.lastFocus=n;else{let c=r.lastFocus;BD(a,r),c===e.activeElement&&UD(a,r),r.lastFocus=e.activeElement}}},i=()=>{if(r.contains(n))r.lastFocus=n;else if(n.tagName==="ION-TOAST")HD(r.lastFocus,r);else{let s=r.lastFocus;BD(r),s===e.activeElement&&UD(r),r.lastFocus=e.activeElement}};r.shadowRoot?i():o()},CA=t=>{Xf===0&&(Xf=1,t.addEventListener("focus",e=>{IA(e,t)},!0),t.addEventListener("ionBackButton",e=>{let r=Si(t);r!=null&&r.backdropDismiss&&e.detail.register(mh,()=>{r.dismiss(void 0,$D)})}),gh()||t.addEventListener("keydown",e=>{if(e.key==="Escape"){let r=Si(t);r!=null&&r.backdropDismiss&&r.dismiss(void 0,$D)}}))},bA=(t,e,r,n,o)=>{let i=Si(t,n,o);return i?i.dismiss(e,r):Promise.reject("overlay does not exist")},EA=(t,e)=>(e===void 0&&(e="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(t.querySelectorAll(e)).filter(r=>r.overlayIndex>0)),Ac=(t,e)=>EA(t,e).filter(r=>!DA(r)),Si=(t,e,r)=>{let n=Ac(t,e);return r===void 0?n[n.length-1]:n.find(o=>o.id===r)},GD=(t=!1)=>{let r=WD(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");r&&(t?r.setAttribute("aria-hidden","true"):r.removeAttribute("aria-hidden"))},n$=(t,e,r,n,o)=>ve(void 0,null,function*(){var i,s;if(t.presented)return;t.el.tagName!=="ION-TOAST"&&(GD(!0),document.body.classList.add(Hc)),TA(t.el),ZD(t.el),t.presented=!0,t.willPresent.emit(),(i=t.willPresentShorthand)===null||i===void 0||i.emit();let a=Zf(t),c=t.enterAnimation?t.enterAnimation:Kn.get(e,a==="ios"?r:n);(yield qD(t,c,t.el,o))&&(t.didPresent.emit(),(s=t.didPresentShorthand)===null||s===void 0||s.emit()),t.el.tagName!=="ION-TOAST"&&wA(t.el),t.keyboardClose&&(document.activeElement===null||!t.el.contains(document.activeElement))&&t.el.focus(),t.el.removeAttribute("aria-hidden")}),wA=t=>ve(void 0,null,function*(){let e=document.activeElement;if(!e)return;let r=e==null?void 0:e.shadowRoot;r&&(e=r.querySelector(Ti)||e),yield t.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&e.focus()}),r$=(t,e,r,n,o,i,s)=>ve(void 0,null,function*(){var a,c;if(!t.presented)return!1;let d=(qt!==void 0?Ac(qt):[]).filter(g=>g.tagName!=="ION-TOAST");d.length===1&&d[0].id===t.el.id&&(GD(!1),document.body.classList.remove(Hc)),t.presented=!1;try{ZD(t.el),t.el.style.setProperty("pointer-events","none"),t.willDismiss.emit({data:e,role:r}),(a=t.willDismissShorthand)===null||a===void 0||a.emit({data:e,role:r});let g=Zf(t),p=t.leaveAnimation?t.leaveAnimation:Kn.get(n,g==="ios"?o:i);r!==SA&&(yield qD(t,p,t.el,s)),t.didDismiss.emit({data:e,role:r}),(c=t.didDismissShorthand)===null||c===void 0||c.emit({data:e,role:r}),(xc.get(t)||[]).forEach(D=>D.destroy()),xc.delete(t),t.el.classList.add("overlay-hidden"),t.el.style.removeProperty("pointer-events"),t.el.lastFocus!==void 0&&(t.el.lastFocus=void 0)}catch(g){Ni(`[${t.el.tagName.toLowerCase()}] - `,g)}return t.el.remove(),_A(),!0}),WD=t=>t.querySelector("ion-app")||t.body,qD=(t,e,r,n)=>ve(void 0,null,function*(){r.classList.remove("overlay-hidden");let o=t.el,i=e(o,n);(!t.animated||!Kn.getBoolean("animated",!0))&&i.duration(0),t.keyboardClose&&i.beforeAddWrite(()=>{let a=r.ownerDocument.activeElement;a!=null&&a.matches("input,ion-input, ion-textarea")&&a.blur()});let s=xc.get(t)||[];return xc.set(t,[...s,i]),yield i.play(),!0}),o$=(t,e)=>{let r,n=new Promise(o=>r=o);return MA(t,e,o=>{r(o.detail)}),n},MA=(t,e,r)=>{let n=o=>{hh(t,e,n),r(o)};fh(t,e,n)};var $D="backdrop",SA="gesture";var i$=t=>{let e=!1,r,n=VD(),o=(a=!1)=>{if(r&&!a)return{delegate:r,inline:e};let{el:c,hasController:l,delegate:d}=t;return e=c.parentNode!==null&&!l,r=e?d||n:d,{inline:e,delegate:r}};return{attachViewToDom:a=>ve(void 0,null,function*(){let{delegate:c}=o(!0);if(c)return yield c.attachViewToDom(t.el,a);let{hasController:l}=t;if(l&&a!==void 0)throw new Error("framework delegate is missing");return null}),removeViewFromDom:()=>{let{delegate:a}=o();a&&t.el!==void 0&&a.removeViewFromDom(t.el.parentElement,t.el)}}},s$=()=>{let t,e=()=>{t&&(t(),t=void 0)};return{addClickListener:(n,o)=>{e();let i=o!==void 0?document.getElementById(o):null;if(!i){Pc(`[${n.tagName.toLowerCase()}] - A trigger element with the ID "${o}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,n);return}t=((a,c)=>{let l=()=>{c.present()};return a.addEventListener("click",l),()=>{a.removeEventListener("click",l)}})(i,n)},removeClickListener:e}},ZD=t=>{qt!==void 0&&Mi("android")&&t.setAttribute("aria-hidden","true")},TA=t=>{var e;if(qt===void 0)return;let r=Ac(qt);for(let n=r.length-1;n>=0;n--){let o=r[n],i=(e=r[n+1])!==null&&e!==void 0?e:t;(i.hasAttribute("aria-hidden")||i.tagName!=="ION-TOAST")&&o.setAttribute("aria-hidden","true")}},_A=()=>{if(qt===void 0)return;let t=Ac(qt);for(let e=t.length-1;e>=0;e--){let r=t[e];if(r.removeAttribute("aria-hidden"),r.tagName!=="ION-TOAST")break}},xA="ion-disable-focus-trap";var AA=["tabsInner"];var XD=(()=>{let e=class e{doc;_readyPromise;win;backButton=new ee;keyboardDidShow=new ee;keyboardDidHide=new ee;pause=new ee;resume=new ee;resize=new ee;constructor(n,o){this.doc=n,o.run(()=>{var s;this.win=n.defaultView,this.backButton.subscribeWithPriority=function(a,c){return this.subscribe(l=>l.register(a,d=>o.run(()=>c(d))))},oo(this.pause,n,"pause",o),oo(this.resume,n,"resume",o),oo(this.backButton,n,"ionBackButton",o),oo(this.resize,this.win,"resize",o),oo(this.keyboardDidShow,this.win,"ionKeyboardDidShow",o),oo(this.keyboardDidHide,this.win,"ionKeyboardDidHide",o);let i;this._readyPromise=new Promise(a=>{i=a}),(s=this.win)!=null&&s.cordova?n.addEventListener("deviceready",()=>{i("cordova")},{once:!0}):i("dom")})}is(n){return Mi(this.win,n)}platforms(){return Tc(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(n){return RA(this.win.location.href,n)}isLandscape(){return!this.isPortrait()}isPortrait(){var n,o;return(o=(n=this.win).matchMedia)==null?void 0:o.call(n,"(orientation: portrait)").matches}testUserAgent(n){let o=this.win.navigator;return!!(o!=null&&o.userAgent&&o.userAgent.indexOf(n)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce),k(m))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),RA=(t,e)=>{e=e.replace(/[[\]\\]/g,"\\$&");let n=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(t);return n?decodeURIComponent(n[1].replace(/\+/g," ")):null},oo=(t,e,r,n)=>{e&&e.addEventListener(r,o=>{n.run(()=>{let i=o!=null?o.detail:void 0;t.next(i)})})},pn=(()=>{let e=class e{location;serializer;router;topOutlet;direction=YD;animated=QD;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(n,o,i,s){this.location=o,this.serializer=i,this.router=s,s&&s.events.subscribe(a=>{if(a instanceof At){let c=a.restoredState?a.restoredState.navigationId:a.id;this.guessDirection=this.guessAnimation=c<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?a.id:c}}),n.backButton.subscribeWithPriority(0,a=>{this.pop(),a()})}navigateForward(n,o={}){return this.setDirection("forward",o.animated,o.animationDirection,o.animation),this.navigate(n,o)}navigateBack(n,o={}){return this.setDirection("back",o.animated,o.animationDirection,o.animation),this.navigate(n,o)}navigateRoot(n,o={}){return this.setDirection("root",o.animated,o.animationDirection,o.animation),this.navigate(n,o)}back(n={animated:!0,animationDirection:"back"}){return this.setDirection("back",n.animated,n.animationDirection,n.animation),this.location.back()}pop(){return ve(this,null,function*(){let n=this.topOutlet;for(;n;){if(yield n.pop())return!0;n=n.parentOutlet}return!1})}setDirection(n,o,i,s){this.direction=n,this.animated=NA(n,o,i),this.animationBuilder=s}setTopOutlet(n){this.topOutlet=n}consumeTransition(){let n="root",o,i=this.animationBuilder;return this.direction==="auto"?(n=this.guessDirection,o=this.guessAnimation):(o=this.animated,n=this.direction),this.direction=YD,this.animated=QD,this.animationBuilder=void 0,{direction:n,animation:o,animationBuilder:i}}navigate(n,o){if(Array.isArray(n))return this.router.navigate(n,o);{let i=this.serializer.parse(n.toString());return o.queryParams!==void 0&&(i.queryParams=b({},o.queryParams)),o.fragment!==void 0&&(i.fragment=o.fragment),this.router.navigateByUrl(i,o)}}};u(e,"\u0275fac",function(o){return new(o||e)(k(XD),k(We),k(Gt),k(Ce,8))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),NA=(t,e,r)=>{if(e!==!1){if(r!==void 0)return r;if(t==="forward"||t==="back")return t;if(t==="root"&&e===!0)return"forward"}},YD="auto",QD=void 0,xi=(()=>{let e=class e{get(n,o){let i=Jf();return i?i.get(n,o):null}getBoolean(n,o){let i=Jf();return i?i.getBoolean(n,o):!1}getNumber(n,o){let i=Jf();return i?i.getNumber(n,o):0}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),Rc=new N("USERCONFIG"),Jf=()=>{if(typeof window<"u"){let t=window.Ionic;if(t!=null&&t.config)return t.config}return null},_i=class{data;constructor(e={}){this.data=e,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(e){return this.data[e]}},gn=(()=>{let e=class e{zone=I(m);applicationRef=I(Et);config=I(Rc);create(n,o,i){var s;return new th(n,o,this.applicationRef,this.zone,i,(s=this.config.useSetInputAPI)!=null?s:!1)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),th=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(e,r,n,o,i,s){this.environmentInjector=e,this.injector=r,this.applicationRef=n,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(e,r,n,o){return this.zone.run(()=>new Promise(i=>{let s=b({},n);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=e);let a=OA(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,e,r,s,o,this.elementReferenceKey,this.enableSignalsSupport);i(a)}))}removeViewFromDom(e,r){return this.zone.run(()=>new Promise(n=>{let o=this.elRefMap.get(r);if(o){o.destroy(),this.elRefMap.delete(r);let i=this.elEventsMap.get(r);i&&(i(),this.elEventsMap.delete(r))}n()}))}},OA=(t,e,r,n,o,i,s,a,c,l,d,h)=>{let g=te.create({providers:PA(c),parent:r}),p=Rv(a,{environmentInjector:e,elementInjector:g}),v=p.instance,D=p.location.nativeElement;if(c)if(d&&v[d]!==void 0&&console.error(`[Ionic Error]: ${d} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${d}" property from ${a.name}.`),h===!0&&p.setInput!==void 0){let O=c,{modal:X,popover:Ue}=O,he=kc(O,["modal","popover"]);for(let Ot in he)p.setInput(Ot,he[Ot]);X!==void 0&&Object.assign(v,{modal:X}),Ue!==void 0&&Object.assign(v,{popover:Ue})}else Object.assign(v,c);if(l)for(let X of l)D.classList.add(X);let x=JD(t,v,D);return s.appendChild(D),n.attachView(p.hostView),o.set(D,p),i.set(D,x),D},kA=[jc,Lc,Vc,Bc,Uc],JD=(t,e,r)=>t.run(()=>{let n=kA.filter(o=>typeof e[o]=="function").map(o=>{let i=s=>e[o](s.detail);return r.addEventListener(o,i),()=>r.removeEventListener(o,i)});return()=>n.forEach(o=>o())}),KD=new N("NavParamsToken"),PA=t=>[{provide:KD,useValue:t},{provide:_i,useFactory:FA,deps:[KD]}],FA=t=>new _i(t),jA=(t,e)=>{let r=t.prototype;e.forEach(n=>{Object.defineProperty(r,n,{get(){return this.el[n]},set(o){this.z.runOutsideAngular(()=>this.el[n]=o)}})})},LA=(t,e)=>{let r=t.prototype;e.forEach(n=>{r[n]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[n].apply(this.el,o))}})},sh=(t,e,r)=>{r.forEach(n=>t[n]=bn(e,n))};function Nc(t){return function(r){let{defineCustomElementFn:n,inputs:o,methods:i}=t;return n!==void 0&&n(),o&&jA(r,o),i&&LA(r,i),r}}var VA=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],BA=["present","dismiss","onDidDismiss","onWillDismiss"],eI=(()=>{var e;let t=(e=class{z;template;isCmpOpen=!1;el;constructor(n,o,i){this.z=i,this.el=o.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,n.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,n.detectChanges()}),sh(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275dir",B({type:e,selectors:[["ion-popover"]],contentQueries:function(o,i,s){if(o&1&&sn(s,bt,5),o&2){let a;lt(a=ut())&&(i.template=a.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"},standalone:!1})),e);return t=M([Nc({inputs:VA,methods:BA})],t),t})(),UA=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],HA=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],tI=(()=>{var e;let t=(e=class{z;template;isCmpOpen=!1;el;constructor(n,o,i){this.z=i,this.el=o.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,n.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,n.detectChanges()}),sh(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275dir",B({type:e,selectors:[["ion-modal"]],contentQueries:function(o,i,s){if(o&1&&sn(s,bt,5),o&2){let a;lt(a=ut())&&(i.template=a.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"},standalone:!1})),e);return t=M([Nc({inputs:UA,methods:HA})],t),t})(),$A=(t,e,r)=>r==="root"?nI(t,e):r==="forward"?zA(t,e):GA(t,e),nI=(t,e)=>(t=t.filter(r=>r.stackId!==e.stackId),t.push(e),t),zA=(t,e)=>(t.indexOf(e)>=0?t=t.filter(n=>n.stackId!==e.stackId||n.id<=e.id):t.push(e),t),GA=(t,e)=>t.indexOf(e)>=0?t.filter(n=>n.stackId!==e.stackId||n.id<=e.id):nI(t,e),nh=(t,e)=>{let r=t.createUrlTree(["."],{relativeTo:e});return t.serializeUrl(r)},rI=(t,e)=>e?t.stackId!==e.stackId:!0,WA=(t,e)=>{if(!t)return;let r=oI(e);for(let n=0;n<r.length;n++){if(n>=t.length)return r[n];if(r[n]!==t[n])return}},oI=t=>t.split("/").map(e=>e.trim()).filter(e=>e!==""),iI=t=>{t&&(t.ref.destroy(),t.unlistenEvents())},rh=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(e,r,n,o,i,s){this.containerEl=r,this.router=n,this.navCtrl=o,this.zone=i,this.location=s,this.tabsPrefix=e!==void 0?oI(e):void 0}createView(e,r){var s;let n=nh(this.router,r),o=(s=e==null?void 0:e.location)==null?void 0:s.nativeElement,i=JD(this.zone,e.instance,o);return{id:this.nextId++,stackId:WA(this.tabsPrefix,n),unlistenEvents:i,element:o,ref:e,url:n}}getExistingView(e){let r=nh(this.router,e),n=this.views.find(o=>o.url===r);return n&&n.ref.changeDetectorRef.reattach(),n}setActive(e){var v,D;let r=this.navCtrl.consumeTransition(),{direction:n,animation:o,animationBuilder:i}=r,s=this.activeView,a=rI(e,s);a&&(n="back",o=void 0);let c=this.views.slice(),l,d=this.router;d.getCurrentNavigation?l=d.getCurrentNavigation():(v=d.navigations)!=null&&v.value&&(l=d.navigations.value),(D=l==null?void 0:l.extras)!=null&&D.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let h=this.views.includes(e),g=this.insertView(e,n);h||e.ref.changeDetectorRef.detectChanges();let p=e.animationBuilder;return i===void 0&&n==="back"&&!a&&p!==void 0&&(i=p),s&&(s.animationBuilder=i),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),e.ref.changeDetectorRef.reattach(),this.transition(e,s,o,this.canGoBack(1),!1,i).then(()=>qA(e,g,c,this.location,this.zone)).then(()=>({enteringView:e,direction:n,animation:o,tabSwitch:a})))))}canGoBack(e,r=this.getActiveStackId()){return this.getStack(r).length>e}pop(e,r=this.getActiveStackId()){return this.zone.run(()=>{var c,l;let n=this.getStack(r);if(n.length<=e)return Promise.resolve(!1);let o=n[n.length-e-1],i=o.url,s=o.savedData;if(s){let d=s.get("primary");(l=(c=d==null?void 0:d.route)==null?void 0:c._routerState)!=null&&l.snapshot.url&&(i=d.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(i,L(b({},o.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let e=this.activeView;if(e){let r=this.getStack(e.stackId),n=r[r.length-2],o=n.animationBuilder;return this.wait(()=>this.transition(n,e,"back",this.canGoBack(2),!0,o))}return Promise.resolve()}endBackTransition(e){e?(this.skipTransition=!0,this.pop(1)):this.activeView&&sI(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(e){let r=this.getStack(e);return r.length>0?r[r.length-1]:void 0}getRootUrl(e){let r=this.getStack(e);return r.length>0?r[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(iI),this.activeView=void 0,this.views=[]}getStack(e){return this.views.filter(r=>r.stackId===e)}insertView(e,r){return this.activeView=e,this.views=$A(this.views,e,r),this.views.slice()}transition(e,r,n,o,i,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(r===e)return Promise.resolve(!1);let a=e?e.element:void 0,c=r?r.element:void 0,l=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),l.commit)?l.commit(a,c,{duration:n===void 0?0:void 0,direction:n,showGoBack:o,progressAnimation:i,animationBuilder:s}):Promise.resolve(!1)}wait(e){return ve(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let r=this.runningTask=e();return r.finally(()=>this.runningTask=void 0),r})}},qA=(t,e,r,n,o)=>typeof requestAnimationFrame=="function"?new Promise(i=>{requestAnimationFrame(()=>{sI(t,e,r,n,o),i()})}):Promise.resolve(),sI=(t,e,r,n,o)=>{o.run(()=>r.filter(i=>!e.includes(i)).forEach(iI)),e.forEach(i=>{let a=n.path().split("?")[0].split("#")[0];if(i!==t&&i.url!==a){let c=i.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),i.ref.changeDetectorRef.detach()}})},ah=(()=>{let e=class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new pe(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=j;stackWillChange=new ie;stackDidChange=new ie;activateEvents=new ie;deactivateEvents=new ie;parentContexts=I(Nt);location=I(ze);environmentInjector=I(se);inputBinder=I(aI,{optional:!0});supportsBindingToComponentInputs=!0;config=I(xi);navCtrl=I(pn);set animation(n){this.nativeEl.animation=n}set animated(n){this.nativeEl.animated=n}set swipeGesture(n){this._swipeGesture=n,this.nativeEl.swipeHandler=n?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:o=>this.stackCtrl.endBackTransition(o)}:void 0}constructor(n,o,i,s,a,c,l,d){this.parentOutlet=d,this.nativeEl=s.nativeElement,this.name=n||j,this.tabsPrefix=o==="true"?nh(a,l):void 0,this.stackCtrl=new rh(this.tabsPrefix,this.nativeEl,a,this.navCtrl,c,i),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){var n;this.stackCtrl.destroy(),(n=this.inputBinder)==null||n.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let n=this.getContext();n!=null&&n.route&&this.activateWith(n.route,n.injector)}new Promise(n=>mn(this.nativeEl,n)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(n,o){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let o=this.getContext();this.activatedView.savedData=new Map(o.children.contexts);let i=this.activatedView.savedData.get("primary");if(i&&o.route&&(i.route=b({},o.route)),this.activatedView.savedExtras={},o.route){let s=o.route.snapshot;this.activatedView.savedExtras.queryParams=s.queryParams,this.activatedView.savedExtras.fragment=s.fragment}}let n=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,o){var c,l;if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=n;let i,s=this.stackCtrl.getExistingView(n);if(s){i=this.activated=s.ref;let d=s.savedData;if(d){let h=this.getContext();h.children.contexts=d}this.updateActivatedRouteProxy(i.instance,n)}else{let d=n._futureSnapshot,h=this.parentContexts.getOrCreateContext(this.name).children,g=new pe(null),p=this.createActivatedRouteProxy(g,n),v=new oh(p,h,this.location.injector),D=(c=d.routeConfig.component)!=null?c:d.component;i=this.activated=this.outletContent.createComponent(D,{index:this.outletContent.length,injector:v,environmentInjector:o!=null?o:this.environmentInjector}),g.next(i.instance),s=this.stackCtrl.createView(this.activated,n),this.proxyMap.set(i.instance,p),this.currentActivatedRoute$.next({component:i.instance,activatedRoute:n})}(l=this.inputBinder)==null||l.bindActivatedRouteToOutletComponent(this),this.activatedView=s,this.navCtrl.setTopOutlet(this);let a=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:s,tabSwitch:rI(s,a)}),this.stackCtrl.setActive(s).then(d=>{this.activateEvents.emit(i.instance),this.stackDidChange.emit(d)})}canGoBack(n=1,o){return this.stackCtrl.canGoBack(n,o)}pop(n=1,o){return this.stackCtrl.pop(n,o)}getLastUrl(n){let o=this.stackCtrl.getLastUrl(n);return o?o.url:void 0}getLastRouteView(n){return this.stackCtrl.getLastUrl(n)}getRootView(n){return this.stackCtrl.getRootUrl(n)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(n,o){let i=new xe;return i._futureSnapshot=o._futureSnapshot,i._routerState=o._routerState,i.snapshot=o.snapshot,i.outlet=o.outlet,i.component=o.component,i._paramMap=this.proxyObservable(n,"paramMap"),i._queryParamMap=this.proxyObservable(n,"queryParamMap"),i.url=this.proxyObservable(n,"url"),i.params=this.proxyObservable(n,"params"),i.queryParams=this.proxyObservable(n,"queryParams"),i.fragment=this.proxyObservable(n,"fragment"),i.data=this.proxyObservable(n,"data"),i}proxyObservable(n,o){return n.pipe(ye(i=>!!i),De(i=>this.currentActivatedRoute$.pipe(ye(s=>s!==null&&s.component===i),De(s=>s&&s.activatedRoute[o]),ul())))}updateActivatedRouteProxy(n,o){let i=this.proxyMap.get(n);if(!i)throw new Error("Could not find activated route proxy for view");i._futureSnapshot=o._futureSnapshot,i._routerState=o._routerState,i.snapshot=o.snapshot,i.outlet=o.outlet,i.component=o.component,this.currentActivatedRoute$.next({component:n,activatedRoute:o})}};u(e,"\u0275fac",function(o){return new(o||e)(Mt("name"),Mt("tabs"),f(We),f(y),f(Ce),f(m),f(xe),f(e,12))}),u(e,"\u0275dir",B({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1}));let t=e;return t})(),oh=class{route;childContexts;parent;constructor(e,r,n){this.route=e,this.childContexts=r,this.parent=n}get(e,r){return e===xe?this.route:e===Nt?this.childContexts:this.parent.get(e,r)}},aI=new N(""),ZA=(()=>{let e=class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){var o;(o=this.outletDataSubscriptions.get(n))==null||o.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:o}=n,i=Cn([o.queryParams,o.params,o.data]).pipe(De(([s,a,c],l)=>(c=b(b(b({},s),a),c),l===0?P(c):Promise.resolve(c)))).subscribe(s=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==o||o.component===null){this.unsubscribeFromRouteData(n);return}let a=wa(o.component);if(!a){this.unsubscribeFromRouteData(n);return}for(let{templateName:c}of a.inputs)n.activatedComponentRef.setInput(c,s[c])});this.outletDataSubscriptions.set(n,i)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),cI=()=>({provide:aI,useFactory:YA,deps:[Ce]});function YA(t){return t!=null&&t.componentInputBindingEnabled?new ZA:null}var QA=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],lI=(()=>{var e;let t=(e=class{routerOutlet;navCtrl;config;r;z;el;constructor(n,o,i,s,a,c){this.routerOutlet=n,this.navCtrl=o,this.config=i,this.r=s,this.z=a,c.detach(),this.el=this.r.nativeElement}onClick(n){var i;let o=this.defaultHref||this.config.get("backButtonDefaultHref");(i=this.routerOutlet)!=null&&i.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),n.preventDefault()):o!=null&&(this.navCtrl.navigateBack(o,{animation:this.routerAnimation}),n.preventDefault())}},u(e,"\u0275fac",function(o){return new(o||e)(f(ah,8),f(pn),f(xi),f(y),f(m),f(C))}),u(e,"\u0275dir",B({type:e,hostBindings:function(o,i){o&1&&Ie("click",function(a){return i.onClick(a)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"},standalone:!1})),e);return t=M([Nc({inputs:QA})],t),t})(),uI=(()=>{let e=class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(n,o,i,s,a){this.locationStrategy=n,this.navCtrl=o,this.elementRef=i,this.router=s,this.routerLink=a}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let n=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],o=this.elementRef.nativeElement;n.includes(o.tagName)&&o.getAttribute("tabindex")==="0"&&o.removeAttribute("tabindex")}updateTargetUrlAndHref(){var n;if((n=this.routerLink)!=null&&n.urlTree){let o=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=o}}onClick(n){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),n.preventDefault()}};u(e,"\u0275fac",function(o){return new(o||e)(f(Ve),f(pn),f(y),f(Ce),f(yi,8))}),u(e,"\u0275dir",B({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(o,i){o&1&&Ie("click",function(a){return i.onClick(a)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[tt]}));let t=e;return t})(),dI=(()=>{let e=class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(n,o,i,s,a){this.locationStrategy=n,this.navCtrl=o,this.elementRef=i,this.router=s,this.routerLink=a}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){var n;if((n=this.routerLink)!=null&&n.urlTree){let o=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=o}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}};u(e,"\u0275fac",function(o){return new(o||e)(f(Ve),f(pn),f(y),f(Ce),f(yi,8))}),u(e,"\u0275dir",B({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(o,i){o&1&&Ie("click",function(){return i.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[tt]}));let t=e;return t})(),KA=["animated","animation","root","rootParams","swipeGesture"],XA=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],fI=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i,s,a,c){this.z=a,c.detach(),this.el=n.nativeElement,n.nativeElement.delegate=s.create(o,i),sh(this,this.el,["ionNavDidChange","ionNavWillChange"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(y),f(se),f(te),f(gn),f(m),f(C))}),u(e,"\u0275dir",B({type:e,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"},standalone:!1})),e);return t=M([Nc({inputs:KA,methods:XA})],t),t})(),hI=(()=>{let e=class e{navCtrl;tabsInner;ionTabsWillChange=new ie;ionTabsDidChange=new ie;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(n){this.navCtrl=n}ngAfterViewInit(){let n=this.tabs.length>0?this.tabs.first:void 0;n&&(this.hasTab=!0,this.setActiveTab(n.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:n,tabSwitch:o}){let i=n.stackId;o&&i!==void 0&&this.ionTabsWillChange.emit({tab:i})}onStackDidChange({enteringView:n,tabSwitch:o}){let i=n.stackId;o&&i!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=i),this.ionTabsDidChange.emit({tab:i}))}select(n){let o=typeof n=="string",i=o?n:n.detail.tab;if(this.hasTab){this.setActiveTab(i),this.tabSwitch();return}let s=this.outlet.getActiveStackId()===i,a=`${this.outlet.tabsPrefix}/${i}`;if(o||n.stopPropagation(),s){let c=this.outlet.getActiveStackId(),l=this.outlet.getLastRouteView(c);if((l==null?void 0:l.url)===a)return;let d=this.outlet.getRootView(i),h=d&&a===d.url&&d.savedExtras;return this.navCtrl.navigateRoot(a,L(b({},h),{animated:!0,animationDirection:"back"}))}else{let c=this.outlet.getLastRouteView(i),l=(c==null?void 0:c.url)||a,d=c==null?void 0:c.savedExtras;return this.navCtrl.navigateRoot(l,L(b({},d),{animated:!0,animationDirection:"back"}))}}setActiveTab(n){let i=this.tabs.find(s=>s.tab===n);if(!i){console.error(`[Ionic Error]: Tab with id: "${n}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=i,this.ionTabsWillChange.emit({tab:n}),i.el.active=!0}tabSwitch(){let{selectedTab:n,leavingTab:o}=this;this.tabBar&&n&&(this.tabBar.selectedTab=n.tab),(o==null?void 0:o.tab)!==(n==null?void 0:n.tab)&&o!=null&&o.el&&(o.el.active=!1),n&&this.ionTabsDidChange.emit({tab:n.tab})}getSelected(){var n;return this.hasTab?(n=this.selectedTab)==null?void 0:n.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(n=>{let o=n.el.getAttribute("slot");o!==this.tabBarSlot&&(this.tabBarSlot=o,this.relocateTabBar())})}relocateTabBar(){let n=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(n):this.tabsInner.nativeElement.after(n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(pn))}),u(e,"\u0275dir",B({type:e,selectors:[["ion-tabs"]],viewQuery:function(o,i){if(o&1&&Bo(AA,7,y),o&2){let s;lt(s=ut())&&(i.tabsInner=s.first)}},hostBindings:function(o,i){o&1&&Ie("ionTabButtonClick",function(a){return i.select(a)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"},standalone:!1}));let t=e;return t})(),ch=t=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(t):typeof requestAnimationFrame=="function"?requestAnimationFrame(t):setTimeout(t),Ai=(()=>{let e=class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(n,o){this.injector=n,this.elementRef=o}writeValue(n){this.elementRef.nativeElement.value=this.lastValue=n,Yn(this.elementRef)}handleValueChange(n,o){n===this.elementRef.nativeElement&&(o!==this.lastValue&&(this.lastValue=o,this.onChange(o)),Yn(this.elementRef))}_handleBlurEvent(n){n===this.elementRef.nativeElement?(this.onTouched(),Yn(this.elementRef)):n.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(n){this.onChange=n}registerOnTouched(n){this.onTouched=n}setDisabledState(n){this.elementRef.nativeElement.disabled=n}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let n;try{n=this.injector.get(Wn)}catch{}if(!n)return;n.statusChanges&&(this.statusChanges=n.statusChanges.subscribe(()=>Yn(this.elementRef)));let o=n.control;o&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(s=>{if(typeof o[s]<"u"){let a=o[s].bind(o);o[s]=(...c)=>{a(...c),Yn(this.elementRef)}}})}};u(e,"\u0275fac",function(o){return new(o||e)(f(te),f(y))}),u(e,"\u0275dir",B({type:e,hostBindings:function(o,i){o&1&&Ie("ionBlur",function(a){return i._handleBlurEvent(a.target)})},standalone:!1}));let t=e;return t})(),Yn=t=>{ch(()=>{let e=t.nativeElement,r=e.value!=null&&e.value.toString().length>0,n=JA(e);eh(e,n);let o=e.closest("ion-item");o&&(r?eh(o,[...n,"item-has-value"]):eh(o,n))})},JA=t=>{let e=t.classList,r=[];for(let n=0;n<e.length;n++){let o=e.item(n);o!==null&&eR(o,"ng-")&&r.push(`ion-${o.substring(3)}`)}return r},eh=(t,e)=>{let r=t.classList;r.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),r.add(...e)},eR=(t,e)=>t.substring(0,e.length)===e,ih=class{shouldDetach(e){return!1}shouldAttach(e){return!1}store(e,r){}retrieve(e){return null}shouldReuseRoute(e,r){if(e.routeConfig!==r.routeConfig)return!1;let n=e.params,o=r.params,i=Object.keys(n),s=Object.keys(o);if(i.length!==s.length)return!1;for(let a of i)if(o[a]!==n[a])return!1;return!0}},hn=class{ctrl;constructor(e){this.ctrl=e}create(e){return this.ctrl.create(e||{})}dismiss(e,r,n){return this.ctrl.dismiss(e,r,n)}getTop(){return this.ctrl.getTop()}};function vI(){var t=[];if(typeof window<"u"){var e=window;(!e.customElements||e.Element&&(!e.Element.prototype.closest||!e.Element.prototype.matches||!e.Element.prototype.remove||!e.Element.prototype.getRootNode))&&t.push(import("./chunk-5X4HMWFG.js"));var r=function(){try{var n=new URL("b","http://a");return n.pathname="c%20d",n.href==="http://a/c%20d"&&n.searchParams}catch{return!1}};(typeof Object.assign!="function"||!Object.entries||!Array.prototype.find||!Array.prototype.includes||!String.prototype.startsWith||!String.prototype.endsWith||e.NodeList&&!e.NodeList.prototype.forEach||!e.fetch||!r()||typeof WeakMap>"u")&&t.push(import("./chunk-2WNWRKHP.js"))}return Promise.all(t)}var yI=$c;var DI=(t,e)=>ve(void 0,null,function*(){if(!(typeof window>"u"))return yield yI(),vh(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16],"beforeLeave":[16],"beforeEnter":[16]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16],"readonly":[4],"isDateEnabled":[16],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16],"multiple":[4],"highlightedDates":[16],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16],"leaveAnimation":[16],"component":[1],"componentProps":[16],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),e)});var T=["*"],fR=["outletContent"],hR=["outlet"],pR=[[["","slot","top"]],"*",[["ion-tab"]]],gR=["[slot=top]","*","ion-tab"];function mR(t,e){if(t&1){let r=Dv();jr(0,"ion-router-outlet",5,1),Ie("stackWillChange",function(o){td(r);let i=Vo();return nd(i.onStackWillChange(o))})("stackDidChange",function(o){td(r);let i=Vo();return nd(i.onStackDidChange(o))}),Lr()}}function vR(t,e){t&1&&w(0,2,["*ngIf","tabs.length > 0"])}function yR(t,e){if(t&1&&(jr(0,"div",1),ba(1,2),Lr()),t&2){let r=Vo();ua(),on("ngTemplateOutlet",r.template)}}function DR(t,e){if(t&1&&ba(0,1),t&2){let r=Vo();on("ngTemplateOutlet",r.template)}}var IR=(()=>{let e=class e extends Ai{constructor(n,o){super(n,o)}writeValue(n){this.elementRef.nativeElement.checked=this.lastValue=n,Yn(this.elementRef)}_handleIonChange(n){this.handleValueChange(n,n.checked)}};u(e,"\u0275fac",function(o){return new(o||e)(f(te),f(y))}),u(e,"\u0275dir",B({type:e,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(o,i){o&1&&Ie("ionChange",function(a){return i._handleIonChange(a.target)})},standalone:!1,features:[Re([{provide:qn,useExisting:e,multi:!0}]),ne]}));let t=e;return t})(),CR=(()=>{let e=class e extends Ai{el;constructor(n,o){super(n,o),this.el=o}handleInputEvent(n){this.handleValueChange(n,n.value)}registerOnChange(n){this.el.nativeElement.tagName==="ION-INPUT"?super.registerOnChange(o=>{n(o===""?null:parseFloat(o))}):super.registerOnChange(n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(te),f(y))}),u(e,"\u0275dir",B({type:e,selectors:[["ion-input","type","number"],["ion-range"]],hostBindings:function(o,i){o&1&&Ie("ionInput",function(a){return i.handleInputEvent(a.target)})},standalone:!1,features:[Re([{provide:qn,useExisting:e,multi:!0}]),ne]}));let t=e;return t})(),bR=(()=>{let e=class e extends Ai{constructor(n,o){super(n,o)}_handleChangeEvent(n){this.handleValueChange(n,n.value)}};u(e,"\u0275fac",function(o){return new(o||e)(f(te),f(y))}),u(e,"\u0275dir",B({type:e,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(o,i){o&1&&Ie("ionChange",function(a){return i._handleChangeEvent(a.target)})},standalone:!1,features:[Re([{provide:qn,useExisting:e,multi:!0}]),ne]}));let t=e;return t})(),ER=(()=>{let e=class e extends Ai{constructor(n,o){super(n,o)}_handleInputEvent(n){this.handleValueChange(n,n.value)}};u(e,"\u0275fac",function(o){return new(o||e)(f(te),f(y))}),u(e,"\u0275dir",B({type:e,selectors:[["ion-input",3,"type","number"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(o,i){o&1&&Ie("ionInput",function(a){return i._handleInputEvent(a.target)})},standalone:!1,features:[Re([{provide:qn,useExisting:e,multi:!0}]),ne]}));let t=e;return t})(),wR=(t,e)=>{let r=t.prototype;e.forEach(n=>{Object.defineProperty(r,n,{get(){return this.el[n]},set(o){this.z.runOutsideAngular(()=>this.el[n]=o)},configurable:!0})})},MR=(t,e)=>{let r=t.prototype;e.forEach(n=>{r[n]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[n].apply(this.el,o))}})},Q=(t,e,r)=>{r.forEach(n=>t[n]=bn(e,n))};function _(t){return function(r){let{defineCustomElementFn:n,inputs:o,methods:i}=t;return n!==void 0&&n(),o&&wR(r,o),i&&MR(r,i),r}}var SR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],t),t})(),TR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],t),t})(),_R=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),xR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),AR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-app"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({methods:["setFocus"]})],t),t})(),RR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-avatar"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),NR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionBackdropTap"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["stopPropagation","tappable","visible"]})],t),t})(),OR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),kR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],t),t})(),PR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionCollapsedClick"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],t),t})(),FR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],t),t})(),jR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["collapse"]})],t),t})(),LR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],t),t})(),VR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-card-content"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["mode"]})],t),t})(),BR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode","translucent"]})],t),t})(),UR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),HR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),$R=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange","ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],t),t})(),zR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","disabled","mode","outline"]})],t),t})(),GR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],t),t})(),WR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],t),t})(),qR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],t),t})(),ZR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","datetime","disabled","mode"]})],t),t})(),YR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],t),t})(),QR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],t),t})(),KR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["activated","side"]})],t),t})(),XR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["collapse","mode","translucent"]})],t),t})(),JR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["fixed"]})],t),t})(),eN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["collapse","mode","translucent"]})],t),t})(),tN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],t),t})(),nN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["alt","src"]})],t),t})(),rN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionInfinite"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled","position","threshold"],methods:["complete"]})],t),t})(),oN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["loadingSpinner","loadingText"]})],t),t})(),iN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],t),t})(),sN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","hideIcon","mode","showIcon"]})],t),t})(),aN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],t),t})(),cN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode","sticky"]})],t),t})(),lN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item-group"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),uN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],t),t})(),dN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionSwipe"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item-options"]],inputs:{side:"side"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["side"]})],t),t})(),fN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionDrag"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],t),t})(),hN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode","position"]})],t),t})(),pN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],t),t})(),gN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","lines","mode"]})],t),t})(),mN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),vN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],t),t})(),yN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["autoHide","color","disabled","menu","mode","type"]})],t),t})(),DN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["autoHide","menu"]})],t),t})(),IN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["component","componentProps","routerAnimation","routerDirection"]})],t),t})(),CN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),bN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-picker"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["mode"]})],t),t})(),EN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],t),t})(),wN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","disabled","value"]})],t),t})(),MN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],t),t})(),SN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["buffer","color","mode","reversed","type","value"]})],t),t})(),TN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],t),t})(),_N=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],t),t})(),xN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],t),t})(),AN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionRefresh","ionPull","ionStart"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],t),t})(),RN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],t),t})(),NN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-reorder"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),ON=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionItemReorder"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled"],methods:["complete"]})],t),t})(),kN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["type"],methods:["addRipple"]})],t),t})(),PN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-row"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),FN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],t),t})(),jN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],t),t})(),LN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["contentId","disabled","layout","mode","type","value"]})],t),t})(),VN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-segment-content"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),BN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionSegmentViewScroll"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled"]})],t),t})(),UN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],t),t})(),HN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["header","multiple","options"]})],t),t})(),$N=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled","value"]})],t),t})(),zN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated"]})],t),t})(),GN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","duration","name","paused"]})],t),t})(),WN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionSplitPaneVisible"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["contentId","disabled","when"]})],t),t})(),II=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["component","tab"],methods:["setActive"]})],t),t})(),lh=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode","selectedTab","translucent"]})],t),t})(),qN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],t),t})(),ZN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),YN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],t),t})(),QN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-thumbnail"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),KN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","size"]})],t),t})(),XN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),JN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange","ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],t),t})(),eO=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(y),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),Oc=(()=>{let e=class e extends ah{parentOutlet;outletContent;constructor(n,o,i,s,a,c,l,d){super(n,o,i,s,a,c,l,d),this.parentOutlet=d}};u(e,"\u0275fac",function(o){return new(o||e)(Mt("name"),Mt("tabs"),f(We),f(y),f(Ce),f(m),f(xe),f(e,12))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-router-outlet"]],viewQuery:function(o,i){if(o&1&&Bo(fR,7,ze),o&2){let s;lt(s=ut())&&(i.outletContent=s.first)}},standalone:!1,features:[ne],ngContentSelectors:T,decls:3,vars:0,consts:[["outletContent",""]],template:function(o,i){o&1&&(S(),Ia(0,null,0),w(2),Ca())},encapsulation:2}));let t=e;return t})(),tO=(()=>{let e=class e extends hI{outlet;tabBar;tabBars;tabs};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275cmp",E({type:e,selectors:[["ion-tabs"]],contentQueries:function(o,i,s){if(o&1&&(sn(s,lh,5),sn(s,lh,4),sn(s,II,4)),o&2){let a;lt(a=ut())&&(i.tabBar=a.first),lt(a=ut())&&(i.tabBars=a),lt(a=ut())&&(i.tabs=a)}},viewQuery:function(o,i){if(o&1&&Bo(hR,5,Oc),o&2){let s;lt(s=ut())&&(i.outlet=s.first)}},standalone:!1,features:[ne],ngContentSelectors:gR,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(o,i){o&1&&(S(pR),w(0),jr(1,"div",2,0),jo(3,mR,2,0,"ion-router-outlet",3)(4,vR,1,0,"ng-content",4),Lr(),w(5,1)),o&2&&(ua(3),on("ngIf",i.tabs.length===0),ua(),on("ngIf",i.tabs.length>0))},dependencies:[zo,Oc],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]}));let t=e;return t})(),nO=(()=>{let e=class e extends lI{constructor(n,o,i,s,a,c){super(n,o,i,s,a,c)}};u(e,"\u0275fac",function(o){return new(o||e)(f(Oc,8),f(pn),f(xi),f(y),f(m),f(C))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-back-button"]],standalone:!1,features:[ne],ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0}));let t=e;return t})(),rO=(()=>{let e=class e extends fI{constructor(n,o,i,s,a,c){super(n,o,i,s,a,c)}};u(e,"\u0275fac",function(o){return new(o||e)(f(y),f(se),f(te),f(gn),f(m),f(C))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-nav"]],standalone:!1,features:[ne],ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0}));let t=e;return t})(),oO=(()=>{let e=class e extends uI{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",B({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],standalone:!1,features:[ne]}));let t=e;return t})(),iO=(()=>{let e=class e extends dI{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",B({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],standalone:!1,features:[ne]}));let t=e;return t})(),sO=(()=>{let e=class e extends tI{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275cmp",E({type:e,selectors:[["ion-modal"]],standalone:!1,features:[ne],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(o,i){o&1&&jo(0,yR,2,1,"div",0),o&2&&on("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[zo,Ra],encapsulation:2,changeDetection:0}));let t=e;return t})(),aO=(()=>{let e=class e extends eI{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275cmp",E({type:e,selectors:[["ion-popover"]],standalone:!1,features:[ne],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(o,i){o&1&&jo(0,DR,1,1,"ng-container",0),o&2&&on("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[zo,Ra],encapsulation:2,changeDetection:0}));let t=e;return t})(),cO={provide:Wt,useExisting:$e(()=>CI),multi:!0},CI=(()=>{let e=class e extends Gf{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",B({type:e,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(o,i){o&2&&ct("max",i._enabled?i.max:null)},standalone:!1,features:[Re([cO]),ne]}));let t=e;return t})(),lO={provide:Wt,useExisting:$e(()=>bI),multi:!0},bI=(()=>{let e=class e extends Wf{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",B({type:e,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(o,i){o&2&&ct("min",i._enabled?i.min:null)},standalone:!1,features:[Re([lO]),ne]}));let t=e;return t})(),Xz=(()=>{let e=class e extends hn{constructor(){super(zc)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();var Jz=(()=>{let e=class e extends hn{constructor(){super(Gc)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();var uO=(()=>{let e=class e extends hn{angularDelegate=I(gn);injector=I(te);environmentInjector=I(se);constructor(){super(Wc)}create(n){return super.create(L(b({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();var uh=class extends hn{angularDelegate=I(gn);injector=I(te);environmentInjector=I(se);constructor(){super(qc)}create(e){return super.create(L(b({},e),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},e3=(()=>{let e=class e extends hn{constructor(){super(Zc)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),dO=(t,e,r)=>()=>{let n=e.defaultView;if(n&&typeof window<"u"){Yc(L(b({},t),{_zoneGate:i=>r.run(i)}));let o="__zone_symbol__addEventListener"in e.body?"__zone_symbol__addEventListener":"addEventListener";return vI().then(()=>DI(n,{exclude:["ion-tabs"],syncQueue:!0,raf:ch,jmp:i=>r.runOutsideAngular(i),ael(i,s,a,c){i[o](s,a,c)},rel(i,s,a,c){i.removeEventListener(s,a,c)}}))}},fO=[SR,TR,_R,xR,AR,RR,NR,OR,kR,PR,FR,jR,LR,VR,BR,UR,HR,$R,zR,GR,WR,qR,ZR,YR,QR,KR,XR,JR,eN,tN,nN,rN,oN,iN,sN,aN,cN,lN,uN,dN,fN,hN,pN,gN,mN,vN,yN,DN,IN,CN,bN,EN,wN,MN,SN,TN,_N,xN,AN,RN,NN,ON,kN,PN,FN,jN,LN,VN,BN,UN,HN,$N,zN,GN,WN,II,lh,qN,ZN,YN,QN,KN,XN,JN,eO],t3=[...fO,sO,aO,IR,CR,bR,ER,tO,Oc,nO,rO,oO,iO,bI,CI],n3=(()=>{let e=class e{static forRoot(n={}){return{ngModule:e,providers:[{provide:Rc,useValue:n},{provide:ya,useFactory:dO,multi:!0,deps:[Rc,ce,m]},gn,cI()]}}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Ae({type:e})),u(e,"\u0275inj",Te({providers:[uO,uh],imports:[Go]}));let t=e;return t})();export{q as a,ee as b,pe as c,r1 as d,M as e,sr as f,SI as g,bn as h,xI as i,pt as j,JI as k,A as l,Te as m,k as n,I as o,td as p,nd as q,we as r,sV as s,ie as t,It as u,aV as v,Gb as w,ua as x,f as y,E as z,Ae as A,jo as B,on as C,Da as D,jr as E,Lr as F,Ud as G,Dv as H,Ie as I,Vo as J,nS as K,vV as L,oS as M,bv as N,iS as O,sS as P,yV as Q,aS as R,DV as S,kS as T,Lv as U,zo as V,Go as W,JS as X,nT as Y,cn as Z,iy as _,ay as $,MT as aa,xe as ba,Xy as ca,Ce as da,ex as ea,ox as fa,bH as ga,EH as ha,Bx as ia,$x as ja,MH as ka,Zx as la,Qx as ma,SH as na,Zf as oa,jH as pa,LH as qa,BH as ra,UH as sa,vA as ta,e$ as ua,t$ as va,n$ as wa,r$ as xa,o$ as ya,$D as za,i$ as Aa,s$ as Ba,XD as Ca,ih as Da,hn as Ea,IR as Fa,CR as Ga,bR as Ha,ER as Ia,xR as Ja,AR as Ka,OR as La,FR as Ma,jR as Na,LR as Oa,VR as Pa,BR as Qa,UR as Ra,HR as Sa,$R as Ta,zR as Ua,WR as Va,YR as Wa,QR as Xa,KR as Ya,XR as Za,eN as _a,tN as $a,iN as ab,aN as bb,cN as cb,lN as db,hN as eb,pN as fb,CN as gb,SN as hb,AN as ib,RN as jb,FN as kb,jN as lb,LN as mb,UN as nb,$N as ob,GN as pb,lh as qb,qN as rb,ZN as sb,KN as tb,eO as ub,Oc as vb,tO as wb,sO as xb,Xz as yb,Jz as zb,uO as Ab,e3 as Bb,n3 as Cb};
