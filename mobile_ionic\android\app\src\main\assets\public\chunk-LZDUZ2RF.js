import{b as U}from"./chunk-KAKQC7EG.js";import{a as M}from"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{$a as D,B as g,C as s,Cb as R,D as C,E as r,F as a,G as p,H as P,I as u,J as m,L as d,M as b,Ma as z,N as O,Na as $,T as N,U as w,V as T,Va as F,W as I,_ as S,_a as B,da as E,na as A,p as h,pb as L,q as x,tb as V,ub as j,w as y,x as o,y as v,z as k}from"./chunk-6UWMO7JM.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as _}from"./chunk-LNJ3S2LQ.js";function q(i,l){if(i&1&&(r(0,"span",17),d(1),a()),i&2){let n=m();o(),b(n.unreadCount)}}function H(i,l){if(i&1){let n=P();r(0,"div",18)(1,"span",19),d(2,"Earlier"),a(),r(3,"button",20),u("click",function(){h(n);let e=m();return x(e.seeAllNotifications())}),d(4,"See all"),a()()}}function W(i,l){i&1&&(r(0,"div",21),p(1,"ion-icon",22),r(2,"h3"),d(3,"No notifications yet"),a(),r(4,"p"),d(5,"When you receive notifications, they'll appear here."),a()())}function Y(i,l){if(i&1&&(r(0,"span",36),d(1),a()),i&2){let n=m().$implicit;o(),O(" ",n.reactions," Reactions ")}}function G(i,l){i&1&&p(0,"div",37)}function J(i,l){if(i&1){let n=P();r(0,"div",23),u("click",function(){let e=h(n).$implicit,c=m();return x(c.onNotificationClick(e))}),r(1,"div",24),p(2,"img",25),r(3,"div",26),p(4,"ion-icon",27),a()(),r(5,"div",28)(6,"div",29)(7,"span",30),d(8),a(),r(9,"span",31),d(10),a()(),r(11,"div",32)(12,"span",33),d(13),a(),g(14,Y,2,1,"span",34),a()(),g(15,G,1,0,"div",35),a()}if(i&2){let n=l.$implicit,t=m();C("unread",!n.read),o(2),s("src",t.getNotificationIcon(n),y)("alt",n.type),o(),s("ngClass",t.getIconBadgeClass(n)),o(),s("name",t.getBadgeIcon(n)),o(4),b(t.getNotificationTitle(n)),o(2),b(t.getNotificationDescription(n)),o(3),b(t.getTimeAgo(n.created_at)),o(),s("ngIf",n.reactions),o(),s("ngIf",!n.read)}}function K(i,l){i&1&&p(0,"ion-spinner",42)}function Q(i,l){i&1&&(r(0,"span"),d(1,"Load More"),a())}function X(i,l){if(i&1){let n=P();r(0,"div",38)(1,"ion-button",39),u("click",function(){h(n);let e=m();return x(e.loadMoreNotifications())}),g(2,K,1,0,"ion-spinner",40)(3,Q,2,0,"span",41),a()()}if(i&2){let n=m();o(),s("disabled",n.isLoading),o(),s("ngIf",n.isLoading),o(),s("ngIf",!n.isLoading)}}var lt=(()=>{let l=class l{constructor(t,e,c){this.router=t,this.http=e,this.fcmService=c,this.notifications=[],this.filteredNotifications=[],this.activeTab="all",this.unreadCount=0,this.isLoading=!1,this.hasMoreNotifications=!1,this.currentPage=1,this.notificationSubscription=null}ngOnInit(){this.loadNotifications(),this.subscribeToNewNotifications()}ngOnDestroy(){this.notificationSubscription&&this.notificationSubscription.unsubscribe()}loadNotifications(){return _(this,null,function*(){this.isLoading=!0;try{let t=yield this.http.get(`${M.apiUrl}/notifications?page=${this.currentPage}`).toPromise();t&&(this.currentPage===1?this.notifications=t.notifications:this.notifications.push(...t.notifications),this.unreadCount=t.unread_count,this.hasMoreNotifications=t.has_more,this.filterNotifications())}catch(t){console.error("Error loading notifications:",t)}finally{this.isLoading=!1}})}subscribeToNewNotifications(){this.notificationSubscription=this.fcmService.notifications$.subscribe(t=>{let e={id:Date.now(),type:this.mapFCMTypeToAppType(t.category),title:t.title,message:t.body,data:t,read:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};this.notifications.unshift(e),this.unreadCount++,this.filterNotifications()})}mapFCMTypeToAppType(t){switch(t==null?void 0:t.toLowerCase()){case"evacuation":case"evacuation_center":return"evacuation_center_added";case"emergency":case"earthquake":case"typhoon":case"flood":return"emergency_alert";case"system":return"system_update";default:return"general"}}setActiveTab(t){this.activeTab=t,this.filterNotifications()}filterNotifications(){this.activeTab==="unread"?this.filteredNotifications=this.notifications.filter(t=>!t.read):this.filteredNotifications=this.notifications}onNotificationClick(t){return _(this,null,function*(){switch(t.read||(yield this.markAsRead(t)),t.type){case"evacuation_center_added":this.router.navigate(["/tabs/map"],{queryParams:{disasterType:"all",showNewCenters:!0}});break;case"emergency_alert":let e=this.extractDisasterType(t);this.router.navigate(["/tabs/map"],{queryParams:{disasterType:e}});break;default:break}})}extractDisasterType(t){let e=t.message.toLowerCase();return e.includes("earthquake")?"Earthquake":e.includes("typhoon")?"Typhoon":e.includes("flood")?"Flood":"all"}markAsRead(t){return _(this,null,function*(){try{yield this.http.put(`${M.apiUrl}/notifications/${t.id}/read`,{}).toPromise(),t.read=!0,this.unreadCount=Math.max(0,this.unreadCount-1),this.filterNotifications()}catch(e){console.error("Error marking notification as read:",e)}})}markAllAsRead(){return _(this,null,function*(){try{yield this.http.put(`${M.apiUrl}/notifications/mark-all-read`,{}).toPromise(),this.notifications.forEach(t=>t.read=!0),this.unreadCount=0,this.filterNotifications()}catch(t){console.error("Error marking all notifications as read:",t)}})}loadMoreNotifications(){!this.isLoading&&this.hasMoreNotifications&&(this.currentPage++,this.loadNotifications())}seeAllNotifications(){this.setActiveTab("all")}goBack(){this.router.navigate(["/tabs/home"])}trackByNotificationId(t,e){return e.id}getNotificationIcon(t){switch(t.type){case"evacuation_center_added":return"assets/evacuation-center-icon.png";case"emergency_alert":return"assets/emergency-icon.png";case"system_update":return"assets/system-icon.png";default:return"assets/alerto_icon.png"}}getBadgeIcon(t){switch(t.type){case"evacuation_center_added":return"add-circle";case"emergency_alert":return"warning";case"system_update":return"settings";default:return"notifications"}}getIconBadgeClass(t){switch(t.type){case"evacuation_center_added":return"badge-success";case"emergency_alert":return"badge-danger";case"system_update":return"badge-info";default:return"badge-primary"}}getNotificationTitle(t){switch(t.type){case"evacuation_center_added":return"New evacuation center added.";case"emergency_alert":return t.title;default:return t.title}}getNotificationDescription(t){return t.message}getTimeAgo(t){let e=new Date(t),f=Math.floor((new Date().getTime()-e.getTime())/1e3);return f<60?`${f}s`:f<3600?`${Math.floor(f/60)}m`:f<86400?`${Math.floor(f/3600)}h`:f<604800?`${Math.floor(f/86400)}d`:`${Math.floor(f/604800)}w`}};l.\u0275fac=function(e){return new(e||l)(v(E),v(S),v(U))},l.\u0275cmp=k({type:l,selectors:[["app-notifications"]],decls:23,vars:13,consts:[[3,"translucent"],["slot","start"],[3,"click"],["name","chevron-back-outline"],["slot","end"],[3,"click","disabled"],["name","checkmark-done-outline"],[3,"fullscreen"],[1,"notification-header"],[1,"notification-tabs"],[1,"tab-button",3,"click"],["class","unread-badge",4,"ngIf"],["class","section-header",4,"ngIf"],[1,"notifications-container"],["class","no-notifications",4,"ngIf"],["class","notification-item",3,"unread","click",4,"ngFor","ngForOf","ngForTrackBy"],["class","load-more-container",4,"ngIf"],[1,"unread-badge"],[1,"section-header"],[1,"section-title"],[1,"see-all-btn",3,"click"],[1,"no-notifications"],["name","notifications-outline",1,"no-notifications-icon"],[1,"notification-item",3,"click"],[1,"notification-icon"],[1,"icon-image",3,"src","alt"],[1,"icon-badge",3,"ngClass"],[3,"name"],[1,"notification-content"],[1,"notification-text"],[1,"notification-title"],[1,"notification-description"],[1,"notification-meta"],[1,"notification-time"],["class","notification-reactions",4,"ngIf"],["class","unread-indicator",4,"ngIf"],[1,"notification-reactions"],[1,"unread-indicator"],[1,"load-more-container"],["fill","clear",3,"click","disabled"],["name","crescent",4,"ngIf"],[4,"ngIf"],["name","crescent"]],template:function(e,c){e&1&&(r(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),u("click",function(){return c.goBack()}),p(4,"ion-icon",3),a()(),r(5,"ion-title"),d(6,"Notifications"),a(),r(7,"ion-buttons",4)(8,"ion-button",5),u("click",function(){return c.markAllAsRead()}),p(9,"ion-icon",6),a()()()(),r(10,"ion-content",7)(11,"div",8)(12,"div",9)(13,"button",10),u("click",function(){return c.setActiveTab("all")}),d(14," All "),a(),r(15,"button",10),u("click",function(){return c.setActiveTab("unread")}),d(16," Unread "),g(17,q,2,1,"span",11),a()(),g(18,H,5,0,"div",12),a(),r(19,"div",13),g(20,W,6,0,"div",14)(21,J,16,11,"div",15),a(),g(22,X,4,3,"div",16),a()),e&2&&(s("translucent",!0),o(8),s("disabled",c.unreadCount===0),o(2),s("fullscreen",!0),o(3),C("active",c.activeTab==="all"),o(2),C("active",c.activeTab==="unread"),o(2),s("ngIf",c.unreadCount>0),o(),s("ngIf",c.filteredNotifications.length>0),o(2),s("ngIf",c.filteredNotifications.length===0),o(),s("ngForOf",c.filteredNotifications)("ngForTrackBy",c.trackByNotificationId),o(),s("ngIf",c.hasMoreNotifications))},dependencies:[R,z,$,F,B,D,L,V,j,I,N,w,T,A],styles:['@charset "UTF-8";ion-content[_ngcontent-%COMP%]{--background: #f0f2f5}.notification-header[_ngcontent-%COMP%]{background:#fff;padding:16px;border-bottom:1px solid #e4e6ea;position:sticky;top:0;z-index:10}.notification-tabs[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:16px}.tab-button[_ngcontent-%COMP%]{background:none;border:none;font-size:16px;font-weight:600;color:#65676b;padding:8px 12px;border-radius:20px;cursor:pointer;transition:all .2s ease;position:relative}.tab-button.active[_ngcontent-%COMP%]{color:#1877f2;background:#e7f3ff}.tab-button[_ngcontent-%COMP%]:hover{background:#f2f3f4}.unread-badge[_ngcontent-%COMP%]{background:#e41e3f;color:#fff;font-size:12px;font-weight:600;padding:2px 6px;border-radius:10px;margin-left:6px;min-width:18px;text-align:center}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.section-title[_ngcontent-%COMP%]{font-size:17px;font-weight:600;color:#050505}.see-all-btn[_ngcontent-%COMP%]{background:none;border:none;color:#1877f2;font-size:15px;font-weight:500;cursor:pointer;padding:4px 8px;border-radius:6px}.see-all-btn[_ngcontent-%COMP%]:hover{background:#f2f3f4}.notifications-container[_ngcontent-%COMP%]{padding:0}.no-notifications[_ngcontent-%COMP%]{text-align:center;padding:60px 20px;color:#65676b}.no-notifications[_ngcontent-%COMP%]   .no-notifications-icon[_ngcontent-%COMP%]{font-size:64px;color:#bcc0c4;margin-bottom:16px}.no-notifications[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;margin:0 0 8px;color:#050505}.no-notifications[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:15px;margin:0;line-height:1.4}.notification-item[_ngcontent-%COMP%]{background:#fff;padding:12px 16px;border-bottom:1px solid #e4e6ea;display:flex;align-items:flex-start;gap:12px;cursor:pointer;transition:background-color .2s ease;position:relative}.notification-item[_ngcontent-%COMP%]:hover{background:#f7f8fa}.notification-item.unread[_ngcontent-%COMP%]{background:#f0f8ff}.notification-item.unread[_ngcontent-%COMP%]:hover{background:#e7f3ff}.notification-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.notification-icon[_ngcontent-%COMP%]{position:relative;flex-shrink:0}.icon-image[_ngcontent-%COMP%]{width:56px;height:56px;border-radius:50%;object-fit:cover;border:2px solid #e4e6ea}.icon-badge[_ngcontent-%COMP%]{position:absolute;bottom:-2px;right:-2px;width:24px;height:24px;border-radius:50%;display:flex;align-items:center;justify-content:center;border:2px solid white;font-size:12px}.icon-badge.badge-success[_ngcontent-%COMP%]{background:#42b883;color:#fff}.icon-badge.badge-danger[_ngcontent-%COMP%]{background:#e41e3f;color:#fff}.icon-badge.badge-info[_ngcontent-%COMP%]{background:#1877f2;color:#fff}.icon-badge.badge-primary[_ngcontent-%COMP%]{background:#03b2dd;color:#fff}.icon-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.notification-content[_ngcontent-%COMP%]{flex:1;min-width:0}.notification-text[_ngcontent-%COMP%]{margin-bottom:4px;line-height:1.3}.notification-title[_ngcontent-%COMP%]{color:#050505;font-size:15px;font-weight:400;display:block;margin-bottom:2px}.notification-description[_ngcontent-%COMP%]{color:#65676b;font-size:13px;display:block;line-height:1.4}.notification-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:13px;color:#65676b}.notification-time[_ngcontent-%COMP%]{font-weight:500}.notification-reactions[_ngcontent-%COMP%]:before{content:"\\2022";margin-right:8px}.unread-indicator[_ngcontent-%COMP%]{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:8px;height:8px;background:#1877f2;border-radius:50%}.load-more-container[_ngcontent-%COMP%]{padding:20px;text-align:center;background:#fff;border-top:1px solid #e4e6ea}.load-more-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: #1877f2;font-weight:600}@media (prefers-color-scheme: dark){ion-content[_ngcontent-%COMP%]{--background: #18191a}.notification-header[_ngcontent-%COMP%]{background:#242526;border-bottom-color:#3a3b3c}.tab-button[_ngcontent-%COMP%]{color:#b0b3b8}.tab-button.active[_ngcontent-%COMP%]{color:#2d88ff;background:#263951}.tab-button[_ngcontent-%COMP%]:hover{background:#3a3b3c}.section-title[_ngcontent-%COMP%]{color:#e4e6ea}.see-all-btn[_ngcontent-%COMP%]{color:#2d88ff}.see-all-btn[_ngcontent-%COMP%]:hover{background:#3a3b3c}.notification-item[_ngcontent-%COMP%]{background:#242526;border-bottom-color:#3a3b3c}.notification-item[_ngcontent-%COMP%]:hover{background:#3a3b3c}.notification-item.unread[_ngcontent-%COMP%]{background:#263951}.notification-item.unread[_ngcontent-%COMP%]:hover{background:#2d4373}.notification-title[_ngcontent-%COMP%]{color:#e4e6ea}.notification-description[_ngcontent-%COMP%], .notification-meta[_ngcontent-%COMP%], .no-notifications[_ngcontent-%COMP%]{color:#b0b3b8}.no-notifications[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#e4e6ea}.load-more-container[_ngcontent-%COMP%]{background:#242526;border-top-color:#3a3b3c}}']});let i=l;return i})();export{lt as NotificationsPage};
