import{a as W}from"./chunk-XUHFMYON.js";import{a as G,b as Y}from"./chunk-BNNKLWXU.js";import{a as V}from"./chunk-KGX7B5OW.js";import{a as B}from"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{$ as T,$a as S,A as b,Ab as F,Cb as z,D as k,Db as Q,F as g,G as f,H as C,J as A,M as w,Na as R,O as L,Oa as q,Pa as _,Qa as I,Wa as $,X as x,ab as U,ca as O,ea as N,g as P,p as u,ub as D,vb as H,y as M,zb as K}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{a as y,b as E,f as j,h as l}from"./chunk-LNJ3S2LQ.js";var i=j(Y());var ut=(()=>{let p=class p{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.loadingCtrl=u(F),this.toastCtrl=u(z),this.alertCtrl=u(K),this.http=u(T),this.router=u(N),this.route=u(O),this.mapboxRouting=u(W),this.offlineStorage=u(V)}ngOnInit(){console.log("\u{1F7E0} EARTHQUAKE MAP: Component initialized..."),this.route.queryParams.subscribe(e=>{e.newCenterId&&(this.newCenterId=e.newCenterId,this.highlightCenter=e.highlightCenter==="true",this.centerLat=e.centerLat?parseFloat(e.centerLat):null,this.centerLng=e.centerLng?parseFloat(e.centerLng):null,console.log("\u{1F7E0} EARTHQUAKE MAP: New center to highlight:",this.newCenterId))})}ngAfterViewInit(){return l(this,null,function*(){console.log("\u{1F7E0} EARTHQUAKE MAP: View initialized, loading map..."),setTimeout(()=>l(this,null,function*(){yield this.loadEarthquakeMap()}),100)})}loadEarthquakeMap(){return l(this,null,function*(){let e=yield this.loadingCtrl.create({message:"Loading earthquake evacuation centers...",spinner:"crescent"});yield e.present();try{let n=yield G.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),t=n.coords.latitude,o=n.coords.longitude;this.userLocation={lat:t,lng:o},console.log(`\u{1F7E0} EARTHQUAKE MAP: User location [${t}, ${o}]`),this.initializeMap(t,o),yield this.loadEarthquakeCenters(t,o),yield e.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F7E0} Showing ${this.evacuationCenters.length} earthquake evacuation centers`,duration:3e3,color:"warning",position:"top"})).present()}catch(n){yield e.dismiss(),console.error("\u{1F7E0} EARTHQUAKE MAP: Error loading map",n),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadEarthquakeMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(e,n){if(console.log(`\u{1F7E0} EARTHQUAKE MAP: Initializing map at [${e}, ${n}]`),!document.getElementById("earthquake-map"))throw console.error("\u{1F7E0} EARTHQUAKE MAP: Container #earthquake-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=i.map("earthquake-map").setView([e,n],13),i.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=i.marker([e,n],{icon:i.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadEarthquakeCenters(e,n){return l(this,null,function*(){try{console.log("\u{1F7E0} EARTHQUAKE MAP: Fetching earthquake centers...");let t=[];if(this.offlineStorage.isOfflineMode()||!navigator.onLine){if(console.log("\u{1F504} Loading earthquake centers from offline storage"),t=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} OFFLINE DATA:",t),t.length===0){console.warn("\u26A0\uFE0F No cached evacuation centers found"),yield(yield this.alertCtrl.create({header:"No Offline Data",message:"No offline evacuation data available. Please sync data when online.",buttons:["OK"]})).present();return}}else try{t=yield P(this.http.get(`${B.apiUrl}/evacuation-centers`)),console.log("\u{1F7E0} EARTHQUAKE MAP: Total centers received from API:",(t==null?void 0:t.length)||0)}catch(o){if(console.error("\u274C API failed, falling back to offline data:",o),t=yield this.offlineStorage.getEvacuationCenters(),t.length===0){yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server and no offline data available. Please check your connection or sync data when online.",buttons:["OK"]})).present();return}}if(this.evacuationCenters=t.filter(o=>o.disaster_type==="Earthquake"),console.log(`\u{1F7E0} EARTHQUAKE MAP: Filtered to ${this.evacuationCenters.length} earthquake centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Earthquake Centers",message:"No earthquake evacuation centers found in the data.",buttons:["OK"]})).present();return}yield this.addMarkersAndRoutes(e,n)}catch(t){console.error("\u{1F7E0} EARTHQUAKE MAP: Error loading centers",t);try{console.log("\u{1F504} Last resort: trying offline storage...");let a=yield this.offlineStorage.getEvacuationCenters();if(this.evacuationCenters=a.filter(r=>r.disaster_type==="Earthquake"),this.evacuationCenters.length>0){console.log(`\u{1F7E0} Loaded ${this.evacuationCenters.length} earthquake centers from offline storage`),yield this.addMarkersAndRoutes(e,n);return}}catch(a){console.error("\u274C Offline storage also failed:",a)}yield(yield this.toastCtrl.create({message:"Error loading earthquake centers. Please check your connection or sync offline data.",duration:4e3,color:"danger"})).present()}})}addMarkersAndRoutes(e,n){return l(this,null,function*(){if(this.evacuationCenters.forEach(t=>{let o=Number(t.latitude),a=Number(t.longitude);if(!isNaN(o)&&!isNaN(a)){let r=i.marker([o,a],{icon:i.icon({iconUrl:"assets/forEarthquake.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),s=this.calculateDistance(e,n,o,a);r.on("click",()=>{this.showTransportationOptions(t)});let c=this.newCenterId&&t.id.toString()===this.newCenterId;r.bindPopup(`
          <div class="evacuation-popup">
            <h3>\u{1F7E0} ${t.name} ${c?"\u2B50 NEW!":""}</h3>
            <p><strong>Type:</strong> Earthquake Center</p>
            <p><strong>Distance:</strong> ${(s/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${t.capacity||"N/A"}</p>
            <p><em>Click marker for route options</em></p>
            ${c?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
          </div>
        `),c&&(r.openPopup(),this.map.setView([o,a],15),this.toastCtrl.create({message:`\u{1F195} New earthquake evacuation center: ${t.name}`,duration:5e3,color:"warning",position:"top"}).then(h=>h.present())),r.addTo(this.map),console.log(`\u{1F7E0} Added earthquake marker: ${t.name}`)}}),yield this.routeToTwoNearestCenters(),this.evacuationCenters.length>0){let t=i.latLngBounds([]);t.extend([e,n]),this.evacuationCenters.forEach(o=>{t.extend([Number(o.latitude),Number(o.longitude)])}),this.map.fitBounds(t,{padding:[50,50]})}})}calculateDistance(e,n,t,o){let r=e*Math.PI/180,s=t*Math.PI/180,c=(t-e)*Math.PI/180,h=(o-n)*Math.PI/180,m=Math.sin(c/2)*Math.sin(c/2)+Math.cos(r)*Math.cos(s)*Math.sin(h/2)*Math.sin(h/2);return 6371e3*(2*Math.atan2(Math.sqrt(m),Math.sqrt(1-m)))}routeToTwoNearestCenters(){return l(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F7E0} EARTHQUAKE MAP: No user location or evacuation centers available");return}try{console.log("\u{1F7E0} EARTHQUAKE MAP: Finding 2 nearest earthquake centers...");let e=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(e.length===0)return;this.clearRoutes(),yield this.calculateRoutes(e)}catch(e){console.error("\u{1F7E0} EARTHQUAKE MAP: Error calculating routes",e)}})}getTwoNearestCenters(e,n){return this.evacuationCenters.map(o=>E(y({},o),{distance:this.calculateDistance(e,n,Number(o.latitude),Number(o.longitude))})).sort((o,a)=>o.distance-a.distance).slice(0,2)}calculateRoutes(e){return l(this,null,function*(){if(this.userLocation){this.routeLayer=i.layerGroup().addTo(this.map);for(let n=0;n<e.length;n++){let t=e[n],o=Number(t.latitude),a=Number(t.longitude);if(!isNaN(o)&&!isNaN(a))try{let r=this.mapboxRouting.convertTravelModeToProfile("walking"),s=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,a,o,r,{geometries:"geojson",overview:"simplified",steps:!1});if(s&&s.routes&&s.routes.length>0){let c=s.routes[0];i.polyline(c.geometry.coordinates.map(d=>[d[1],d[0]]),{color:"#ff9500",weight:4,opacity:.8,dashArray:n===0?void 0:"10, 10"}).addTo(this.routeLayer),console.log(`\u{1F7E0} Route ${n+1}: ${(c.distance/1e3).toFixed(2)}km, ${(c.duration/60).toFixed(0)}min`)}}catch(r){console.error(`\u{1F7E0} Error calculating route to center ${n+1}:`,r)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(e=>{this.map.removeLayer(e)}),this.nearestMarkers=[]}showTransportationOptions(e){return l(this,null,function*(){yield(yield this.alertCtrl.create({header:`Route to ${e.name}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToCenter(e,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToCenter(e,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToCenter(e,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToCenter(e,n){return l(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let t=Number(e.latitude),o=Number(e.longitude);if(!isNaN(t)&&!isNaN(o)){let a=this.mapboxRouting.convertTravelModeToProfile(n),r=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,o,t,a,{geometries:"geojson",overview:"full",steps:!1});if(r&&r.routes&&r.routes.length>0){let s=r.routes[0],c="#ff9500";this.routeLayer=i.layerGroup().addTo(this.map);let h=i.polyline(s.geometry.coordinates.map(d=>[d[1],d[0]]),{color:c,weight:5,opacity:.8});h.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`\u{1F7E0} Route: ${(s.distance/1e3).toFixed(2)}km, ${(s.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"warning"})).present(),this.map.fitBounds(h.getBounds(),{padding:[50,50]})}}}catch(t){console.error("\u{1F7E0} Error routing to center:",t),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}goBack(){this.router.navigate(["/tabs/home"])}ionViewWillLeave(){this.clearRoutes(),this.map&&this.map.remove()}};p.\u0275fac=function(n){return new(n||p)},p.\u0275cmp=b({type:p,selectors:[["app-earthquake-map"]],decls:18,vars:3,consts:[[3,"translucent"],["color","warning"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[3,"fullscreen"],["id","earthquake-map",2,"height","100%","width","100%"],[1,"floating-info"],[1,"info-row"],["name","warning","color","warning"],[1,"info-text"]],template:function(n,t){n&1&&(g(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),A("click",function(){return t.goBack()}),C(4,"ion-icon",4),f()(),g(5,"ion-title"),w(6,"\u{1F7E0} Earthquake Evacuation Centers"),f()()(),g(7,"ion-content",5),C(8,"div",6),g(9,"div",7)(10,"ion-card")(11,"ion-card-content")(12,"div",8),C(13,"ion-icon",9),g(14,"span"),w(15),f()(),g(16,"div",10),w(17," Showing evacuation centers specifically for earthquake disasters "),f()()()()()),n&2&&(k("translucent",!0),M(7),k("fullscreen",!0),M(8),L("Earthquake Centers: ",t.evacuationCenters.length,""))},dependencies:[Q,R,q,_,I,$,S,U,D,H,x],styles:["#earthquake-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:250px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-warning);margin-bottom:4px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);line-height:1.3}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-warning);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-warning);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}"]});let v=p;return v})();export{ut as EarthquakeMapPage};
